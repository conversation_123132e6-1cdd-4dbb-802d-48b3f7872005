# Known Issues & Bugs

## Sidebar Context Provider Bug

**Issue Date:** [Current Date]

### Problem

Components using UI elements from `@/components/ui/sidebar.tsx` (like `SidebarMenu`, `SidebarMenuItem`, etc.) throw the error:

> "Error: useSidebar must be used within a SidebarProvider"

This occurs even when not directly using the `useSidebar` hook because these components internally depend on the sidebar context.

### Root Cause

- The sidebar UI components are tightly coupled with the `SidebarContext`
- Components like `SidebarMenu`, `SidebarMenuItem` internally use `useSidebar` hook
- These components must be wrapped in `<SidebarProvider>` to work, regardless of whether you're using sidebar functionality

### Solution Options

1. **Preferred:** Replace sidebar components with standard UI components when not needing sidebar functionality
2. **Alternative:** Wrap components in `<SidebarProvider>` if sidebar components must be used

### Example Fix

```tsx
// Before (causing error)
<SidebarMenu>
  <SidebarMenuItem>Content</SidebarMenuItem>
</SidebarMenu>

// After (fixed)
<DropdownMenu>
  <DropdownMenuItem>Content</DropdownMenuItem>
</DropdownMenu>
```

### Prevention

- Only use sidebar components when building actual sidebar interfaces
- Use regular UI components (Button, Dropdown, etc.) for standard navigation elements
- Check component dependencies before using UI components from the sidebar module

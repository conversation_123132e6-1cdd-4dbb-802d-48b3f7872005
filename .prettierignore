# dependencies
node_modules/
.pnp
.pnp.js

# testing
coverage/
.nyc_output/

# next.js
.next/
out/

# production
build/
dist/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# package manager
pnpm-lock.yaml
package-lock.json
yarn.lock

# PWA files
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/fallback-*.js

# Sentry
.sentryclirc

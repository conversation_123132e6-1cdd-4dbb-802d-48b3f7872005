{"name": "theportfolyo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "tsc --noEmit", "clean": "rm -rf .next out node_modules", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepare": "husky install", "preinstall": "npx only-allow pnpm", "check:all": "pnpm run lint && pnpm run format:check && pnpm run typecheck", "fix:all": "pnpm run lint:fix && pnpm run format"}, "dependencies": {"@ai-sdk/google": "^1.2.1", "@ai-sdk/mistral": "^1.2.0", "@ai-sdk/openai": "^1.3.0", "@emailjs/browser": "^4.4.1", "@google/generative-ai": "^0.21.0", "@headlessui/react": "^2.1.2", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.5", "@sentry/nextjs": "^8", "@tanstack/react-query": "^5.61.5", "@tanstack/react-query-devtools": "^5.62.16", "@tanstack/react-table": "^8.20.5", "@uploadthing/react": "^7.1.2", "ai": "^4.2.0", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "cookies-next": "^5.0.2", "date-fns": "^4.1.0", "embla-carousel-react": "^8.2.0", "framer-motion": "^11.3.28", "hamburger-react": "^2.5.1", "input-otp": "^1.2.4", "lucide-react": "^0.428.0", "mini-svg-data-uri": "^1.4.4", "motion": "^11.15.0", "next": "14.2.6", "next-themes": "^0.4.3", "next-turnstile": "^1.0.0", "nextjs-toploader": "^3.7.15", "openai": "^4.80.0", "posthog-js": "^1.193.0", "react": "^18", "react-confetti": "^6.2.2", "react-day-picker": "8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.0", "react-layout-masonry": "^1.2.0", "react-parallax-tilt": "^1.7.237", "react-quill": "^2.0.0", "react-use": "^17.5.1", "recharts": "^2.13.2", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.4.0", "use-immer": "^0.11.0", "usehooks-ts": "^3.1.0", "vaul": "^1.1.2", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^18.0.0", "@commitlint/config-conventional": "^18.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@types/jest": "^29.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.6", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-simple-import-sort": "^12.0.0", "husky": "^8.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "lint-staged": "^15.0.0", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "typescript": "^5"}}
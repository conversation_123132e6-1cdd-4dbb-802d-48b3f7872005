{"root": true, "extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:jest/recommended", "plugin:prettier/recommended"], "plugins": ["@typescript-eslint", "simple-import-sort", "import"], "rules": {"no-console": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "off", "simple-import-sort/imports": "error", "simple-import-sort/exports": "error", "import/first": "error", "import/newline-after-import": "error", "import/no-duplicates": "error", "@next/next/no-img-element": "off"}, "ignorePatterns": ["node_modules/**/*", ".next/**/*", "dist/**/*", "build/**/*", "tailwind.config.ts", "postcss.config.js"]}
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build
/dist

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local
.env

# IDE
.vscode/*
!.vscode/settings.json
!.vscode/extensions.json

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# other
index.txt
# Sentry Config File
.env.sentry-build-plugin

# Linting and Code Quality Documentation

This document outlines the code quality tools and configurations used in this project.

## Table of Contents

- [ESLint Configuration](#eslint-configuration)
- [Prettier Configuration](#prettier-configuration)
- [Commit Conventions](#commit-conventions)
- [Git Hooks (<PERSON><PERSON>)](#git-hooks)
- [Ignore Patterns](#ignore-patterns)
- [VS Code Integration](#vs-code-integration)

## ESLint Configuration

ESLint is configured to enforce TypeScript and React best practices.

### Key Extensions

- `next/core-web-vitals`: Next.js recommended rules
- `@typescript-eslint/recommended`: TypeScript-specific rules
- `jest/recommended`: Testing best practices
- `prettier/recommended`: Prettier integration

### Custom Rules

```json
{
  "no-console": "off",
  "no-unused-vars": "off",
  "@typescript-eslint/no-unused-vars": "error",
  "@typescript-eslint/no-explicit-any": "off",
  "simple-import-sort/imports": "error",
  "simple-import-sort/exports": "error",
  "import/first": "error",
  "import/newline-after-import": "error",
  "import/no-duplicates": "error"
}
```

### Running ESLint

```bash
# Check for issues
pnpm run lint

# Fix automatically fixable issues
pnpm run lint:fix
```

## Prettier Configuration

Prettier ensures consistent code formatting across the project.

### Key Settings

```yaml
trailingComma: all # Add trailing commas wherever possible
tabWidth: 2 # 2 spaces indentation
semi: false # No semicolons
singleQuote: false # Use double quotes for strings
jsxSingleQuote: true # Use single quotes in JSX
printWidth: 80 # Line length
bracketSpacing: true # Spaces between brackets
bracketSameLine: false # Brackets on new lines
arrowParens: "always" # Always add parens to arrow functions
endOfLine: "lf" # Linux-style line endings
```

### Running Prettier

```bash
# Format files
pnpm run format

# Check formatting
pnpm run format:check
```

## Commit Conventions

We use conventional commits to maintain a clear git history.

### Commit Types

- `feat`: New features
- `fix`: Bug fixes
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `perf`: Performance improvements
- `test`: Testing changes
- `chore`: Maintenance tasks
- `revert`: Reverting changes
- `ci`: CI/CD changes
- `build`: Build system changes

### Example Commits

```bash
git commit -m "feat: add user authentication"
git commit -m "fix: resolve memory leak in dashboard"
git commit -m "docs: update API documentation"
```

## Git Hooks

Husky is used to enforce code quality at git hooks.

### Pre-commit Hook

Runs before each commit:

- Lints staged files
- Runs type checking
- Formats code

### Commit Message Hook

Validates commit messages against conventional commit format.

### Running All Checks

```bash
pnpm run check:all  # Runs lint, format:check, and typecheck
pnpm run fix:all    # Runs lint:fix and format
```

## Ignore Patterns

### ESLint Ignores

```json
[
  "node_modules/**/*",
  ".next/**/*",
  "dist/**/*",
  "build/**/*",
  "tailwind.config.ts",
  "postcss.config.js"
]
```

### Prettier Ignores

- Dependencies (`node_modules/`)
- Build outputs (`.next/`, `build/`, `dist/`)
- Lock files (`pnpm-lock.yaml`, `package-lock.json`)
- Environment files (`.env*`)
- TypeScript declaration files (`*.tsbuildinfo`)

### Git Ignores

- Dependencies and package managers
- Build outputs
- Environment files
- IDE settings
- Debug logs
- Testing coverage

## VS Code Integration

For the best development experience, install these VS Code extensions:

- ESLint
- Prettier
- TypeScript and JavaScript Language Features

The project includes VS Code settings for:

- Format on save
- Organize imports on save
- Default formatters for different file types
- Consistent line endings and whitespace handling

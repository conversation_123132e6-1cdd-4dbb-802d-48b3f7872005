export interface TemplateFont {
  name: string // Name of the font
  url: string // URL to the font file
}

export interface TemplateColorPalette {
  primary_color: string // Primary color code
  secondary_color: string // Secondary color code
  accent_color: string // Accent color code
}

export interface TemplateSection {
  section_name: string // Name of the section (e.g., "hero", "about")
  is_enabled: boolean // Whether the section is active
}

export interface DefaultMeta {
  sections: TemplateSection[] // List of sections and their state
  favicon: string // URL to the favicon
  logo: string // URL to the logo
  keywords: string // SEO keywords
  fonts: {
    heading_font: TemplateFont // Font configuration for headings
    body_font: TemplateFont // Font configuration for body text
  }
  color_palette: TemplateColorPalette // Color palette configuration
}

export interface Template {
  _id: string // Unique identifier for the template
  name: string // Name of the template
  key: string // Unique key for the template
  description: string // Brief description of the template
  preview_image: string // URL for the preview image
  preview_url?: string // Optional URL for the live preview
  default_meta: DefaultMeta // Default meta information for the template
  is_pro: boolean // Indicates if the template is a premium template
  tags: string[] // List of tags for filtering (e.g., "portfolio", "eCommerce")
  is_enabled: boolean // Indicates if the template is enabled/available
  createdAt: Date // Timestamp for when the template was created
  updatedAt: Date // Timestamp for the last update to the template
}

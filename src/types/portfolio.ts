import { z } from "zod"

export interface Portfolio {
  _id?: string
  title: string
  description?: string
  images: string[]
  external_links: {
    live_url?: string
    repository_url?: string
  }
  order: number
  tags?: string[]
  categories?: string[] // Added based on schema
  is_enabled: boolean
  website: string // Reference to Website ID
  createdAt?: Date
  updatedAt?: Date
}

export const portfolioSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z
    .string()
    .max(900, "Description must be 900 characters or less")
    .optional(),
  images: z
    .array(z.string().url()) // Ensures images is an array of strings (URLs)
    .max(10, "You can only have up to 20 images"),
  external_links: z.object({
    live_url: z.string().url().optional().or(z.literal("")),
    repository_url: z.string().url().optional().or(z.literal("")),
  }),
  order: z
    .union([z.string(), z.number()])
    .transform((val) => {
      if (typeof val === "string") {
        const parsed = parseFloat(val)
        return isNaN(parsed) ? 0 : parsed
      }
      return val
    })
    .pipe(z.number().min(0, "Order must be a positive number or zero")),
  tags: z.array(z.string()),
  categories: z.array(z.string()),
  website: z.string().optional(),
  is_enabled: z.boolean(),
})

export type PortfolioFormData = Omit<
  Portfolio,
  "createdAt" | "updatedAt" | "website"
>

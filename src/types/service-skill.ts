import { z } from "zod"

export const serviceSkillSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  type: z.enum(["service", "skill", "language"]),
  icon: z.string().or(z.literal("")).optional(),
  percentage: z
    .union([z.string(), z.number()])
    .transform((val) => {
      if (typeof val === "string") {
        const parsed = parseFloat(val)
        return isNaN(parsed) ? 0 : parsed
      }
      return val
    })
    .pipe(z.number().min(0).max(100))
    .optional(),
  price: z
    .union([z.string(), z.number()])
    .transform((val) => {
      if (typeof val === "string") {
        const parsed = parseFloat(val)
        return isNaN(parsed) ? 1 : parsed
      }
      return val
    })
    .pipe(z.number().min(1))
    .optional(),
  is_enabled: z.boolean(),
})

export interface ServiceSkill {
  _id?: string
  website: string
  title: string
  description: string
  type: "service" | "skill" | "language"
  icon?: string
  percentage?: number | null // Only for skill type
  price?: number | null // Only for service type
  is_enabled: boolean
  createdAt?: Date
  updatedAt?: Date
}

export type ServiceSkillFormData = Omit<
  ServiceSkill,
  "createdAt" | "updatedAt" | "website"
>

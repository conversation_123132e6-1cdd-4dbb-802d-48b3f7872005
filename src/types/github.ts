// app/types.ts
export interface GitHubUser {
  login: string
  name: string
  bio: string
  public_repos: number
  followers: number
  following: number
  html_url: string
  avatar_url: string
  created_at: string
}

export interface GitHubRepository {
  name: string
  description: string
  html_url: string
  language: string
  stargazers_count: number
  forks_count: number
}

export interface GitHubStarredRepository {
  name: string
  html_url: string
}

export interface GitHubFollower {
  login: string
  html_url: string
}

export interface GitHubOrganization {
  login: string
  html_url: string
}

export interface GitHubEvent {
  type: string
  repo: {
    name: string
  }
  created_at: string
}

export interface GitHubData {
  user: GitHubUser
  repositories: GitHubRepository[]
  // starred: GitHubStarredRepository[];
  // followers: GitHubFollower[];
  // following: GitHubFollower[];
  // organizations: GitHubOrganization[];
  // events: GitHubEvent[];
}

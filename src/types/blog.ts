import { z } from "zod"

export const blogSchema = z.object({
  _id: z.string(),
  title: z
    .string()
    .min(1, "Title is required")
    .max(120, "Title must be 120 characters or less"),
  slug: z.string().optional(),
  content: z.string().min(1, "Content is required"),
  excerpt: z
    .string()
    .max(250, "Excerpt must be 250 characters or less")
    .optional(),
  // categories: z.array(z.string()),
  tags: z.array(z.string()),
  thumbnail: z.string().url("Invalid URL").or(z.literal("")).optional(),
  is_published: z.boolean(),
  published_at: z.date().optional(),
  reading_time: z
    .string()
    .regex(/^\d+\smin\sread$/, "Invalid reading time format")
    .or(z.literal(""))
    .optional(),
  featured: z.boolean(),
  meta_title: z
    .string()
    .max(60, "Meta title must be 60 characters or less")
    .optional(),
  meta_description: z
    .string()
    .max(160, "Meta description must be 160 characters or less")
    .optional(),
  last_edited: z.date(),
  is_enabled: z.boolean(),
  website: z.string().min(1, "Website is required"),
  createdAt: z.string(),
  updatedAt: z.string(),
})

export type Blog = z.infer<typeof blogSchema>

export type BlogFormData = Omit<
  Blog,
  "_id" | "createdAt" | "updatedAt" | "last_edited"
>

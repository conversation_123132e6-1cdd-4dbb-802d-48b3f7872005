import { z } from "zod"

const socialLinkSchema = z.object({
  platform: z.string().min(1, "Platform is required"),
  url: z.string().url("Must be a valid URL").or(z.literal("")),
  icon: z.string().optional(),
})

const addressSchema = z.object({
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  zip_code: z.string().optional(),
})

export const aboutSchema = z.object({
  first_name: z.string().min(2, "First name must be at least 2 characters"),
  middle_name: z.string().optional(),
  last_name: z.string().min(2, "Last name must be at least 2 characters"),
  title: z.string().min(2, "Title must be at least 2 characters"),
  sub_title: z.string().min(2, "Subtitle must be at least 2 characters"),
  bio: z.string().min(10, "Bio must be at least 10 characters"),
  quote: z.string().optional(),
  years_of_experience: z
    .number()
    .min(0, "Experience must be a positive number"),
  address: addressSchema,
  total_projects: z.number().min(0, "Total projects must be a positive number"),
  total_clients: z
    .number()
    .min(0, "Total clients must be a positive number")
    .optional(),
  phone_number: z
    .string()
    .min(10, "Phone number must be at least 10 characters"),
  contact_email: z.string().email("Invalid email address"),
  social_links: z.array(socialLinkSchema),
  avatar: z.string().url("Must be a valid URL").or(z.literal("")),
  // alternate_avatars: z.array(z.string().url("Must be a valid URL")),
})

// export interface SocialLink {
//   platform: string;
//   url: string;
//   icon?: string;
// }

// export interface Address {
//   street: string;
//   city: string;
//   state: string;
//   country: string;
//   zip_code: string;
// }

// export interface About {
//   first_name: string;
//   middle_name: string;
//   last_name: string;
//   title: string;
//   sub_title: string;
//   bio: string;
//   quote: string;
//   years_of_experience: number;
//   address: Address;
//   total_projects: number;
//   phone_number: string;
//   contact_email: string;
//   social_links: SocialLink[];
//   avatar: string;
//   alternate_avatars: string[];
// }

export type About = z.infer<typeof aboutSchema>
export type SocialLink = z.infer<typeof socialLinkSchema>
export type Address = z.infer<typeof addressSchema>

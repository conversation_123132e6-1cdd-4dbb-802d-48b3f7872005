import { z } from "zod"

export type HeroLayout =
  | "default"
  | "centered"
  | "minimal"
  | "gradient"
  | "image-focus"

export const heroFormSchema = z.object({
  title: z
    .string()
    .min(1)
    .max(100, { message: "Title must be between 1 and 100 characters" }),
  sub_title: z.string().max(150).optional(),
  description: z.string().max(300).optional(),
  main_image: z
    .string()
    .url({ message: "Invalid URL format" })
    .or(z.literal("")),
  background_images: z
    .array(z.string().url().or(z.literal("")))
    .max(10, "You can only have up to 20 images"),

  cta_button_primary: z.object({
    text: z.string().min(1).max(25, {
      message: "Primary button text must be between 1 and 50 characters",
    }),

    url: z
      .string()
      .max(100, { message: "URL must be less than 100 characters" }),
  }),
  cta_button_secondary: z
    .object({
      text: z
        .string()
        .max(250, {
          message: "Secondary button text must be less than 50 characters",
        })
        .optional(),
      url: z
        .string()
        .max(100, { message: "URL must be less than 100 characters" })
        .optional(),
    })
    .optional(),
})

export type HeroFormValues = z.infer<typeof heroFormSchema>

// Helper type for CTA buttons (optional, for better type reuse)
export interface CTAButton {
  text: string
  url: string
}

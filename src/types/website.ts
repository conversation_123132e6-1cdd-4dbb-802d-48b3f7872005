// Meta type for website configurations
export interface WebsiteMeta {
  sections?: {
    section_name?: string
    is_enabled?: boolean
  }[]
  favicon?: string // URL to the favicon
  logo?: string // URL to the logo
  keywords?: string // Comma-separated SEO keywords
  fonts?: {
    heading_font?: string // Font for headings
    body_font?: string // Font for body text
  }
  color_palette?: {
    primary_color?: string // Primary color of the website
    secondary_color?: string // Secondary color
    accent_color?: string // Accent color
  }
  default?: Record<string, any> // Other user-defined settings
}

// Main Website type
export interface Website {
  _id: string // Unique identifier for the website (mapped from _id)
  user: string // User ID who owns the website
  name: string // Unique name of the portfolio/site
  is_active: boolean // Whether the site is active
  is_published: boolean // Whether the site is publicly available
  is_deleted: boolean // Logical deletion of the site
  is_pro: boolean // Whether the site is a pro account
  meta: WebsiteMeta // Meta configuration for the site
  current_subscription?: string // Active subscription ID
  subscriptions?: string[] // History of subscription IDs
  hero?: string // Hero section ID
  about?: string // About section ID
  domains?: string[] // Linked domain IDs
  template?: string // Template ID
  createdAt: string // Creation timestamp
  updatedAt: string // Update timestamp
}

import type {
  <PERSON><PERSON>rrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form"
import * as z from "zod"

export const CreateWebsiteFormSchema = z.object({
  name: z
    .string()
    .min(3, { message: "Website name must be at least 3 characters long." })
    .max(50, { message: "Website name must be less than 50 characters." })
    .regex(/^[a-zA-Z0-9-]+$/, {
      message:
        "Website name can only contain letters, numbers, and hyphens (-).",
    })
    .refine((name) => !/^[-]/.test(name), {
      message: "Website name cannot start with a special character.",
    })
    .refine((name) => !/[-]$/.test(name), {
      message: "Website name cannot end with a special character.",
    })
    .refine((name) => !/--/.test(name), {
      message: "Website name cannot contain consecutive special characters.",
    }),
  template: z.string().min(1, "Please select a template"),
  plan: z.string().min(1, "Please select a plan"),
  githubUsername: z.string().optional(),
  pdfData: z.string().optional(),
})

export type CreateWebsiteFormData = z.infer<typeof CreateWebsiteFormSchema>

export const validationRules = [
  {
    id: "length",
    label: "Between 3 and 50 characters",
    check: (value: string) => value.length >= 3 && value.length <= 50,
  },
  {
    id: "characters",
    label: "Only letters, numbers, and hyphens",
    check: (value: string) => /^[a-zA-Z0-9-]*$/.test(value),
  },
  {
    id: "start",
    label: "Doesn't start with a hyphen",
    check: (value: string) => !/^[-]/.test(value),
  },
  {
    id: "end",
    label: "Doesn't end with a hyphen",
    check: (value: string) => !/[-]$/.test(value),
  },
  {
    id: "consecutive",
    label: "No consecutive hyphens",
    check: (value: string) => !/--/.test(value),
  },
]

export interface StepComponentProps {
  register: UseFormRegister<FormData>
  errors: FieldErrors<FormData>
  watch: UseFormWatch<FormData>
  setValue: UseFormSetValue<FormData>
  debouncedName?: string
}

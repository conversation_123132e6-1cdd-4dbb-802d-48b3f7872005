import { z } from "zod"

export interface Review {
  _id?: string
  client_name: string
  client_designation: string
  feedback: string
  rating: number // Rating value from 1 to 5
  company?: string
  client_image: string
  is_enabled: boolean
  createdAt?: Date
  updatedAt?: Date
}

export const reviewSchema = z.object({
  client_name: z.string().min(1, "Client name is required"),
  client_image: z.string().or(z.literal("")),
  client_designation: z.string().optional(),
  feedback: z
    .string()
    .min(10, "Feedback must be at least 10 characters")
    .max(1000, "Feedback must be 1000 characters or less"),
  company: z.string().optional(),
  rating: z
    .union([z.string(), z.number()])
    .transform((val) => {
      if (typeof val === "string") {
        const parsed = parseFloat(val)
        return isNaN(parsed) ? 5 : parsed
      }
      return val
    })
    .pipe(
      z.number().min(1, "Rating must be at least 1").max(5, "Max rating is 5"),
    ),
  is_enabled: z.boolean(),
})

export type ReviewFormData = Omit<Review, "created_by" | "date_created">

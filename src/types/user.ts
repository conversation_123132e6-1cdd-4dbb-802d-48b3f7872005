export interface OAuthProvider {
  provider: string
  provider_id: string
  linked_at: string // ISO string format
}

export interface User {
  _id: string
  username: string
  full_name: string
  email: string
  phone_number?: string // Optional field
  role: "user" | "admin" | "app-admin"
  status: string
  is_active: boolean
  refresh_token?: string // Optional field
  free_website_limit: number
  websites: string[] // Array of website IDs (string format)
  is_email_verified: boolean
  oauth_providers: OAuthProvider[]
  profile_picture?: string // Optional field
}

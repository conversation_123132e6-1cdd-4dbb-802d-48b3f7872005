import { z } from "zod"

import { FormConfig } from "./form"

export interface ImageType {
  _id?: string
  image_name: string
  image_type: "JPEG" | "PNG" | "GIF" | "WEBP" | "SVG" | "ICO"
  image_size: number
  title: string
  url: string
  key: string
  alt_text?: string
  is_favorite: boolean
  is_deleted: boolean
  is_private: boolean
  tags: string[]
  uploaded_by: string
  website: string
  uploaded_at: Date
}

export const imageSchema = z.object({
  image_name: z.string().min(1, "Image name is required"),
  image_type: z.enum(["JPEG", "PNG", "GIF", "WEBP", "SVG", "ICO"]),
  image_size: z.number().min(0, "File size must be a positive number"),
  title: z.string().min(1, "Title is required"),
  url: z.string().url("Invalid URL"),
  key: z.string().min(1, "Key is required"),
  alt_text: z.string().optional(),
  is_favorite: z.boolean(),
  is_deleted: z.boolean(),
  is_private: z.boolean(),
  tags: z.array(z.string()),
  uploaded_by: z.string(),
  website: z.string(),
  uploaded_at: z.date(),
})

export interface ImageFormProps {
  initialData?: Partial<ImageType>
  onSubmit: (data: Partial<ImageType>) => void
}

export type ImageFormData = Omit<
  ImageType,
  "_id" | "created_by" | "date_created"
>

export type ImageFormConfig = FormConfig<typeof imageSchema>

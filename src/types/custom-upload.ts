import * as z from "zod"

export const MAX_FILE_SIZE = 4 * 1024 * 1024 // 4MB
export const ACCEPTED_IMAGE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/gif",
  "image/webp",
  "image/svg+xml",
] as const

export const FileValidation = z.object({
  files: z
    .array(
      z
        .custom<File>()
        .refine((file) => file instanceof File, {
          message: "Must be a valid file",
        })
        .refine((file) => file.size <= MAX_FILE_SIZE, {
          message: `File size must be less than ${
            MAX_FILE_SIZE / (1024 * 1024)
          }MB`,
        })
        .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type as any), {
          message:
            "Only .jpg, .jpeg, .png, .gif, .webp, and .svg formats are supported",
        }),
    )
    .max(10, "Maximum 10 files allowed"),
})

export type FileValidationType = z.infer<typeof FileValidation>

export interface FileResponse {
  fileName: string
  fileSize: number
  key: string
  name: string
  s3Location: string
  size: number
  title: string
  type: string
  url: string
}

export interface UploadResult {
  files: FileResponse[]
  message: string
}

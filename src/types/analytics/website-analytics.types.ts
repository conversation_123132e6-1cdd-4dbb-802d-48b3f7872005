export type PerformanceMetrics = {
  avgFCP: number // Avg. First Contentful Paint
  avgLCP: number // Avg. Largest Contentful Paint
  avgFID: number // Avg. First Input Delay
  avgCLS: number // Avg. Cumulative Layout Shift
  avgTTFB: number // Avg. Time to First Byte
  avgLoadTime: number // Avg. total load time
}

export type CountryStats = {
  country: string
  count: number
}

export type DeviceStats = {
  device: string
  count: number
}

export type BrowserStats = {
  browser: string
  count: number
}

export type WebsiteAnalytics = {
  website: string // Assuming ObjectId is stored as a string on the frontend

  // Traffic stats
  totalPageViews: number
  uniqueVisitors: number
  totalSessions: number
  totalEvents?: number
  avgTimeOnSite?: number // Avg. time spent
  bounceRate?: number // % of single-page sessions

  // Performance Metrics
  performance?: PerformanceMetrics

  // Audience insights
  topCountries?: CountryStats[]
  topDevices?: DeviceStats[]
  topBrowsers?: BrowserStats[]

  createdAt: string // Date string
  updatedAt: string // Date string
}

export type EventCategory =
  | "click"
  | "form_submit"
  | "video_play"
  | "scroll"
  | "social_media"
  | "custom"

export type Event = {
  website: string // ObjectId stored as a string
  sessionId: string // Session ID
  eventName: string // Name of the event
  elementId?: string // Optional: Specific element interacted with
  category?: EventCategory // Event category
  label?: string // Additional descriptive information
  value?: number // Numeric metric (e.g., success rate)
  path?: string // URL path where event occurred
  metadata?: Record<string, any> // Flexible metadata object
  timestamp: string // ISO Date string

  createdAt: string // ISO Date string
  updatedAt: string // ISO Date string
}

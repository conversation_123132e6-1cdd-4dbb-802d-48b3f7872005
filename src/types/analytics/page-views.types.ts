export type PageInteraction = {
  timeOnPage?: number // Time spent on page (in seconds)
  scrollDepth?: number // Scroll percentage (0-100)
  exitPath?: string // Last page before exit
}

export type Pageview = {
  website: string // ObjectId stored as a string
  sessionId: string // Reference to the session

  path: string // URL visited
  referer?: string // Traffic source
  timestamp: string // ISO Date string

  interaction?: PageInteraction

  createdAt: string // ISO Date string
  updatedAt: string // ISO Date string
}

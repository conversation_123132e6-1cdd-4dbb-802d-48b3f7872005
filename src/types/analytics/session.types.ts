export type VisitorInfo = {
  ip?: string
  userAgent?: string
  browser?: string
  browserVersion?: string
  os?: string
  osVersion?: string
  device?: string
  deviceModel?: string
  isReturningVisitor?: boolean
  screenResolution?: string
  viewportSize?: string
  language?: string
}

export type GeoInfo = {
  country?: string
  region?: string
  city?: string
  latitude?: number
  longitude?: number
  timezone?: string
  isp?: string
}

export type UTMParams = {
  source?: string
  medium?: string
  campaign?: string
  term?: string
  content?: string
}

export type Session = {
  website: string // ObjectId stored as a string
  sessionId: string // Unique session ID

  visitor: VisitorInfo
  geo: GeoInfo
  utm: UTMParams

  referer?: string // Use 'referer' to track referral sources
  sessionStart: string // ISO Date string
  sessionEnd?: string // ISO Date string (nullable if session is active)
  duration?: number // Time in seconds
  pageCount: number
  isActive: boolean

  createdAt: string // Date string
  updatedAt: string // Date string
}

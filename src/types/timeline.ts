import { z } from "zod"

const isValidDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return !isNaN(date.getTime())
}

export const timelineSchema = z
  .object({
    website: z.string().optional(),
    _id: z.string().optional(),
    title: z.string().min(1, "Title is required").max(100, "Title is too long"),
    description: z.string().max(500, "Description is too long").optional(),
    bullet_points: z
      .array(z.string().max(200, "Bullet point is too long"))
      .optional()
      .default([]),
    type: z.enum(["experience", "education", "achievement", "milestone"], {
      required_error: "Type is required",
      invalid_type_error: "Invalid timeline type",
    }),
    institution: z.string().max(100, "Institution name is too long").optional(),
    location: z.string().max(100, "Location is too long").optional(),

    // Start date handling
    start_date: z
      .string()
      .min(1, "Start date is required")
      .refine(isValidDate, {
        message: "Invalid start date format",
      })
      .transform((date) => new Date(date).toISOString()),

    // End date handling
    end_date: z
      .string()
      .nullable()
      .optional()
      .refine((date) => !date || isValidDate(date), {
        message: "Invalid end date format",
      })
      .transform((date) => (date ? new Date(date).toISOString() : null))
      .default(null),

    is_ongoing: z.boolean().default(false),
    is_enabled: z.boolean().default(true),

    order: z
      .union([z.string(), z.number()])
      .transform((val) => {
        if (typeof val === "string") {
          const parsed = parseFloat(val)
          return isNaN(parsed) ? 0 : parsed
        }
        return val
      })
      .pipe(
        z
          .number()
          .int("Order must be an integer")
          .min(0, "Order must be a non-negative number"),
      )
      .default(0),

    icon: z
      .string()
      .url("Icon must be a valid URL")
      .or(z.literal(""))
      .optional(),

    tags: z
      .array(z.string().max(50, "Tag is too long"))
      .max(10, "Maximum 10 tags allowed")
      .optional()
      .default([]),
  })
  .refine(
    (data) => {
      // Skip validation if ongoing or no end date
      if (data.is_ongoing || !data.end_date) return true

      const startDate = new Date(data.start_date)
      const endDate = new Date(data.end_date)

      return endDate >= startDate
    },
    {
      message: "End date must be after or equal to start date",
      path: ["end_date"],
    },
  )
  .refine(
    (data) => {
      // Ensure end_date is null when is_ongoing is true
      return !data.is_ongoing || data.end_date === null
    },
    {
      message: "End date must be empty for ongoing items",
      path: ["end_date"],
    },
  )

export interface Timeline {
  _id?: string
  title: string
  description: string
  bullet_points: string[]
  type: "experience" | "education" | "achievement" | "milestone"
  institution?: string
  location?: string
  start_date: string // ISO string
  end_date: string | null // ISO string or null
  is_ongoing: boolean
  is_enabled: boolean
  order: number
  icon?: string
  tags: string[]
  createdAt?: string
  updatedAt?: string
}

export type TimelineFormData = Omit<Timeline, "createdAt" | "updatedAt">

// Type for the form's default values
export type TimelineDefaultValues = Partial<TimelineFormData>

// Helper function to create default timeline values
export const createDefaultTimelineValues = (): TimelineDefaultValues => ({
  title: "",
  description: "",
  bullet_points: [],
  type: "experience",
  start_date: new Date().toISOString(),
  end_date: null,
  is_ongoing: false,
  is_enabled: true,
  order: 0,
  tags: [],
})

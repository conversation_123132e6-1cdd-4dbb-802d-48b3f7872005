// Enum for Plan Types
export enum PlanType {
  FREE = "free",
  STARTER = "starter",
  PRO = "pro",
  PREMIUM = "premium",
  LIFETIME = "lifetime",
}

// Enum for Duration Types
export enum PlanDuration {
  MONTHLY = "monthly",
  YEARLY = "yearly",
}

// Enum for Supported Currencies
export enum Currency {
  USD = "USD",
  EUR = "EUR",
  GBP = "GBP",
  INR = "INR",
  AUD = "AUD",
}

// Trial Period Type
export interface TrialPeriod {
  duration: number // In days
  is_active: boolean // Whether a trial period is offered
}

// Discount Type
export interface Discount {
  percentage: number // Discount percentage (if applicable)
  start_date: Date // Discount start date
  end_date: Date // Discount end date
}

// Plan Base Type
export interface Plan {
  _id: string // Unique ID of the plan
  name: string // Name of the plan (e.g., Free, Pro, Premium)
  description?: string // Brief description of the plan
  pricing: {
    monthly: number // Monthly price
    yearly: number // Yearly price
  }
  currency: Currency // Currency of the plan
  type: PlanType // Type of the plan (free, pro, premium, etc.)
  bandwidth_limit?: number // Bandwidth limit in MB or GB (optional)
  storage_limit?: number // Storage limit in MB or GB (optional)
  is_active: boolean // Whether the plan is active
  metadata?: Record<string, any> // Flexible metadata (optional)
  trial_period?: TrialPeriod // Optional trial period for Pro/Lifetime plans
  discount?: Discount // Optional discount for Pro/Lifetime plans
}

// Example of plan types:

// // Free Plan type
// export type FreePlan = PlanBase;

// // Pro Plan type (with trial and discount)
// export type ProPlan = PlanBase;

// // Lifetime Plan type (no trial period)
// export type LifetimePlan = PlanBase;

import { toast } from "sonner"

import { API_BASE_URL } from "@/actions"
import { BillingCycle, Coupon } from "@/types/checkout"
import { Plan } from "@/types/plans"
import { User } from "@/types/user"

import { calculateFinalPrice } from "./calculateAmount"
import { fetchWithAuth } from "./fetchWithAuth"

type RazorpayResponse = {
  razorpay_order_id: string
  razorpay_payment_id: string
  razorpay_signature: string
}

const loadRazorpayScript = (): Promise<boolean> =>
  new Promise((resolve) => {
    if (document.querySelector("#razorpay-script")) {
      resolve(true)
      return
    }
    const script = document.createElement("script")
    script.id = "razorpay-script"
    script.src = "https://checkout.razorpay.com/v1/checkout.js"
    script.onload = () => resolve(true)
    script.onerror = () => resolve(false)
    document.body.appendChild(script)
  })

export const handlePayment = async (
  user: User,
  websiteId: string,
  plan: Plan,
  planType: BillingCycle,
  coupon: Coupon | null,
): Promise<void> => {
  const isRazorpayLoaded = await loadRazorpayScript()
  if (!isRazorpayLoaded) {
    toast.error(
      "Failed to load Razorpay. Please refresh the page and try again.",
    )
    return
  }

  const currency = "USD"
  const amount = calculateFinalPrice(plan, planType, coupon)

  if (amount <= 0) {
    toast.error(
      "There is some issue with the payment amount. Please contact support.",
    )
    return
  }

  try {
    const response = await fetchWithAuth(
      `${API_BASE_URL}/payments/create-order`,
      {
        method: "POST",
        body: JSON.stringify({
          userId: user._id,
          websiteId,
          planId: plan._id,
          amount,
          currency,
        }),
      },
    )

    if (!response) throw new Error("Failed to create payment order.")

    const { order, newOrder } = response

    const options: RazorpayOptions = {
      key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID || "",
      amount: order.amount,
      currency: order.currency,
      name: "ThePortfolyo",
      description: "Subscription Plan Upgrade",
      order_id: order.id,
      handler: async (response: RazorpayResponse) => {
        try {
          const verifyRes = await fetchWithAuth(
            `${API_BASE_URL}/payments/verify-payment`,
            {
              method: "POST",
              body: JSON.stringify({
                ...response,
                orderId: newOrder._id,
                userId: user._id,
                websiteId,
                planId: plan._id,
                amount,
                currency,
              }),
            },
          )

          if (!verifyRes.success)
            throw new Error("Payment verification failed.")

          if (verifyRes.data.verified) {
            toast.success("Upgrade successful!")
            window.location.href = `/dashboard/${websiteId}`
          }
        } catch (err) {
          console.error("Payment verification error:", err)
          toast.error("Error verifying payment. Please contact support.")
        }
      },
      prefill: {
        name: user.full_name,
        email: user.email,
        contact: user?.phone_number ?? "",
      },
      theme: {
        color: "#9514ff",
      },
      modal: {
        ondismiss: () => {
          toast.info("Upgrade process was canceled. You can try again anytime.")
        },
      },
    }

    const razorpay = new (window as any).Razorpay(options)

    razorpay.on("payment.failed", (response: any) => {
      console.error("Payment failed:", response.error)
      toast.error(
        `Payment failed due to ${response.error.reason}. Please try again.`,
      )
    })

    razorpay.open()
  } catch (error) {
    console.error("Payment failed:", error)
    toast.error(
      "An error occurred during the upgrade process. Please try again later.",
    )
  }
}

interface RazorpayOptions {
  key: string
  amount: number
  currency: string
  name: string
  description: string
  order_id: string
  handler: (response: RazorpayResponse) => Promise<void>
  prefill: {
    name: string
    email: string
    contact: string
  }
  theme: {
    color: string
  }
  modal?: {
    ondismiss?: () => void
  }
}

export function mapDataToSchemas(extractedData: any) {
  const {
    personalDetails,
    workExperience,
    education,
    skills,
    projects,
    certifications,
    socialLinks,
  } = extractedData

  // Map personal details to the About schema
  const aboutData = {
    first_name: personalDetails?.firstName || "",
    middle_name: personalDetails?.middleName || "",
    last_name: personalDetails?.lastName || "",
    title: personalDetails?.title || "",
    sub_title: personalDetails?.subTitle || "",
    bio: personalDetails?.bio || "",
    quote: personalDetails?.quote || "",
    years_of_experience: personalDetails?.yearsOfExperience || 0,
    address: {
      street: personalDetails?.address?.street || "",
      city: personalDetails?.address?.city || "",
      state: personalDetails?.address?.state || "",
      country: personalDetails?.address?.country || "",
      zip_code: personalDetails?.address?.zipCode || "",
    },
    total_projects: projects?.length || 0,
    total_clients: 0, // You can update this based on your logic
    phone_number: personalDetails?.phoneNumber || "",
    contact_email: personalDetails?.email || "",
    social_links: socialLinks?.map((link: any) => ({
      platform: link.platform,
      url: link.url,
      icon: link.icon || "",
    })),
    avatar: personalDetails?.avatar || "",
    alternate_avatars: personalDetails?.alternateAvatars || [],
  }

  // Map work experience to the Timeline schema
  const timelineData = workExperience?.map((exp: any) => ({
    title: exp.position,
    description: exp.description,
    bullet_points: exp.responsibilities,
    type: "experience",
    institution: exp.company,
    location: exp.location,
    start_date: exp.startDate,
    end_date: exp.endDate,
    is_ongoing: exp.isOngoing || false,
    icon: exp.icon || "",
    tags: exp.tags || [],
  }))

  // Map education to the Timeline schema
  const educationData = education?.map((edu: any) => ({
    title: edu.degree,
    description: edu.description,
    type: "education",
    institution: edu.school,
    location: edu.location,
    start_date: edu.startDate,
    end_date: edu.endDate,
    is_ongoing: edu.isOngoing || false,
    icon: edu.icon || "",
    tags: edu.tags || [],
  }))

  // Map skills to the ServiceSkill schema
  const skillsData = skills?.map((skill: any) => ({
    title: skill.name,
    description: skill.description,
    type: "skill",
    icon: skill.icon || "",
    percentage: skill.proficiency || 0,
  }))

  // Map projects to the Portfolio schema
  const portfolioData = projects?.map((project: any) => ({
    title: project.name,
    description: project.description,
    images: project.images || [],
    external_links: {
      live_url: project.liveUrl || "",
      repository_url: project.repositoryUrl || "",
    },
    tags: project.tags || [],
    categories: project.categories || [],
  }))

  // Map certifications to the Timeline schema
  const certificationData = certifications?.map((cert: any) => ({
    title: cert.name,
    description: cert.description,
    type: "achievement",
    institution: cert.issuer,
    start_date: cert.issueDate,
    end_date: cert.expiryDate || null,
    is_ongoing: !cert.expiryDate,
    icon: cert.icon || "",
    tags: cert.tags || [],
  }))

  return {
    about: aboutData,
    timeline: [...timelineData, ...educationData, ...certificationData],
    skills: skillsData,
    portfolio: portfolioData,
  }
}

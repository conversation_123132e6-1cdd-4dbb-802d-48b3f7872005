const GUMROAD_BASE_URL = "https://api.gumroad.com/v2"

// Types for the API responses
interface GumroadProduct {
  id: string
  name: string
  description: string
  short_url: string
  [key: string]: any // Catch-all for other fields
}

interface GumroadProductsResponse {
  success: boolean
  products: GumroadProduct[]
}

interface GumroadProductResponse {
  success: boolean
  product: GumroadProduct
}

interface GumroadSalesResponse {
  success: boolean
  sales: any[] // Replace `any` with a specific sales type if available
}

/**
 * Fetches all products from Gumroad.
 * @returns {Promise<GumroadProductsResponse>} The response containing the list of products.
 */
export const fetchProducts = async (): Promise<GumroadProductsResponse> => {
  try {
    const response = await fetch(`${GUMROAD_BASE_URL}/products`, {
      headers: {
        Authorization: `Bearer ${process.env.GUMROAD_ACCESS_TOKEN}`,
      },
    })

    if (!response.ok) {
      throw new Error(`Error fetching products: ${response.statusText}`)
    }

    return (await response.json()) as GumroadProductsResponse
  } catch (error) {
    console.error("Failed to fetch products:", error)
    throw error
  }
}

/**
 * Fetches a specific product by its ID.
 * @param {string} productId - The ID of the product.
 * @returns {Promise<GumroadProductResponse>} The response containing the product details.
 */
export const fetchProductByIdAdmin = async (
  productId: string,
): Promise<GumroadProductResponse> => {
  try {
    const response = await fetch(`${GUMROAD_BASE_URL}/products/${productId}`, {
      headers: {
        Authorization: `Bearer ${process.env.GUMROAD_ACCESS_TOKEN}`,
      },
    })

    if (!response.ok) {
      throw new Error(`Error fetching product: ${response.statusText}`)
    }

    return (await response.json()) as GumroadProductResponse
  } catch (error) {
    console.error("Failed to fetch product:", error)
    throw error
  }
}

/**
 * Fetches all sales data.
 * @returns {Promise<GumroadSalesResponse>} The response containing the list of sales.
 */
export const fetchSales = async (): Promise<GumroadSalesResponse> => {
  try {
    const response = await fetch(`${GUMROAD_BASE_URL}/sales`, {
      headers: {
        Authorization: `Bearer ${process.env.GUMROAD_ACCESS_TOKEN}`,
      },
    })

    if (!response.ok) {
      throw new Error(`Error fetching sales: ${response.statusText}`)
    }

    return (await response.json()) as GumroadSalesResponse
  } catch (error) {
    console.error("Failed to fetch sales:", error)
    throw error
  }
}

export const getCountryFromGeolocation = async () => {
  if ("geolocation" in navigator) {
    try {
      return new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          async (position) => {
            const { latitude, longitude } = position.coords

            try {
              // Reverse Geocoding using OpenCage API
              const response = await fetch(
                `https://api.opencagedata.com/geocode/v1/json?q=${latitude}+${longitude}&key=YOUR_API_KEY`,
              )

              if (!response.ok) {
                reject("Failed to fetch geolocation data.")
                return
              }

              const data = await response.json()
              const country = data.results[0]?.components?.country || "Unknown"
              resolve(country)
            } catch (error) {
              reject("Error in reverse geocoding API: ")
            }
          },
          (error) => {
            reject("Geolocation error: " + error.message)
          },
        )
      })
    } catch (error) {
      console.error("Error using Geolocation API:", error)
      return "Unknown"
    }
  } else {
    console.log("Geolocation is not available in this browser.")
    return "Unknown"
  }
}

// "use server";

// import { cookies } from "next/headers";
import { getCookie } from "cookies-next/client"

// export const getToken = async (): Promise<string | undefined> => {
//   const cookieStore = cookies();
//   const session = cookieStore.get("__session")?.value;
//   return session;
// };

export const getToken = (): string | undefined => {
  // return localStorage.getItem("token");
  const token = getCookie("__session")
  return token
}

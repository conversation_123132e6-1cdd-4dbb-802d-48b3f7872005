import { toast } from "sonner"

import { FileValidation, UploadResult } from "@/types/custom-upload"
import { compressMultipleImagesNewForS3 } from "@/utils/image-compression"

export async function processFiles(acceptedFiles: File[]): Promise<File[]> {
  try {
    const filesToCompress = acceptedFiles.filter(
      (file) => file.type !== "image/svg+xml",
    )
    const svgFiles = acceptedFiles.filter(
      (file) => file.type === "image/svg+xml",
    )

    const compressedFiles = await compressMultipleImagesNewForS3(
      filesToCompress,
      { quality: 0.8 },
    )

    const allFiles = [...compressedFiles, ...svgFiles]
    await FileValidation.parseAsync({ files: allFiles })

    return allFiles
  } catch (error) {
    if (error instanceof Error) {
      toast.error(error.message)
    }
    throw error
  }
}

export async function uploadFilesToS3(
  files: File[],
  folderName: string | undefined,
  onProgress: (progress: number) => void,
): Promise<UploadResult> {
  if (!files.length) {
    throw new Error("No files selected for upload")
  }

  const formData = new FormData()
  files.forEach((file) => formData.append("files", file))
  formData.append("folder", folderName ?? "unknown")

  const token = localStorage.getItem("token")
  if (!token) {
    throw new Error("Authentication token is missing")
  }

  const totalSize = files.reduce((acc, file) => acc + file.size, 0)
  console.log(`Total upload size: ${totalSize} bytes`)

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    console.log("Starting upload for", files.length, "files")

    let lastProgress = 0
    let progressCount = 0

    xhr.upload.addEventListener("progress", (event) => {
      if (event.lengthComputable) {
        const percent = Math.round((event.loaded * 100) / event.total)
        progressCount++
        console.log(
          `Progress event #${progressCount}: ${event.loaded}/${event.total} (${percent}%)`,
        )
        if (percent !== lastProgress) {
          onProgress(percent)
          lastProgress = percent
        }
      } else {
        console.log("Progress not computable:", event)
      }
    })

    xhr.addEventListener("loadstart", () => {
      console.log("Upload started")
      onProgress(0)
    })

    xhr.addEventListener("load", () => {
      console.log("Upload completed, status:", xhr.status)
      if (xhr.status >= 200 && xhr.status < 300) {
        const results = JSON.parse(xhr.responseText)
        // console.log("Server response:", results);
        onProgress(100) // Ensure it reaches 100%
        // resolve(Array.isArray(results) ? results : [results]);
        resolve(results)
      } else {
        // console.error("Upload failed:", xhr.status, xhr.responseText);
        reject(new Error(`Upload failed with status ${xhr.status}`))
      }
    })

    xhr.addEventListener("error", () => {
      console.error("Upload error occurred")
      reject(new Error("Upload failed"))
    })

    xhr.addEventListener("abort", () => {
      console.log("Upload aborted")
      reject(new Error("Upload aborted"))
    })

    // Fallback for small files: simulate progress if no intermediate updates
    if (totalSize < 1024 * 1024) {
      // Less than 1MB
      // console.log("Small upload detected, simulating progress");
      let simulatedProgress = 0
      const interval = setInterval(() => {
        simulatedProgress += 10
        if (simulatedProgress < 90) {
          // Stop at 90% to let real progress finish
          onProgress(simulatedProgress)
        } else {
          clearInterval(interval)
        }
      }, 100) // Update every 100ms
    }

    xhr.open(
      "POST",
      `${process.env.NEXT_PUBLIC_AWS_SERVICE_API_URL}/s3/upload-multiple`,
    )
    xhr.setRequestHeader("Authorization", `Bearer ${token}`)
    xhr.send(formData)
  })
}

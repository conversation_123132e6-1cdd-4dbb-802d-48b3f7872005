import { API_BASE_URL } from "@/actions"

export async function fetchWithAuth(url: string, options: RequestInit = {}) {
  // Retrieve the auth token
  const token = localStorage.getItem("token")

  if (!token) {
    console.error("No token found in localStorage")
    return null
  }

  const fullUrl = url.startsWith("http") ? url : `${API_BASE_URL}/${url}`

  try {
    // Fetch data from the given URL with the provided options
    const response = await fetch(fullUrl, {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${token}`, // Add Authorization header
        "Content-Type": "application/json", // Default content type
      },
      cache: "no-store", // Avoid caching for fresh data
    })

    const data = await response.json() // Parse JSON response

    // Check if response is not OK
    if (!response.ok) {
      const errorMessage =
        typeof data === "object" && data?.message
          ? data.message
          : `Request failed with status ${response.status}`
      console.error(`Fetch error: ${response.status} - ${errorMessage}`)
      throw new Error(errorMessage)
    }

    return data
  } catch (error) {
    // Enhanced error handling and logging
    console.error("An error occurred during fetchWithAuth:")
    console.error(error)
    throw error // Re-throw the error to handle it higher up
  }
}

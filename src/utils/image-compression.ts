import imageCompression from "browser-image-compression"

export interface CompressOptions {
  quality?: number
  maxSizeMB?: number
}

export const compressImage = async (
  file: File,
  options: CompressOptions = {},
): Promise<Blob> => {
  console.log("Starting image compression for:", file.name)

  const compressionOptions = {
    maxSizeMB: options.maxSizeMB || 1,
    maxWidthOrHeight: 1920,
    useWebWorker: true,
    fileType: "image/webp",
    quality: options.quality || 0.8,
  }

  try {
    const compressedFile = await imageCompression(file, compressionOptions)
    console.log("Image compression completed for:", file.name)
    return compressedFile
  } catch (error) {
    console.error("Error compressing image:", error)
    throw error
  }
}

export const compressMultipleImages = async (
  files: File[],
  options: CompressOptions = {},
): Promise<Blob[]> => {
  console.log(
    "Starting compression for multiple images:",
    files.length,
    "files",
  )
  const results: Blob[] = []

  for (const file of files) {
    try {
      const compressedImage = await compressImage(file, options)
      results.push(compressedImage)
    } catch (error) {
      console.error(`Error compressing ${file.name}:`, error)
    }
  }

  return results
}

export const compressMultipleImagesNewForS3 = async (
  files: File[],
  options: CompressOptions = {},
  onProgress?: (progress: number) => void,
): Promise<File[]> => {
  console.log(`🗜️ Compressing ${files.length} images for S3...`)

  const totalFiles = files.length
  let completed = 0

  const compressedFiles = await Promise.all(
    files.map(async (file) => {
      try {
        // Compress the image
        const compressedBlob = await compressImage(file, options)
        completed++

        // Update progress if callback is provided
        if (onProgress) {
          onProgress(Math.round((completed / totalFiles) * 100))
        }

        // Convert Blob to File
        const compressedFile = new File(
          [compressedBlob],
          file.name.replace(/\.[^.]+$/, ".webp"),
          {
            type: "image/webp",
            lastModified: Date.now(),
          },
        )

        return compressedFile
      } catch (error) {
        console.error(`❌ Error compressing ${file.name}:`, error)
        return null // Skip failed files
      }
    }),
  )

  return compressedFiles.filter(Boolean) as File[]
}

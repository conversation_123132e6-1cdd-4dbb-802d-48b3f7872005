// Import necessary modules and constants
import {
  codingLanguageIcons,
  photographyImages,
  posterImages,
  projectImages,
  publicUserImages,
  socialMediaIcons,
} from "@/constants/public-images"
import { getAboutSampleData } from "@/data/about/about-samples"
import { getHeroSampleData } from "@/data/hero/hero-samples"
import { getPortfolioSampleData } from "@/data/portfolio/portfolio-samples"
import { getMultipleReviewSamples } from "@/data/reviews/review-samples"
import { getMultipleServiceSkillSamples } from "@/data/service-skill/service-skill-samples"
import { getTimelineSampleData } from "@/data/timeline/timeline-samples"
import { About } from "@/types/about"
import { HeroFormValues } from "@/types/hero"
import { PortfolioFormData } from "@/types/portfolio"
import { Review } from "@/types/review"
import { ServiceSkill } from "@/types/service-skill"
import { Timeline } from "@/types/timeline"

// Utility type to add website field to any type
type WithWebsite<T> = T & { website: string }

// Define the structure with website field added to each type
interface StructuredData {
  hero: WithWebsite<HeroFormValues>
  about: WithWebsite<About>
  portfolio: WithWebsite<PortfolioFormData>[]
  timeline: WithWebsite<Timeline>[]
  reviews: WithWebsite<Review>[]
  service_skills: WithWebsite<ServiceSkill>[]
}

// Helper functions for data generation
function generateHeroSection(
  websiteId: string,
  existingData?: Partial<WithWebsite<HeroFormValues>>,
): WithWebsite<HeroFormValues> {
  const sampleData = getHeroSampleData()
  const mainImage = getRandomItem(publicUserImages).url
  const backgroundImages = [
    getRandomItem(photographyImages).url,
    getRandomItem(posterImages).url,
  ]

  return {
    ...sampleData,
    ...existingData,
    main_image: existingData ? mainImage : sampleData.main_image,
    background_images: existingData
      ? backgroundImages
      : sampleData.background_images,
    website: websiteId,
  }
}

function generateAboutSection(
  websiteId: string,
  existingData?: Partial<WithWebsite<About>>,
): WithWebsite<About> {
  const sampleData = getAboutSampleData()
  const profileImage = getRandomItem(publicUserImages).url
  const socialMediaImages = socialMediaIcons.map((icon) => {
    return {
      platform: icon.title,
      url: `https://${icon.title.toLowerCase()}.com/${
        sampleData.contact_email.split("@")[0]
      }`,
      icon: icon.url,
    }
  })

  // Properly merge address fields, ensuring all fields exist
  const mergedAddress = {
    street: existingData?.address?.street || "",
    city: existingData?.address?.city || "",
    state: existingData?.address?.state || "",
    country: existingData?.address?.country || "",
    zip_code: existingData?.address?.zip_code || "",
  }

  return {
    ...sampleData,
    ...existingData,
    address: mergedAddress,
    avatar: existingData ? profileImage : sampleData.avatar,
    social_links: existingData ? socialMediaImages : sampleData.social_links,
    website: websiteId,
  }
}

function generatePortfolioSection(
  websiteId: string,
  existingProjects: WithWebsite<PortfolioFormData>[] = [],
): WithWebsite<PortfolioFormData>[] {
  if (existingProjects.length >= 3) {
    // Replace images in existing projects with public images
    return existingProjects.map((project) => {
      const projectImage = getRandomItem(projectImages).url
      const additionalImages = projectImages
        .slice(0, 3)
        .map((img) => img.url)
        .sort(() => Math.random() - 0.5)

      return {
        ...project,
        thumbnail: projectImage,
        images: [projectImage, ...additionalImages],
      }
    })
  }

  const newProjects = Array(3 - existingProjects.length)
    .fill(null)
    .map(() => {
      const sampleProject = getPortfolioSampleData()
      const projectImage = getRandomItem(projectImages).url
      const additionalImages = projectImages
        .slice(0, 3)
        .map((img) => img.url)
        .sort(() => Math.random() - 0.5)

      return {
        ...sampleProject,
        thumbnail: projectImage,
        images: [projectImage, ...additionalImages],
        website: websiteId,
      }
    })

  return [...existingProjects, ...newProjects]
}

function generateTimelineSection(
  websiteId: string,
  existingTimeline: WithWebsite<Timeline>[] = [],
): WithWebsite<Timeline>[] {
  if (existingTimeline.length >= 4) {
    return existingTimeline
  }

  const timelineTypes = [
    "experience",
    "education",
    "achievement",
    "milestone",
  ] as const
  const newEntries = timelineTypes
    .slice(0, 4 - existingTimeline.length)
    .map((type) => ({
      ...getTimelineSampleData(type),
      website: websiteId,
    }))

  return [...existingTimeline, ...newEntries]
}

function generateSkillsSection(
  websiteId: string,
  existingSkills: WithWebsite<ServiceSkill>[] = [],
): WithWebsite<ServiceSkill>[] {
  if (existingSkills.length >= 5) {
    // Replace icons with social media icons
    return existingSkills.map((skill) => ({
      ...skill,
      icon: getRandomItem(codingLanguageIcons).url,
    }))
  }

  const remainingCount = 5 - existingSkills.length
  const newSkills = getMultipleServiceSkillSamples("skill", remainingCount).map(
    (skill) => ({
      ...skill,
      icon: getRandomItem(codingLanguageIcons).url,
      website: websiteId,
    }),
  )

  return [...existingSkills, ...newSkills]
}

function generateReviewsSection(
  websiteId: string,
  existingReviews: WithWebsite<Review>[] = [],
): WithWebsite<Review>[] {
  if (existingReviews.length >= 2) {
    // Replace profile images with public user images
    return existingReviews.map((review) => ({
      ...review,
      client_image: getRandomItem(publicUserImages).url,
    }))
  }

  const newReviews = getMultipleReviewSamples(2 - existingReviews.length).map(
    (review) => ({
      ...review,
      client_image: getRandomItem(publicUserImages).url,
      website: websiteId,
    }),
  )

  return [...existingReviews, ...newReviews]
}

/**
 * Generates or enhances structured data for a portfolio website
 * @param websiteId - The unique identifier for the website
 * @param initialData - Optional partial structured data to enhance
 * @returns Complete structured data with all required fields
 */

export function generatePortfolioData(
  websiteId: string,
  initialData: Partial<StructuredData> = {},
): StructuredData {
  console.log("Initial Data:", initialData)
  return {
    hero: generateHeroSection(websiteId, initialData.hero),
    about: generateAboutSection(websiteId, initialData.about),
    portfolio: generatePortfolioSection(websiteId, initialData.portfolio),
    timeline: generateTimelineSection(websiteId, initialData.timeline),
    service_skills: generateSkillsSection(
      websiteId,
      initialData.service_skills,
    ),
    reviews: generateReviewsSection(websiteId, initialData.reviews),
  }
}

const getRandomItem = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)]
}

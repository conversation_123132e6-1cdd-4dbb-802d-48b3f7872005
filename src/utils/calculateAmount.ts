import { BillingCycle, Coupon } from "@/types/checkout"
import { Plan } from "@/types/plans"

const roundValue = (value: number): number => {
  return Math.floor(value)
}

//provide a moneyformatter functionn to format the money value for frontend
export const formatMoney = (
  amount: number,
  currency: string = "USD",
  locale: string = "en-US",
): string => {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}

export const calculateBasePrice = (
  plan: Plan,
  billingCycle: BillingCycle,
): number => {
  return roundValue(
    billingCycle === "monthly" ? plan.pricing.monthly : plan.pricing.yearly,
  )
}

export const calculateDiscountedPrice = (
  plan: Plan,
  billingCycle: BillingCycle,
): number => {
  const basePrice = calculateBasePrice(plan, billingCycle)
  const discountPercentage = plan.discount?.percentage ?? 0
  return roundValue(basePrice - (basePrice * discountPercentage) / 100)
}

export const calculateCouponDiscount = (
  price: number,
  coupon: Coupon | null,
): number => {
  if (!coupon) return 0

  let discount = 0
  if (coupon.discount_type === "percentage") {
    discount = (price * coupon.discount_value) / 100
  } else {
    discount = coupon.discount_value
  }

  return roundValue(Math.min(discount, price)) // Ensure discount does not exceed price
}

export const calculateFinalPrice = (
  plan: Plan,
  billingCycle: BillingCycle,
  coupon: Coupon | null,
): number => {
  const discountedPrice = calculateDiscountedPrice(plan, billingCycle)
  const couponDiscount = calculateCouponDiscount(discountedPrice, coupon)
  return roundValue(discountedPrice - couponDiscount)
}

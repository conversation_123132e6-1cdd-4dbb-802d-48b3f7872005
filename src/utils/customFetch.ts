type FetchOptions = RequestInit & { queryParams?: Record<string, any> }

export const createFetchClient = (
  baseURL: string,
  defaultOptions: RequestInit = {},
) => {
  const client = async (url: string, options: FetchOptions = {}) => {
    const { queryParams, ...restOptions } = options

    // Append query parameters to the URL if provided
    const queryString = queryParams
      ? `?${new URLSearchParams(queryParams).toString()}`
      : ""
    const fullURL = `${baseURL}${url}${queryString}`

    const combinedOptions: RequestInit = {
      ...defaultOptions,
      ...restOptions,
      headers: {
        ...defaultOptions.headers,
        ...restOptions.headers,
      },
    }

    const response = await fetch(fullURL, combinedOptions)

    if (!response.ok) {
      let errorMessage = `Error ${response.status}`
      try {
        const errorData = await response.json()
        errorMessage = errorData.message || errorMessage
      } catch {
        // Fallback in case the response is not <PERSON><PERSON><PERSON>
      }
      throw new Error(errorMessage)
    }

    try {
      return await response.json()
    } catch {
      // Fallback for non-JSON responses
      return response.text()
    }
  }

  return {
    get: (
      url: string,
      queryParams?: Record<string, any>,
      options?: FetchOptions,
    ) => client(url, { method: "GET", queryParams, ...options }),
    post: (url: string, body?: any, options?: FetchOptions) =>
      client(url, {
        method: "POST",
        body: JSON.stringify(body),
        headers: { "Content-Type": "application/json" },
        ...options,
      }),
    put: (url: string, body?: any, options?: FetchOptions) =>
      client(url, {
        method: "PUT",
        body: JSON.stringify(body),
        headers: { "Content-Type": "application/json" },
        ...options,
      }),
    patch: (url: string, body?: any, options?: FetchOptions) =>
      client(url, {
        method: "PATCH",
        body: JSON.stringify(body),
        headers: { "Content-Type": "application/json" },
        ...options,
      }),
    delete: (url: string, options?: FetchOptions) =>
      client(url, { method: "DELETE", ...options }),
  }
}

// how it works
// const apiClient = createFetchClient("https://api.example.com", {
//     headers: { Authorization: "Bearer my-token" },
//   });

//   // GET with query params
//   apiClient
//     .get("/users", { role: "admin" })
//     .then(console.log)
//     .catch(console.error);

//   // POST with body
//   apiClient
//     .post("/users", { name: "John Doe", email: "<EMAIL>" })
//     .then(console.log)
//     .catch(console.error);

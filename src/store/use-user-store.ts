import { create } from "zustand"
import { devtools } from "zustand/middleware"

import { getLoggedInUser } from "@/actions/users"
import { User } from "@/types/user"
import { Website } from "@/types/website"

interface UserStore {
  user: User | null
  userLoading: boolean
  userError: string | null
  isLoggedIn: boolean
  currentWebsite: Website | null
  websiteLoading: boolean
  fetchUser: () => Promise<void>
  clearUser: () => void
  setUser: (user: User) => void
  setIsLoggedIn: (isLoggedIn: boolean) => void
  setCurrentWebsite: (website: Website | null) => void
  logout: () => void
}

export const useUserStore = create<UserStore>()(
  devtools((set) => ({
    user: null,
    userLoading: true,
    userError: null,
    isLoggedIn: false,
    currentWebsite: null,
    websiteLoading: true,

    fetchUser: async () => {
      set({ userLoading: true, userError: null })
      try {
        const user = await getLoggedInUser()
        // console.log("user", user);

        if (user) {
          set({ user, isLoggedIn: true })
        } else {
          set({ isLoggedIn: false, userLoading: false })
        }
      } catch (error) {
        set({ userError: (error as Error).message })
      } finally {
        set({ userLoading: false })
      }
    },

    setUser: (user) => set({ user }),
    setIsLoggedIn: (isLoggedIn) => set({ isLoggedIn }),
    setCurrentWebsite: (website) =>
      set({ currentWebsite: website, websiteLoading: false }),
    clearUser: () =>
      set({ user: null, isLoggedIn: false, currentWebsite: null }),

    logout: () => {
      localStorage.removeItem("token")
      set({ user: null, isLoggedIn: false, currentWebsite: null })
    },
  })),
)

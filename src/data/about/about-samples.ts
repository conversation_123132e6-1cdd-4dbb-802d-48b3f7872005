import { publicUserImages, socialMediaIcons } from "@/constants/public-images"
import { About, SocialLink } from "@/types/about"

const sampleData = {
  firstNames: [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
  ],

  lastNames: [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "Rodriguez",
    "Martinez",
  ],

  titles: [
    "Full Stack Developer",
    "UX/UI Designer",
    "Software Engineer",
    "Product Manager",
    "Frontend Developer",
    "Backend Developer",
    "DevOps Engineer",
    "Mobile App Developer",
    "Cloud Architect",
    "Technical Lead",
  ],

  subTitles: [
    "Building Digital Experiences",
    "Crafting User-Centric Solutions",
    "Turning Ideas into Code",
    "Creating Scalable Systems",
    "Designing the Future",
    "Engineering Excellence",
    "Innovating Through Technology",
  ],

  bios: [
    "Experienced developer passionate about creating innovative solutions. Specializing in full-stack development with expertise in modern frameworks and cloud technologies. Committed to writing clean, maintainable code and building scalable applications.",
    "Creative technologist with a keen eye for detail and a passion for user experience. Combining technical expertise with design thinking to deliver exceptional digital solutions. Experienced in leading teams and mentoring junior developers.",
    "Results-driven software engineer with a track record of delivering high-impact projects. Expertise in agile methodologies and modern development practices. Passionate about solving complex problems and continuous learning.",
    "Innovative developer focused on creating efficient and elegant solutions. Experienced in building scalable applications and implementing best practices. Strong advocate for clean code and test-driven development.",
  ],

  quotes: [
    "Code is poetry in motion",
    "Design is not just what it looks like, it's how it works",
    "Simplicity is the ultimate sophistication",
    "Innovation distinguishes between a leader and a follower",
    "Quality is not an act, it's a habit",
  ],

  cities: [
    "San Francisco",
    "New York",
    "London",
    "Berlin",
    "Toronto",
    "Sydney",
    "Singapore",
    "Amsterdam",
  ],

  states: [
    "California",
    "New York",
    "Texas",
    "Florida",
    "Washington",
    "Massachusetts",
    "Illinois",
    "Oregon",
  ],

  countries: [
    "United States",
    "Canada",
    "United Kingdom",
    "Germany",
    "Australia",
    "Singapore",
    "Netherlands",
    "Sweden",
  ],

  socialPlatforms: [
    {
      platform: "LinkedIn",
      urlPrefix: "https://linkedin.com/in/",
    },
    {
      platform: "GitHub",
      urlPrefix: "https://github.com/",
    },
    {
      platform: "Twitter",
      urlPrefix: "https://twitter.com/",
    },
    {
      platform: "Dribbble",
      urlPrefix: "https://dribbble.com/",
    },
    {
      platform: "Behance",
      urlPrefix: "https://behance.net/",
    },
  ],
}

// Helper functions
const getRandomItem = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)]
}

const getRandomNumber = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

const generateRandomUsername = (firstName: string): string => {
  return `${firstName.toLowerCase()}`
}

const generateSocialLinks = (username: string): SocialLink[] => {
  return sampleData.socialPlatforms.map((platform) => ({
    platform: platform.platform,
    url: `${platform.urlPrefix}${username}`,
    icon:
      socialMediaIcons.find((icon) =>
        icon.title.toLowerCase().includes(platform.platform.toLowerCase()),
      )?.url || "",
  }))
}

export const getAboutSampleData = (initialData: Partial<About> = {}): About => {
  const firstName = getRandomItem(sampleData.firstNames)
  const lastName = getRandomItem(sampleData.lastNames)
  const username = generateRandomUsername(firstName)
  const yearsOfExperience = getRandomNumber(3, 15)

  return {
    first_name: firstName,
    middle_name: "",
    last_name: lastName,
    title: getRandomItem(sampleData.titles),
    sub_title: getRandomItem(sampleData.subTitles),
    bio: getRandomItem(sampleData.bios),
    quote: getRandomItem(sampleData.quotes),
    years_of_experience: yearsOfExperience,
    address: {
      street: `${getRandomNumber(100, 999)} ${getRandomItem(["Main", "Oak", "Maple", "Cedar"])} Street`,
      city: getRandomItem(sampleData.cities),
      state: getRandomItem(sampleData.states),
      country: getRandomItem(sampleData.countries),
      zip_code: `${getRandomNumber(10000, 99999)}`,
    },
    total_projects: getRandomNumber(20, 100),
    total_clients: getRandomNumber(10, 50),
    phone_number: `+1${getRandomNumber(2000000000, 9999999999)}`,
    contact_email: `${username}@example.com`,
    social_links: generateSocialLinks(username),
    avatar: getRandomItem(publicUserImages).url,
    ...initialData,
  }
}

export const initialAboutData: About = {
  first_name: "",
  middle_name: "",
  last_name: "",
  title: "",
  sub_title: "",
  bio: "",
  quote: "",
  years_of_experience: 0,
  address: {
    street: "",
    city: "",
    state: "",
    country: "",
    zip_code: "",
  },
  total_projects: 0,
  total_clients: 0,
  phone_number: "",
  contact_email: "",
  social_links: [],
  avatar: "",
}

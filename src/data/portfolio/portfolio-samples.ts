import { projectImages } from "@/constants/public-images"
import { PortfolioFormData } from "@/types/portfolio"

const sampleData = {
  projectTitles: [
    "E-commerce Platform",
    "Social Media Dashboard",
    "Task Management System",
    "Portfolio Website",
    "Real-time Chat Application",
    "AI-powered Analytics Tool",
    "Mobile Fitness App",
    "Blockchain Wallet",
    "Content Management System",
    "Smart Home IoT Platform",
  ],

  descriptions: [
    "A full-stack e-commerce solution with real-time inventory management, secure payments, and an intuitive admin dashboard.",
    "Comprehensive social media management platform featuring analytics, scheduling, and engagement tracking.",
    "Collaborative project management tool with real-time updates, task delegation, and progress tracking.",
    "Modern portfolio website showcasing projects with dynamic content management and responsive design.",
    "Scalable chat application supporting multiple rooms, file sharing, and end-to-end encryption.",
  ],

  technologies: [
    "React",
    "Next.js",
    "TypeScript",
    "Node.js",
    "Python",
    "Django",
    "Vue.js",
    "Angular",
    "MongoDB",
    "PostgreSQL",
    "AWS",
    "Docker",
    "GraphQL",
    "TailwindCSS",
    "Redux",
  ],

  categories: [
    "Web Development",
    "Mobile Development",
    "Frontend",
    "Backend",
    "Full Stack",
    "DevOps",
    "UI/UX Design",
    "Machine Learning",
    "Blockchain",
    "Cloud Computing",
  ],
}

// Helper functions
const getRandomItems = (array: string[], count: number): string[] => {
  const shuffled = [...array].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

const getRandomNumber = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

const getRandomProjectImages = (): string[] => {
  const numberOfImages = getRandomNumber(2, 5)
  return Array.from(
    { length: numberOfImages },
    () => projectImages[Math.floor(Math.random() * projectImages.length)].url,
  )
}

export const getPortfolioSampleData = (): PortfolioFormData => {
  const title =
    sampleData.projectTitles[
      Math.floor(Math.random() * sampleData.projectTitles.length)
    ]
  const description =
    sampleData.descriptions[
      Math.floor(Math.random() * sampleData.descriptions.length)
    ]

  return {
    title,
    description,
    images: getRandomProjectImages(),
    external_links: {
      live_url: `https://${title.toLowerCase().replace(/\s+/g, "-")}.demo.com`,
      repository_url: `https://github.com/demo/${title.toLowerCase().replace(/\s+/g, "-")}`,
    },
    order: getRandomNumber(1, 10),
    tags: getRandomItems(sampleData.technologies, getRandomNumber(3, 6)),
    categories: getRandomItems(sampleData.categories, getRandomNumber(2, 4)),
    is_enabled: true,
  }
}

import { posterImages, publicUserImages } from "@/constants/public-images"
import { HeroFormValues } from "@/types/hero"

const sampleData = {
  titles: [
    "Build Your Dream Portfolio",
    "Showcase Your Work with Style",
    "Create Your Professional Identity",
    "Stand Out from the Crowd",
    "Your Portfolio, Your Story",
    "Make a Lasting Impression",
    "Design Your Success Story",
    "Elevate Your Online Presence",
  ],

  subtitles: [
    "Showcase Your Work with Style",
    "Where Creativity Meets Professionalism",
    "Your Work, Your Way",
    "Bring Your Projects to Life",
    "Professional Portfolio in Minutes",
    "Share Your Journey with the World",
  ],

  descriptions: [
    "Create a professional portfolio website in minutes. No coding required. Just add your content and watch your portfolio come to life.",
    "Transform your work into a stunning online showcase. Easy to customize, ready to impress.",
    "Build a portfolio that stands out. Showcase your projects, skills, and achievements all in one place.",
    "Your professional journey deserves a professional platform. Create, customize, and share with confidence.",
    "Make your mark online with a portfolio that speaks volumes. Simple to build, impressive to view.",
  ],

  ctaPrimary: [
    { text: "Get Started", url: "#" },
    { text: "Start Free", url: "#" },
    { text: "Create Portfolio", url: "#" },
    { text: "Join Now", url: "#" },
  ],

  ctaSecondary: [
    { text: "Learn More", url: "#" },
    { text: "View Examples", url: "#" },
    { text: "See Templates", url: "#" },
    { text: "Explore Features", url: "#" },
  ],
}

// Helper functions
const getRandomItem = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)]
}

export const getHeroSampleData = (
  initialData: Partial<HeroFormValues> = {},
): HeroFormValues => {
  return {
    title: getRandomItem(sampleData.titles),
    sub_title: getRandomItem(sampleData.subtitles),
    description: getRandomItem(sampleData.descriptions),
    main_image: getRandomItem(publicUserImages).url,
    background_images: [
      getRandomItem(posterImages).url,
      getRandomItem(posterImages).url,
    ],
    cta_button_primary: getRandomItem(sampleData.ctaPrimary),
    cta_button_secondary: getRandomItem(sampleData.ctaSecondary),
    ...initialData,
  }
}

import { socialMediaIcons } from "@/constants/public-images"
import { TimelineFormData } from "@/types/timeline"

type TimelineType = "experience" | "education" | "achievement" | "milestone"

export const getTimelineSampleData = (
  type: TimelineType,
  initialState?: Partial<TimelineFormData>,
): TimelineFormData => {
  const samples: Record<TimelineType, TimelineFormData> = {
    experience: {
      title: "Senior Software Engineer",
      description:
        "Led development of core platform features and mentored junior developers",
      institution: "Tech Corp",
      location: "San Francisco, CA",
      start_date: "2022-01-01",
      end_date: null,
      is_ongoing: true,
      is_enabled: true,
      order: 0,
      type: "experience",
      bullet_points: [
        "Architected and implemented microservices architecture",
        "Reduced system latency by 40%",
        "Led a team of 5 developers",
      ],
      tags: ["React", "Node.js", "AWS"],
      icon: socialMediaIcons[
        Math.floor(Math.random() * socialMediaIcons.length)
      ].url,
    },
    education: {
      title: "Bachelor of Computer Science",
      description: "Specialized in Software Engineering with minor in AI",
      institution: "University of Technology",
      location: "Boston, MA",
      start_date: "2018-09-01",
      end_date: "2022-05-30",
      is_ongoing: false,
      is_enabled: true,
      order: 0,
      type: "education",
      bullet_points: [
        "Dean's List all semesters",
        "Led Programming Club",
        "Published research paper on ML",
      ],
      tags: ["Computer Science", "AI", "Software Engineering"],
      icon: socialMediaIcons[
        Math.floor(Math.random() * socialMediaIcons.length)
      ].url,
    },
    achievement: {
      title: "Best Innovation Award",
      description: "Recognized for developing an AI-powered solution",
      institution: "Tech Innovation Summit",
      location: "New York, NY",
      start_date: "2023-06-15",
      end_date: null,
      is_ongoing: false,
      is_enabled: true,
      order: 0,
      type: "achievement",
      bullet_points: [
        "First place among 100+ submissions",
        "Featured in Tech Weekly",
        "Patent pending for core algorithm",
      ],
      tags: ["Innovation", "AI", "Recognition"],
      icon: socialMediaIcons[
        Math.floor(Math.random() * socialMediaIcons.length)
      ].url,
    },
    milestone: {
      title: "First Startup Launch",
      description: "Successfully launched and grew a tech startup",
      institution: "StartupName",
      location: "Austin, TX",
      start_date: "2021-03-01",
      end_date: null,
      is_ongoing: false,
      is_enabled: true,
      order: 0,
      type: "milestone",
      bullet_points: [
        "Reached 10k active users",
        "Secured seed funding",
        "Built MVP in 3 months",
      ],
      tags: ["Startup", "Entrepreneurship", "Leadership"],
      icon: socialMediaIcons[
        Math.floor(Math.random() * socialMediaIcons.length)
      ].url,
    },
  }

  return {
    ...samples[type],
    ...initialState,
  }
}

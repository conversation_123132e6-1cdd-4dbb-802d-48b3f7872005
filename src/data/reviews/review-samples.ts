import { publicUserImages } from "@/constants/public-images"
import { ReviewFormData } from "@/types/review"

const sampleData = {
  clientNames: [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
  ],

  designations: [
    "CT<PERSON>",
    "Product Manager",
    "Senior Developer",
    "Creative Director",
    "Technical Lead",
    "UX Designer",
    "Project Manager",
    "Art Director",
    "Software Architect",
    "Digital Strategist",
  ],

  companies: [
    "TechVision Labs",
    "Innovation Hub",
    "Digital Dynamics",
    "Creative Solutions",
    "Future Systems",
    "Cloud Nine Tech",
    "Pixel Perfect",
    "Next Level Digital",
    "Smart Solutions",
    "Elite Technologies",
  ],

  feedbacks: [
    "Working with this team has been an absolute pleasure. Their attention to detail and technical expertise are outstanding. The final product exceeded all our expectations.",
    "Exceptional service and outstanding results! The team's professionalism and dedication to quality made our project a huge success. Highly recommended for any technical project.",
    "Impressed by the level of expertise and creativity brought to our project. The team's communication was excellent, and they delivered exactly what we needed, on time and within budget.",
    "A fantastic experience from start to finish. The team's technical knowledge combined with their innovative approach helped us achieve our goals and more. Would definitely work with them again.",
    "The quality of work and professional approach was remarkable. They not only met our requirements but provided valuable suggestions that improved our initial concept significantly.",
    "Outstanding delivery and exceptional attention to detail. The team's technical expertise and creative problem-solving skills made our complex project seem effortless.",
    "Brilliant work! The team's ability to understand our needs and translate them into a practical solution was impressive. Their technical expertise is matched by their professional service.",
    "Could not be happier with the results. The team's technical proficiency and creative approach brought our vision to life perfectly. Their communication throughout was excellent.",
    "An amazing experience working with true professionals. Their technical knowledge and innovative solutions helped us achieve results beyond our expectations.",
    "The level of expertise and dedication shown throughout our project was exceptional. They delivered a superior product while maintaining clear communication at every stage.",
  ],
}

// Helper functions
const getRandomItem = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)]
}

const getRandomRating = (): number => {
  // Bias towards higher ratings (4-5)
  const ratings = [4, 4, 4, 5, 5, 5, 5, 5]
  return getRandomItem(ratings)
}

export const getReviewSampleData = (
  initialData: Partial<ReviewFormData> = {},
): ReviewFormData => {
  return {
    client_name: getRandomItem(sampleData.clientNames),
    client_designation: getRandomItem(sampleData.designations),
    company: getRandomItem(sampleData.companies),
    feedback: getRandomItem(sampleData.feedbacks),
    rating: getRandomRating(),
    client_image: getRandomItem(publicUserImages).url,
    is_enabled: true,
    ...initialData,
  }
}

export const getMultipleReviewSamples = (
  count: number = 3,
  initialData: Partial<ReviewFormData> = {},
): ReviewFormData[] => {
  const reviews: ReviewFormData[] = []
  const usedNames = new Set()
  const usedFeedbacks = new Set()

  for (let i = 0; i < count; i++) {
    let clientName: string
    let feedback: string

    // Ensure unique names and feedbacks
    do {
      clientName = getRandomItem(sampleData.clientNames)
      feedback = getRandomItem(sampleData.feedbacks)
    } while (usedNames.has(clientName) || usedFeedbacks.has(feedback))

    usedNames.add(clientName)
    usedFeedbacks.add(feedback)

    reviews.push({
      ...getReviewSampleData({
        client_name: clientName,
        feedback: feedback,
        ...initialData,
      }),
    })
  }

  return reviews
}

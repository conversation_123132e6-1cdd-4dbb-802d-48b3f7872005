import {
  codingLanguageIcons,
  socialMediaIcons,
} from "@/constants/public-images"
import { ServiceSkillFormData } from "@/types/service-skill"

const sampleData = {
  services: [
    {
      title: "Web Application Development",
      description:
        "Full-stack web application development using modern technologies and best practices. Specializing in scalable, responsive, and user-friendly solutions.",
      price: 120,
    },
    {
      title: "UI/UX Design",
      description:
        "Professional UI/UX design services focusing on creating intuitive and engaging user experiences that drive results.",
      price: 100,
    },
    {
      title: "Technical Consultation",
      description:
        "Expert technical consultation for your projects. Architecture planning, tech stack selection, and best practices guidance.",
      price: 150,
    },
    {
      title: "Code Review & Optimization",
      description:
        "Comprehensive code review and optimization services to improve performance, security, and maintainability.",
      price: 90,
    },
    {
      title: "API Development",
      description:
        "Design and development of RESTful APIs and microservices with focus on scalability and security.",
      price: 130,
    },
  ],

  skills: [
    // Programming Languages
    { title: "JavaScript", percentage: 95 },
    { title: "Python", percentage: 90 },
    { title: "TypeScript", percentage: 92 },
    { title: "Java", percentage: 85 },
    { title: "C++", percentage: 80 },
    // Frameworks & Libraries
    { title: "React", percentage: 95 },
    { title: "Node.js", percentage: 90 },
    { title: "Express.js", percentage: 88 },
    { title: "Next.js", percentage: 92 },
    { title: "Django", percentage: 85 },
    // Tools & Technologies
    { title: "Git", percentage: 90 },
    { title: "Docker", percentage: 85 },
    { title: "AWS", percentage: 82 },
    { title: "MongoDB", percentage: 88 },
    { title: "PostgreSQL", percentage: 86 },
    // Design Tools
    { title: "Figma", percentage: 90 },
    { title: "Adobe XD", percentage: 85 },
    { title: "Photoshop", percentage: 80 },
  ],

  languages: [
    {
      title: "English",
      percentage: 95,
      description: "Professional working proficiency",
    },
    {
      title: "Spanish",
      percentage: 85,
      description: "Full professional proficiency",
    },
    {
      title: "French",
      percentage: 75,
      description: "Limited working proficiency",
    },
    {
      title: "German",
      percentage: 70,
      description: "Elementary proficiency",
    },
    {
      title: "Hindi",
      percentage: 90,
      description: "Native or bilingual proficiency",
    },
    {
      title: "Mandarin",
      percentage: 60,
      description: "Basic communication skills",
    },
    {
      title: "Japanese",
      percentage: 65,
      description: "Intermediate proficiency",
    },
    {
      title: "Arabic",
      percentage: 55,
      description: "Basic proficiency",
    },
  ],
}

// Helper functions
const getRandomItem = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)]
}

const getRandomPercentage = (min: number = 80, max: number = 95): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

const getRandomPrice = (min: number = 50, max: number = 200): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

const findMatchingIcon = (title: string, type: string): string => {
  if (type === "language") {
    // For languages, use a random social media icon
    return getRandomItem(socialMediaIcons).url
  }

  // For skills and services, try to find matching coding language icon
  const exactMatch = codingLanguageIcons.find(
    (icon) => icon.name.toLowerCase() === title.toLowerCase(),
  )
  if (exactMatch) return exactMatch.url

  const partialMatch = codingLanguageIcons.find(
    (icon) =>
      title.toLowerCase().includes(icon.name.toLowerCase()) ||
      icon.name.toLowerCase().includes(title.toLowerCase()),
  )
  if (partialMatch) return partialMatch.url

  return getRandomItem(socialMediaIcons).url
}

export const getServiceSkillSampleData = (
  type: "service" | "skill" | "language" = "skill",
  initialData: Partial<ServiceSkillFormData> = {},
): ServiceSkillFormData => {
  let sampleItem: any

  switch (type) {
    case "service":
      sampleItem = getRandomItem(sampleData.services)
      return {
        title: sampleItem.title,
        description: sampleItem.description,
        type: "service",
        icon: findMatchingIcon(sampleItem.title, type),
        price: sampleItem.price || getRandomPrice(),
        percentage: null,
        is_enabled: true,
        ...initialData,
      }

    case "language":
      sampleItem = getRandomItem(sampleData.languages)
      return {
        title: sampleItem.title,
        description: sampleItem.description,
        type: "language",
        icon: findMatchingIcon(sampleItem.title, type),
        price: null,
        percentage: sampleItem.percentage || getRandomPercentage(55, 95),
        is_enabled: true,
        ...initialData,
      }

    default: // skill
      sampleItem = getRandomItem(sampleData.skills)
      return {
        title: sampleItem.title,
        description: "",
        type: "skill",
        icon: findMatchingIcon(sampleItem.title, type),
        price: null,
        percentage: sampleItem.percentage || getRandomPercentage(),
        is_enabled: true,
        ...initialData,
      }
  }
}

export const getMultipleServiceSkillSamples = (
  type: "service" | "skill" | "language" = "skill",
  count: number = 3,
  initialData: Partial<ServiceSkillFormData> = {},
): ServiceSkillFormData[] => {
  const sourceArray =
    type === "service"
      ? sampleData.services
      : type === "language"
        ? sampleData.languages
        : sampleData.skills

  // Shuffle array and take first 'count' items
  const selectedItems = [...sourceArray]
    .sort(() => Math.random() - 0.5)
    .slice(0, count)
    .map((item) =>
      getServiceSkillSampleData(type, {
        title: item.title,
        ...initialData,
      }),
    )

  return selectedItems
}

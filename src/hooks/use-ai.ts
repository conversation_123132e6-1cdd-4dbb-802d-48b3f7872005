import { useState } from "react"
import { toast } from "sonner"

type Provider = "openai" | "google" | "mistral"

interface UseAITextProps {
  onSuccess?: (text: string) => void
  onError?: (error: string) => void
  defaultProvider?: Provider
}

export function useAIText({
  onSuccess,
  onError,
  defaultProvider = "mistral",
}: UseAITextProps = {}) {
  const [isLoading, setIsLoading] = useState(false)

  const generateText = async (
    text: string,
    instruction: string,
    provider: Provider = defaultProvider,
  ) => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/ai/text", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ text, instruction, provider }),
      })

      if (!response.ok) throw new Error("Failed to generate text")

      const result = await response.text()
      onSuccess?.(result)
      return result
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "Failed to generate text"
      onError?.(message)
      toast.error("Error", { description: message })
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  return {
    generateText,
    isLoading,
  }
}

interface UseAIStructuredProps {
  onSuccess?: (data: any) => void
  onError?: (error: string) => void
}

export function useAIStructured({
  onSuccess,
  onError,
}: UseAIStructuredProps = {}) {
  const [isLoading, setIsLoading] = useState(false)

  const generateStructuredData = async (text: string, websiteId: string) => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/ai/structured", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ text, websiteId }),
      })

      const data = await response.json()
      if (!response.ok) throw new Error(data.error || "Failed to generate data")

      onSuccess?.(data)
      return data
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "Failed to generate data"
      onError?.(message)
      toast.error("Error", { description: message })
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  return {
    generateStructuredData,
    isLoading,
  }
}

import { useState } from "react"
// import { generateText } from "@/actions/gemini";
import { toast } from "sonner"

import { generateText } from "@/actions/text-generation"

export const useAITextGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false) // Loading state for AI generation
  const [aiError, setAiError] = useState<string | null>(null) // Error state for AI generation

  const handleAITextGeneration = async (
    inputText: string,
    instruction: string,
    onSuccess: (generatedText: string) => void,
  ) => {
    if (!inputText) {
      setAiError("Please provide input text to generate or rewrite content.")
      return
    }

    setIsGenerating(true)
    setAiError(null)

    try {
      // Call the server action to generate or rewrite the text
      const { response, error } = await generateText({
        text: inputText,
        instruction,
      })

      if (error) {
        setAiError(error)
      } else if (response) {
        // Call the success callback with the generated text
        onSuccess(response)
        toast.success(
          instruction.includes("Rewrite")
            ? "Content Rewritten"
            : "Content Generated",
          {
            description: `The content has been successfully ${
              instruction.includes("Rewrite") ? "rewritten" : "generated"
            }.`,
          },
        )
      }
    } catch (error) {
      setAiError("Failed to process content. Please try again.")
      toast.error("Error", {
        description: "Failed to process content. Please try again.",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  return {
    isGenerating,
    aiError,
    handleAITextGeneration,
  }
}

import { useCallback, useRef } from "react"
import { useImmer } from "use-immer"

export function useUndoRedoForm<T>(initialState: T) {
  const [state, updateState] = useImmer<T>(initialState)
  const historyRef = useRef<T[]>([initialState])
  const pointerRef = useRef(0)

  const setState = useCallback(
    (newState: T | ((draft: T) => void)) => {
      if (typeof newState === "function") {
        updateState((draft) => {
          ;(newState as (draft: T) => void)(draft as T)
        })
      } else {
        updateState(() => newState)
      }

      const newHistory = historyRef.current.slice(0, pointerRef.current + 1)
      newHistory.push(typeof newState === "function" ? { ...state } : newState)
      historyRef.current = newHistory
      pointerRef.current = newHistory.length - 1
    },
    [state, updateState],
  )

  const undo = useCallback(() => {
    if (pointerRef.current > 0) {
      pointerRef.current--
      updateState(() => historyRef.current[pointerRef.current])
    }
  }, [updateState])

  const redo = useCallback(() => {
    if (pointerRef.current < historyRef.current.length - 1) {
      pointerRef.current++
      updateState(() => historyRef.current[pointerRef.current])
    }
  }, [updateState])

  const canUndo = pointerRef.current > 0
  const canRedo = pointerRef.current < historyRef.current.length - 1

  return { state, setState, undo, redo, canUndo, canRedo }
}

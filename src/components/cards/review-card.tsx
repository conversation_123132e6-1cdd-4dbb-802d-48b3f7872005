/* eslint-disable @next/next/no-img-element */

"use client"

import { Edit, MoreVertical, Star, Trash2 } from "lucide-react"
import { useState } from "react"

import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>ooter,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Switch } from "@/components/ui/switch"
import { cn } from "@/lib/utils"
import { Review } from "@/types/review"

interface ReviewCardProps {
  review: Review
  onEdit: (review: Review) => void
  onDelete: (review: Review) => void
  onToggle: (review: Review, is_enabled: boolean) => void
}

export function ReviewCard({
  review,
  onEdit,
  onDelete,
  onToggle,
}: ReviewCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <Card
      className={cn(
        "group relative overflow-hidden transition-all duration-200",
        !review.is_enabled && "opacity-60",
        isHovered && "",
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardHeader className='p-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-3'>
            <img
              loading='lazy'
              width={40}
              height={40}
              src={review.client_image || "/placeholder.svg"}
              alt={`${review.client_name}'s avatar`}
              className='h-10 w-10 rounded-full object-cover'
            />
            <div>
              <CardTitle className='line-clamp-1 text-lg font-semibold'>
                {review.client_name}
              </CardTitle>
              <p className='text-sm text-muted-foreground'>
                {review.client_designation}
              </p>
            </div>
          </div>
          <div className='flex items-center gap-2'>
            <Switch
              checked={review.is_enabled}
              onCheckedChange={(checked) => onToggle(review, checked)}
              className='data-[state=checked]:bg-purple-600'
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' size='icon' className='h-8 w-8'>
                  <MoreVertical className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem onClick={() => onEdit(review)}>
                  <Edit className='mr-2 h-4 w-4' />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onDelete(review)}
                  className='text-destructive focus:text-destructive'
                >
                  <Trash2 className='mr-2 h-4 w-4' />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent className='p-4 pt-0'>
        <p className='mb-4 line-clamp-3 text-sm text-muted-foreground'>
          {review.feedback}
        </p>
        <div className='flex items-center gap-1 text-yellow-500'>
          {Array.from({ length: review.rating }).map((_, i) => (
            <Star key={i} className='h-4 w-4' />
          ))}
        </div>
      </CardContent>
      <CardFooter className='p-4 pt-0'>
        <p className='text-sm font-medium'>Company: {review.company}</p>
      </CardFooter>
    </Card>
  )
}

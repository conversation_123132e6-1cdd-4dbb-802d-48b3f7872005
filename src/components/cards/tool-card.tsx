import { ExternalLink } from "lucide-react"
import Image from "next/image"

import { But<PERSON> } from "@/components/ui/button"
import {
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Tool } from "@/constants/tools"

import MagicCard from "../ui/magic-card"

interface ToolCardProps {
  tool: Tool
}

export const ToolCard: React.FC<ToolCardProps> = ({ tool }) => {
  return (
    <MagicCard className='relative'>
      <CardHeader>
        <div className='flex items-center space-x-4'>
          <div className='relative h-12 w-12'>
            <Image
              src={
                !!tool.icon
                  ? tool.icon
                  : "https://vercel.com/api/www/avatar?s=64"
              }
              alt={`${tool.name} icon`}
              layout='fill'
              objectFit='contain'
              className='rounded-md'
            />
          </div>
          <div>
            <CardTitle>{tool.name}</CardTitle>
            <CardDescription className='mt-1'>{tool.category}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className='flex-grow'>
        <p className='text-sm text-muted-foreground'>{tool.description}</p>
      </CardContent>
      <CardFooter>
        <Button asChild className='w-full'>
          <a href={tool.url} target='_blank' rel='noopener noreferrer'>
            Visit Tool
            <ExternalLink className='ml-2 h-4 w-4' />
          </a>
        </Button>
      </CardFooter>
    </MagicCard>
  )
}

/* eslint-disable @next/next/no-img-element */
"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { ImageType } from "@/types/image"

interface GalleryCardProps {
  galleryItem: ImageType

  onDelete: (image: ImageType) => void
  isToggling?: boolean
}

export function GalleryCard({ galleryItem }: GalleryCardProps) {
  return (
    <Card className='relative overflow-hidden transition-all duration-200'>
      <div className='absolute right-2 top-2 z-[1]'>
        {/* <Button
          variant="ghost"
          className="text-red-600"
          size={"sm"}
        >
          Delete
        </Button> */}
      </div>
      <CardHeader className='p-4'>
        <CardTitle className='line-clamp-1 text-lg font-semibold'>
          <div className='relative aspect-video overflow-hidden rounded-lg bg-muted/50'>
            {galleryItem.url ? (
              <img
                loading='lazy'
                src={galleryItem.url}
                alt={galleryItem.alt_text || galleryItem.title}
                width={300}
                height={300}
                className='h-full w-full object-contain p-2 md:p-4'
              />
            ) : (
              <div className='flex h-full w-full items-center justify-center text-muted-foreground'>
                No preview available
              </div>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className='line-clamp-1 text-base font-semibold text-primary-foreground'>
          {galleryItem.title || "Untitled Image"}
        </p>
        {/* <p className='mt-2 line-clamp-1 text-sm text-muted-foreground'>
          {galleryItem.alt_text || "No image description."}
        </p> */}
      </CardContent>
    </Card>
  )
}

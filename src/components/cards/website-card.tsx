import { ArrowRightCircle, Trash2Icon, ZapIcon } from "lucide-react"
import Link from "next/link"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>ooter,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card"
import { cn } from "@/lib/utils"
import type { Website } from "@/types/website"

import { TooltipButton } from "../custom/tooltip-button"

export function WebsiteCard({
  website,
  onDelete,
}: {
  website: Website
  onDelete: () => void
}) {
  return (
    <Card
      className={cn(
        "relative flex flex-col gap-3 bg-gradient-to-br from-primary/5 to-primary/10 p-4 dark:from-primary/10 dark:to-primary/40",
        {
          "border-2 border-yellow-400": website?.is_pro,
          "from-yellow-50 to-yellow-100": website?.is_pro,
        },
      )}
    >
      <CardHeader className='p-0'>
        <CardTitle>
          {website.name}
          {website.is_pro && (
            <Badge variant='success' className='ml-2'>
              Pro
            </Badge>
          )}
        </CardTitle>
        <p className='mt-2 line-clamp-2 text-xs text-muted-foreground'>
          {new Date(website.createdAt).toLocaleDateString()}
        </p>

        <Button
          onClick={onDelete}
          variant='ghost'
          className='absolute right-2 top-0 opacity-70'
          size='icon'
        >
          <Trash2Icon className='h-4 w-4' />
        </Button>
      </CardHeader>
      <CardContent className='p-0'></CardContent>
      <CardFooter className='mt-auto grid items-center justify-end gap-4 p-0'>
        <div className='flex w-full items-center justify-end gap-3'>
          {!website?.is_pro && (
            <TooltipButton
              tooltipContent='Upgrade to PRO'
              variant='white'
              size={"lg"}
              asChild
            >
              <Link href={`/upgrade/${website?._id}`}>
                <ZapIcon className='mr-1.5 size-4 fill-orange-500 text-orange-500' />
                Upgrade
              </Link>
            </TooltipButton>
          )}
          <TooltipButton
            tooltipContent='Manage your website'
            size='lg'
            asChild
            className='w-full'
            // variant={website?.is_pro ? "default" : "white"}
          >
            <Link href={`/dashboard/${website._id}`}>
              Manage <ArrowRightCircle className='ml-2 h-4 w-4' />
            </Link>
          </TooltipButton>
        </div>
      </CardFooter>
    </Card>
  )
}

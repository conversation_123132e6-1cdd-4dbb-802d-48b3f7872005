"use client"
import { Edit, MoreVertical, Trash2 } from "lucide-react"
import { useState } from "react"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  CardContent,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Switch } from "@/components/ui/switch"
import { cn } from "@/lib/utils"
import { ServiceSkill } from "@/types/service-skill"

interface ServiceSkillCardProps {
  serviceSkill: ServiceSkill
  onEdit: (serviceSkill: ServiceSkill) => void
  onDelete: (serviceSkill: ServiceSkill) => void
  onToggle: (serviceSkill: ServiceSkill, is_enabled: boolean) => void
}

export function ServiceSkillCard({
  serviceSkill,
  onEdit,
  onDelete,
  onToggle,
}: ServiceSkillCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <Card
      className={cn(
        "group relative overflow-hidden transition-all duration-200",
        !serviceSkill.is_enabled && "opacity-60",
        isHovered && "",
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardHeader className='p-4'>
        <div className='flex items-center justify-between'>
          <CardTitle className='line-clamp-1 text-lg font-semibold'>
            {serviceSkill.title}
          </CardTitle>
          <div className='flex items-center gap-2'>
            <Switch
              checked={serviceSkill.is_enabled}
              onCheckedChange={(checked) => onToggle(serviceSkill, checked)}
              className='data-[state=checked]:bg-purple-600'
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' size='icon' className='h-8 w-8'>
                  <MoreVertical className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem onClick={() => onEdit(serviceSkill)}>
                  <Edit className='mr-2 h-4 w-4' />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onDelete(serviceSkill)}
                  className='text-destructive focus:text-destructive'
                >
                  <Trash2 className='mr-2 h-4 w-4' />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent className='p-4 pt-0'>
        <p className='mb-4 line-clamp-2 text-sm text-muted-foreground'>
          {serviceSkill.description}
        </p>

        <div className='relative aspect-video overflow-hidden rounded-lg bg-muted/50'>
          {serviceSkill.icon !== "" ? (
            <img
              src={serviceSkill.icon ?? ""}
              alt={serviceSkill.title}
              loading='lazy'
              width={300}
              height={300}
              className='h-full w-full object-contain p-2 transition-transform duration-300 group-hover:scale-110 md:p-4'
            />
          ) : (
            <div className='flex h-full w-full items-center justify-center text-muted-foreground'>
              No preview available
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className='p-4 pt-0'>
        {serviceSkill.type === "service" && serviceSkill.price && (
          <Badge
            variant={"secondary"}
            className='text-sm font-semibold text-muted-foreground'
          >
            Price: ${serviceSkill.price}
          </Badge>
        )}
        {serviceSkill.type === "skill" && serviceSkill.percentage && (
          <Badge
            variant={"secondary"}
            className='border-input text-sm font-semibold text-muted-foreground'
          >
            Skill Level: {serviceSkill.percentage}%
          </Badge>
        )}
      </CardFooter>
    </Card>
  )
}

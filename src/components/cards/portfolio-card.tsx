/* eslint-disable @next/next/no-img-element */
"use client"

import {
  ChevronLeft,
  ChevronRight,
  Edit,
  ExternalLink,
  MoreVertical,
  Trash2,
} from "lucide-react"
import { useState } from "react"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Switch } from "@/components/ui/switch"
import { cn } from "@/lib/utils"
import { Portfolio } from "@/types/portfolio"

interface PortfolioCardProps {
  portfolio: Portfolio
  onEdit: (portfolio: Portfolio) => void
  onDelete: (portfolio: Portfolio) => void
  onToggle: (portfolio: Portfolio, is_enabled: boolean) => void
}

export function PortfolioCard({
  portfolio,
  onEdit,
  onDelete,
  onToggle,
}: PortfolioCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  const nextImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === portfolio.images.length - 1 ? 0 : prevIndex + 1,
    )
  }

  const prevImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? portfolio.images.length - 1 : prevIndex - 1,
    )
  }

  return (
    <Card
      className={cn(
        "group relative overflow-hidden transition-all duration-200",
        !portfolio.is_enabled && "opacity-60",
        isHovered && "",
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardHeader className='p-4 pb-2'>
        <div className='flex items-center justify-between'>
          <CardTitle className='line-clamp-1 text-lg font-semibold'>
            {portfolio.title}
            {portfolio.external_links.live_url && (
              <a
                href={portfolio.external_links.live_url}
                target='_blank'
                rel='noopener noreferrer'
                className='ml-2'
              >
                <Button
                  variant='outline'
                  size='icon'
                  className='opacity-0 transition-opacity duration-200 group-hover:opacity-100'
                >
                  <ExternalLink className='h-4 w-4' />
                </Button>
              </a>
            )}
          </CardTitle>
          <div className='flex items-center gap-2'>
            <Switch
              checked={portfolio.is_enabled}
              onCheckedChange={(checked) => onToggle(portfolio, checked)}
              className='data-[state=checked]:bg-purple-600'
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' size='icon' className='h-8 w-8'>
                  <MoreVertical className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem onClick={() => onEdit(portfolio)}>
                  <Edit className='mr-2 h-4 w-4' />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onDelete(portfolio)}
                  className='text-destructive focus:text-destructive'
                >
                  <Trash2 className='mr-2 h-4 w-4' />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent className='p-4 pt-0'>
        <p className='mb-4 line-clamp-2 text-sm text-muted-foreground'>
          {portfolio.description}
        </p>
        <div className='relative aspect-video overflow-hidden rounded-lg bg-muted'>
          {portfolio.images.length > 0 ? (
            <>
              <img
                loading='lazy'
                src={portfolio.images[currentImageIndex]}
                alt={`${portfolio.title} - Image ${currentImageIndex + 1}`}
                width={300}
                height={300}
                className='h-full w-full object-cover transition-transform duration-300 group-hover:scale-110'
              />
              {portfolio.images.length > 1 && (
                <>
                  <Button
                    variant='tertiary'
                    size='icon'
                    disabled={
                      portfolio.images.length === 1 || currentImageIndex === 0
                    }
                    className='absolute left-2 top-1/2 z-20 hidden -translate-y-1/2 transform transition-all duration-200 group-hover:inline-flex'
                    onClick={prevImage}
                  >
                    <ChevronLeft className='h-4 w-4' />
                  </Button>
                  <Button
                    variant='tertiary'
                    size='icon'
                    disabled={
                      portfolio.images.length === 1 ||
                      currentImageIndex === portfolio.images.length - 1
                    }
                    className='absolute right-2 top-1/2 z-20 hidden -translate-y-1/2 transform transition-all duration-200 group-hover:inline-flex'
                    onClick={nextImage}
                  >
                    <ChevronRight className='h-4 w-4' />
                  </Button>
                </>
              )}
            </>
          ) : (
            <div className='flex h-full w-full items-center justify-center text-muted-foreground'>
              No preview available
            </div>
          )}
          {/* {portfolio.external_links.live_url && (
            <a
              href={portfolio.external_links.live_url}
              target="_blank"
              rel="noopener noreferrer"
              className="absolute top-2 right-2 z-10"
            >
              <Button
                variant="secondary"
                size="sm"
                className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                View Live
              </Button>
            </a>
          )} */}
        </div>
      </CardContent>
      <CardFooter className='p-4 pt-0'>
        <div className='flex flex-wrap gap-2'>
          {portfolio?.tags?.map((tag, index) => (
            <Badge key={index} variant='secondary' className='bg-secondary/50'>
              {tag}
            </Badge>
          ))}
        </div>
      </CardFooter>
    </Card>
  )
}

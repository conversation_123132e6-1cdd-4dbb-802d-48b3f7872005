import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, ExternalLink, Star } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Plugin } from "@/constants/plugins"

import MagicCard from "../ui/magic-card"

interface PluginCardProps {
  plugin: Plugin
  showBadge?: boolean
}

export const PluginCard: React.FC<PluginCardProps> = ({
  plugin,
  showBadge,
}) => {
  return (
    <TooltipProvider>
      <MagicCard className='flex h-full flex-col'>
        <CardHeader>
          <div className='flex items-start justify-between'>
            <div className='flex items-start space-x-4'>
              <div className='relative h-12 w-12'>
                <Image
                  src={
                    !!plugin.icon
                      ? plugin.icon
                      : "https://vercel.com/api/www/avatar?s=64"
                  }
                  // src={plugin.icon ?? 'https://vercel.com/api/www/avatar?s=64'}
                  alt={`${plugin.name} icon`}
                  layout='fill'
                  objectFit='contain'
                  className='rounded-lg'
                />
                {plugin.status !== "active" && (
                  <div className='absolute -right-1 -top-1'>
                    <Tooltip>
                      <TooltipTrigger>
                        <Badge
                          variant={
                            plugin.status === "deprecated"
                              ? "destructive"
                              : "secondary"
                          }
                          className='h-6 w-6 rounded-full p-0'
                        >
                          {plugin.status === "deprecated" ? (
                            <AlertCircle className='h-4 w-4' />
                          ) : (
                            <Clock className='h-4 w-4' />
                          )}
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className='capitalize'>{plugin.status}</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                )}
              </div>
              <div>
                <CardTitle className='flex items-center gap-2'>
                  <h4 className='mr-2 line-clamp-1'> {plugin.name} </h4>

                  {showBadge && plugin.is_pro && (
                    <Badge variant='secondary' className='h-fit'>
                      PRO
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription className='mt-1 flex items-center gap-2'>
                  <span>by {plugin.author}</span>
                  <span className='flex items-center text-yellow-500'>
                    <Star className='mr-1 h-3 w-3 fill-current' />
                    4.8
                  </span>
                </CardDescription>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className='relative z-10 flex-grow'>
          <p className='text-sm text-muted-foreground'>
            {plugin.shortDescription}
          </p>
          <div className='mt-2 flex flex-wrap gap-2'>
            <Badge variant='outline' className='capitalize'>
              {plugin.type}
            </Badge>
            <Badge variant='outline'>v{plugin.version}</Badge>
          </div>
        </CardContent>
        <CardFooter className='flex items-center justify-between'>
          <Button asChild variant='default'>
            <Link href={`#`}>Install plugin</Link>
            {/* <Link href={`/plugins/${plugin.id}`}>Install plugin</Link> */}
          </Button>
          <Button
            variant='ghost'
            size='icon'
            className='h-8 w-8'
            asChild
            // onClick={() =>
            //   window.open(`https://example.com/plugins/${plugin.id}`, "_blank")
            // }
          >
            <Link href={`#`}>
              {/* <Link href={`https://example.com/plugins/${plugin.id}`}> */}
              <ExternalLink className='h-4 w-4' />
              <span className='sr-only'>View plugin details</span>
            </Link>
          </Button>
        </CardFooter>
      </MagicCard>
    </TooltipProvider>
  )
}

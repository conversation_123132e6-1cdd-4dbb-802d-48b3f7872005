import { Skeleton } from "@/components/ui/skeleton"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "../ui/card"

export function SkeletonCard() {
  return (
    <Card className='p-3 md:p-4'>
      <CardHeader className='mb-2 p-0'>
        <div className='flex items-center justify-between'>
          <Skeleton className='h-4 w-[60%]' />
          <Skeleton className='h-4 w-[20%]' />
          <Skeleton className='h-4 w-[10%]' />
        </div>

        <Skeleton className='h-4 w-full' />
      </CardHeader>
      <CardContent className='mb-2 p-0'>
        <Skeleton className='h-[180px] w-full rounded-xl' />
      </CardContent>
      <CardFooter className='flex flex-col items-start justify-start gap-1 p-0'>
        <Skeleton className='h-4 w-full' />
        {/* <Skeleton className="h-4 w-[80%]" /> */}
      </CardFooter>
    </Card>
  )
}

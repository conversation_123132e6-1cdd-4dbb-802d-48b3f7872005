/* eslint-disable @next/next/no-img-element */
"use client"

import Link from "next/link"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Template } from "@/types/template"

interface ThemeCardProps {
  theme: Template
}

export function MainThemeCard({ theme }: ThemeCardProps) {
  return (
    <Card className='overflow-hidden'>
      <CardContent className='p-0'>
        {/* <div className="relative aspect-[3/4] overflow-hidden"> */}
        <div className='relative aspect-video overflow-hidden'>
          <img
            src={theme.preview_image}
            alt={theme.name}
            loading='lazy'
            // fill
            className='object-contain transition-transform hover:scale-105'
          />
        </div>
        <div className='p-4'>
          <div className='flex items-start justify-between gap-4'>
            <div>
              <h3 className='mb-1 text-base font-semibold md:text-lg'>
                {theme.name}
              </h3>
              <p className='line-clamp-2 text-xs text-muted-foreground md:text-sm'>
                {theme.description}
              </p>
            </div>
            {theme.is_pro && (
              <div className='text-right'>
                <Badge
                  variant='default'
                  className='bg-gradient-to-br from-yellow-400 to-orange-400 text-black'
                >
                  Pro
                </Badge>
              </div>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className='flex gap-4 border-t p-4'>
        <Button variant={"secondary"} className='w-full' asChild>
          <Link href={theme?.preview_url ?? "#"}>Preview</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}

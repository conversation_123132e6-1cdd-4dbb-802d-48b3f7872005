import { PlusIcon } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

export function AddNewWebsiteCard({ onClick }: { onClick: () => void }) {
  return (
    <Card
      onClick={onClick}
      className={cn(
        "bg-gradient-t flex h-full min-h-24 cursor-pointer items-center justify-center bg-card bg-gradient-to-br from-primary/5 to-primary/10 dark:from-primary/40 dark:to-primary/10 md:min-h-36",
      )}
    >
      <CardHeader>
        <CardTitle>
          <Button className='mr-3' size='iconlg'>
            <PlusIcon className='h-5 w-5' />
          </Button>
          Add New Website
        </CardTitle>
      </CardHeader>
    </Card>
  )
}

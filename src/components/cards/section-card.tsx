"use client"

import { motion } from "framer-motion"
import { ArrowRightCircle } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { bgImages } from "@/constants/sections"

interface SectionCardProps {
  title: string
  gradient: string
  href: string
  badge?: string
  index: number
  isPro: boolean
  websiteId: string
}

export const SectionCard: React.FC<SectionCardProps> = ({
  title,

  href,
  badge,
  index,
  isPro,
  websiteId,
}) => {
  return (
    <Link href={`/dashboard/${websiteId}${href}`}>
      <Card className='group relative h-[170px] overflow-hidden border-primary/20 bg-primary/5 from-primary/10 to-primary/15 backdrop-blur-sm transition-all hover:bg-gradient-to-br dark:border-primary/20 dark:bg-black/20 dark:from-primary/70 dark:to-primary/50'>
        {/* <motion.img */}
        <Image
          src={bgImages[1]}
          alt=''
          className='absolute inset-0 hidden h-full w-full object-cover opacity-80 transition-opacity duration-500 group-hover:opacity-60 dark:block dark:opacity-40'
          width={300}
          height={170}
          //   initial={{ scale: 1.1 }}
          //   whileHover={{ scale: 1.15 }}
          //   transition={{ duration: 0.4 }}
        />
        <div className='relative flex h-full flex-col justify-between p-4 pb-6 pl-6'>
          <motion.h2
            className='bg-gradient-to-tr from-foreground/90 to-foreground bg-clip-text align-text-bottom font-heading text-3xl font-medium text-transparent dark:from-zinc-400/50 dark:via-white dark:to-white/60'
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: index * 0.1 }}
          >
            {title}
            {!isPro && badge && (
              <Badge className='ml-3 bg-gradient-to-tr from-yellow-400 to-yellow-500 text-xs font-bold text-yellow-900'>
                {badge}
              </Badge>
            )}
          </motion.h2>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            whileHover={{ x: 5 }}
            transition={{ duration: 0.2 }}
          >
            <ArrowRightCircle size={24} className='h-6 w-6' />
          </motion.div>
        </div>
      </Card>
    </Link>
  )
}

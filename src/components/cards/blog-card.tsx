/* eslint-disable @next/next/no-img-element */
import { Edit, MoreVertical, Trash2 } from "lucide-react"
import { useState } from "react"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  CardContent,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Switch } from "@/components/ui/switch"
import { cn } from "@/lib/utils"
import { Blog } from "@/types/blog"

interface BlogCardProps {
  blog: Blog
  onEdit: (blog: Blog) => void
  onDelete: (blog: Blog) => void
  onToggle: (blog: Blog, is_enabled: boolean) => void
  isDeleteLoading: boolean
  isToggleLoading: boolean
}

export function BlogCard({
  blog,
  onEdit,
  onDelete,
  onToggle,
  isDeleteLoading,
  isToggleLoading,
}: BlogCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <Card
      className={cn(
        "group relative overflow-hidden transition-all duration-200",
        !blog.is_enabled && "opacity-60",
        isHovered && "",
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardHeader className='p-4'>
        <div className='flex items-center justify-between'>
          <CardTitle className='line-clamp-1 text-lg font-semibold'>
            {blog.title}
          </CardTitle>
          <div className='flex items-center gap-2'>
            <Switch
              checked={blog.is_enabled}
              onCheckedChange={(checked) => onToggle(blog, checked)}
              className='data-[state=checked]:bg-purple-600'
              disabled={isToggleLoading}
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' size='icon' className='h-8 w-8'>
                  <MoreVertical className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem onClick={() => onEdit(blog)}>
                  <Edit className='mr-2 h-4 w-4' />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onDelete(blog)}
                  className='text-destructive focus:text-destructive'
                  disabled={isDeleteLoading}
                >
                  <Trash2 className='mr-2 h-4 w-4' />
                  {isDeleteLoading ? "Deleting..." : "Delete"}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent className='p-4 pt-0'>
        <p className='mb-4 line-clamp-2 text-sm text-muted-foreground'>
          {blog.excerpt || blog.content.substring(0, 150) + "..."}
        </p>
        {blog.thumbnail && (
          <div className='relative mb-4 aspect-video overflow-hidden rounded-lg bg-muted'>
            <img
              src={blog.thumbnail || "/placeholder.svg"}
              alt={blog.title}
              // layout="fill"
              loading='lazy'
              // objectFit="cover"
              className='object-cover transition-transform duration-300 group-hover:scale-110'
            />
            {blog.featured && (
              <div className='absolute right-0 top-0'>
                <Badge variant='success'>Featured</Badge>
              </div>
            )}
          </div>
        )}
        {/* <div className="flex flex-wrap gap-2 mb-2">
          {blog.categories.map((category, index) => (
            <Badge key={index} variant="secondary">
              {category}
            </Badge>
          ))}
        </div> */}
        <div className='flex flex-wrap gap-2'>
          {blog.tags.map((tag, index) => (
            <Badge key={index} variant='outline'>
              {tag}
            </Badge>
          ))}
        </div>
      </CardContent>
      <CardFooter className='flex items-center justify-between p-4 pt-0'>
        <div className='text-sm text-muted-foreground'>
          {new Date(blog.createdAt).toLocaleDateString()}
        </div>
        {blog.reading_time && (
          <Badge variant='destructive' className='text-xs'>
            {blog.reading_time}
          </Badge>
        )}
      </CardFooter>
    </Card>
  )
}

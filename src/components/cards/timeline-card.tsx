/* eslint-disable @next/next/no-img-element */
"use client"

import {
  Briefcase,
  Calendar,
  Edit,
  MapPin,
  MoreVertical,
  School,
  Star,
  Trash2,
  Trophy,
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Switch } from "@/components/ui/switch"
import { cn } from "@/lib/utils"
import { Timeline } from "@/types/timeline"

interface TimelineCardProps {
  timeline: Timeline
  onEdit: (timeline: Timeline) => void
  onDelete: (timeline: Timeline) => void
  onToggle: (timeline: Timeline, is_enabled: boolean) => void
  isToggling?: boolean
}

const typeConfig = {
  education: {
    icon: School,
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
    borderColor: "border-blue-500/20",
    label: "Institution",
  },
  experience: {
    icon: Briefcase,
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
    borderColor: "border-purple-500/20",
    label: "Company",
  },
  achievement: {
    icon: Trophy,
    color: "text-yellow-500",
    bgColor: "bg-yellow-500/10",
    borderColor: "border-yellow-500/20",
    label: "Achievement",
  },
  milestone: {
    icon: Star,
    color: "text-emerald-500",
    bgColor: "bg-emerald-500/10",
    borderColor: "border-emerald-500/20",
    label: "Milestone",
  },
}

export function TimelineCard({
  timeline,
  onEdit,
  onDelete,
  onToggle,
  isToggling,
}: TimelineCardProps) {
  const config = typeConfig[timeline.type as keyof typeof typeConfig]
  const TypeIcon = config.icon

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      month: "short",
      year: "numeric",
    })
  }

  return (
    <Card
      className={cn(
        "group relative overflow-hidden transition-all duration-200",
        !timeline.is_enabled && "opacity-60",
        config.borderColor,
        "hover:shadow-lg hover:shadow-foreground/5",
      )}
    >
      <CardHeader className='p-4'>
        <div className='flex items-start justify-between gap-4'>
          <div className='flex items-center gap-3'>
            <div
              className={cn("rounded-full p-2", config.bgColor, config.color)}
            >
              <TypeIcon className='h-4 w-4' />
            </div>
            <CardTitle className='line-clamp-2 text-lg font-semibold'>
              {timeline.title}
            </CardTitle>
          </div>
          <div className='flex items-center gap-2'>
            <Switch
              checked={timeline.is_enabled}
              onCheckedChange={(checked) => onToggle(timeline, checked)}
              disabled={isToggling}
              className='data-[state=checked]:bg-purple-600'
            />

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' size='icon' className='h-8 w-8'>
                  <MoreVertical className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem onClick={() => onEdit(timeline)}>
                  <Edit className='mr-2 h-4 w-4' />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onDelete(timeline)}
                  className='text-destructive focus:text-destructive'
                >
                  <Trash2 className='mr-2 h-4 w-4' />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <CardContent className='space-y-4 p-4 pt-0'>
        <p className='line-clamp-2 text-sm text-muted-foreground'>
          {timeline.description}
        </p>

        {timeline.bullet_points && timeline.bullet_points.length > 0 && (
          <div className='flex flex-wrap gap-2'>
            {timeline.bullet_points.map((point, index) => (
              <Badge
                key={index}
                variant='secondary'
                className={cn("font-normal", config.bgColor, config.color)}
              >
                {point}
              </Badge>
            ))}
          </div>
        )}

        <div className='flex flex-col gap-2 text-sm text-muted-foreground'>
          <div className='flex items-center gap-2'>
            {timeline.icon ? (
              <img
                src={timeline.icon}
                alt={timeline.institution}
                className='h-5 w-5 rounded-full object-cover'
              />
            ) : (
              <div className={cn("h-5 w-5 rounded-full", config.bgColor)} />
            )}
            <span>{timeline.institution}</span>
          </div>

          {timeline.location && (
            <div className='flex items-center gap-2'>
              <MapPin className='h-4 w-4' />
              <span>{timeline.location}</span>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className='flex items-center justify-between border-t p-4'>
        <Badge
          variant='outline'
          className={cn("capitalize", config.color, config.borderColor)}
        >
          {timeline.type}
        </Badge>

        <div className='flex items-center gap-2 text-sm text-muted-foreground'>
          <Calendar className='h-4 w-4' />
          <span>
            {formatDate(timeline.start_date)}
            {" - "}
            {timeline.is_ongoing
              ? "Present"
              : formatDate(timeline.end_date || "")}
          </span>
        </div>
      </CardFooter>
    </Card>
  )
}

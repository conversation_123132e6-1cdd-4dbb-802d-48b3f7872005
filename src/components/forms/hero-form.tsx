"use client"

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod"
import { PlusIcon, TrashIcon } from "lucide-react"
import { useEffect } from "react"
import { useFieldArray, useForm } from "react-hook-form"

import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Form } from "@/components/ui/form"
import { getHeroSampleData } from "@/data/hero/hero-samples"
import { cn } from "@/lib/utils"
import { heroFormSchema, type HeroFormValues } from "@/types/hero"

import { FormWrapper } from "../custom/formWrapper"
import { FormFieldWrapper, TextArea, TextInput } from "../form"
import SingleImageSelector from "../gallery/single-image-selector"

type HeroFormProps = {
  initialData: HeroFormValues
  onUpdate: (data: HeroFormValues) => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function HeroForm({
  initialData,
  onUpdate,
  open,
  onOpenChange,
}: HeroFormProps) {
  const form = useForm<HeroFormValues>({
    resolver: zodResolver(heroFormSchema),
    defaultValues: initialData,
    mode: "onChange",
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "background_images",
  } as never)

  // Effect to update form values when initialData changes
  useEffect(() => {
    if (initialData) {
      form.reset(initialData)
    }
  }, [initialData, form])

  function onSubmit(data: HeroFormValues) {
    onUpdate(data)
  }

  const handleReset = () => {
    form.reset(initialData)
  }

  const handleFillSample = () => {
    const sampleData = getHeroSampleData(initialData)
    form.reset(sampleData)
  }

  const FormContent = (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
        <div className={cn("grid grid-cols-1 gap-6 lg:grid-cols-2")}>
          {/* Editor Section */}
          <Card className='order-1 border-0 p-0 lg:order-2'>
            <CardContent className='space-y-6 p-4'>
              {/* Main Content Section */}
              <div className='space-y-4'>
                <h3 className='font-semibold'>Main Content</h3>
                <TextInput
                  control={form.control}
                  name='title'
                  label='Title'
                  placeholder='Enter hero title'
                  required
                />
                <TextInput
                  control={form.control}
                  name='sub_title'
                  label='Subtitle'
                  placeholder='Enter subtitle'
                  required
                />
                <TextArea
                  control={form.control}
                  name='description'
                  label='Description'
                  placeholder='Enter description'
                />
              </div>

              {/* CTA Buttons Section */}
              <div className='space-y-4 border-t pt-6'>
                <h3 className='font-semibold'>Call-to-Action Buttons</h3>
                <div className='space-y-4'>
                  <div className='grid grid-cols-[1fr_2fr] gap-4'>
                    <TextInput
                      control={form.control}
                      name='cta_button_primary.text'
                      label='Primary Button Text'
                      placeholder='e.g., Get Started'
                      required
                    />
                    <TextInput
                      control={form.control}
                      name='cta_button_primary.url'
                      label='Primary Button URL'
                      placeholder='e.g., /get-started'
                      required
                    />
                  </div>
                  <div className='grid grid-cols-[1fr_2fr] gap-4'>
                    <TextInput
                      control={form.control}
                      name='cta_button_secondary.text'
                      label='Secondary Button Text'
                      placeholder='e.g., Learn More'
                    />
                    <TextInput
                      control={form.control}
                      name='cta_button_secondary.url'
                      label='Secondary Button URL'
                      placeholder='e.g., /learn-more'
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Images Section */}

          <div className='space-y-6 p-4'>
            <FormFieldWrapper
              control={form.control}
              name='main_image'
              label='Main Image'
              required
            >
              {(field) => (
                <SingleImageSelector
                  uploadedImage={field.value}
                  setUploadedImage={field.onChange}
                  className='aspect-video w-full border-input'
                />
              )}
            </FormFieldWrapper>

            {/* Background Images */}
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <h4 className='font-medium'>Background Images</h4>
                <Button
                  type='button'
                  variant='outline'
                  size='sm'
                  onClick={() => append("")}
                >
                  <PlusIcon className='mr-2 h-4 w-4' />
                  Add Image
                </Button>
              </div>
              <div className='grid grid-cols-2 gap-4'>
                {fields.map((field, index) => (
                  <div key={field.id} className='group relative'>
                    <FormFieldWrapper
                      control={form.control}
                      label={`Background Image ${index + 1}`}
                      name={`background_images.${index}`}
                    >
                      {(field) => (
                        <SingleImageSelector
                          uploadedImage={field.value}
                          setUploadedImage={field.onChange}
                          className='aspect-square w-full border-input'
                        />
                      )}
                    </FormFieldWrapper>
                    <Button
                      type='button'
                      variant='destructive'
                      size='sm'
                      className='absolute -right-2 -top-2 opacity-0 transition-opacity group-hover:opacity-100'
                      onClick={() => remove(index)}
                    >
                      <TrashIcon className='h-4 w-4' />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className='sticky bottom-0 flex w-full justify-between gap-4 bg-background/40 p-4 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
          <Button
            type='button'
            variant='outline'
            className='flex-1'
            onClick={handleReset}
          >
            Reset
          </Button>

          <Button
            type='button'
            variant='destructive'
            className='flex-1'
            onClick={() => onOpenChange && onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            type='button'
            variant='white'
            className='flex-1'
            onClick={handleFillSample}
          >
            Fill Sample
          </Button>
          <Button type='submit' className='w-full flex-1' size='lg'>
            Save Changes
          </Button>
        </div>
      </form>
    </Form>
  )

  if (open !== undefined && onOpenChange) {
    return (
      <FormWrapper
        title='Edit Hero Section'
        open={open}
        onOpenChange={onOpenChange}
      >
        {FormContent}
      </FormWrapper>
    )
  }

  return FormContent
}

"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  CheckCircle,
  CircleAlertIcon,
  Loader2,
  OctagonAlertIcon,
  XCircle,
} from "lucide-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"

import { getTemplates } from "@/actions/template"
import {
  buildWebsiteWithResumeData,
  checkWebsiteName,
  createWebsite,
} from "@/actions/website"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useDebounce } from "@/hooks/use-debounce"
import { cn } from "@/lib/utils"
import type { Template } from "@/types/template"
import {
  type CreateWebsiteFormData,
  CreateWebsiteFormSchema,
} from "@/types/website-creation"

import { ConfettiEffect } from "../custom/confetti-effect"
import { Skeleton } from "../ui/skeleton"
import { ClientSideResumeExtractor } from "./cv-resume-extractor"

interface MultiStepCreateWebsiteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const steps = ["Name Your Website", "Choose a Template", "Optional Profiles"]

export function MultiStepCreateWebsiteDialog({
  open,
  onOpenChange,
}: MultiStepCreateWebsiteDialogProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const queryClient = useQueryClient()

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    getValues,
    trigger,
    reset,
  } = useForm<CreateWebsiteFormData>({
    resolver: zodResolver(CreateWebsiteFormSchema),
    mode: "onChange",
    defaultValues: {
      name: "",
      template: "",
      plan: process.env.NEXT_PUBLIC_FREE_PLAN_ID, // Fixed default value for the plan
      githubUsername: "",
      pdfData: "",
    },
  })

  //reset form when dialog is closed
  useEffect(() => {
    if (!open) {
      reset()
      setCurrentStep(0)
    }
  }, [open, reset])

  const websiteName = watch("name") || ""
  const selectedTemplate = watch("template")
  // const githubUsername = watch("githubUsername") || ""
  const debouncedName = useDebounce(websiteName, 500)

  const { data: isNameAvailable, isLoading: isCheckingName } = useQuery({
    queryKey: ["checkWebsiteName", debouncedName],
    queryFn: async () => {
      try {
        const data = await checkWebsiteName(debouncedName)

        if (data.success) {
          return true
        } else {
          return false
        }
      } catch (error) {
        console.error("Error checking website name:", error)
        return false
      }
    },
    enabled: debouncedName.length >= 3,
    retry: false,
    refetchOnWindowFocus: false,
  })

  const {
    data: templatesResult,
    isLoading: templateLoading,
    isError: templateError,
  } = useQuery<Template[]>({
    queryKey: ["templates"],
    queryFn: getTemplates,
    enabled: currentStep === 1,
    refetchOnWindowFocus: true,
  })

  const templates = templatesResult?.filter((t) => !t.is_pro)

  const mutation = useMutation({
    mutationFn: async (data: CreateWebsiteFormData) => {
      const responseData = await createWebsite(data)
      if (!responseData.success) {
        throw new Error("Failed to create website")
      }

      // Immediately close dialog and show confetti
      onOpenChange(false)
      setCurrentStep(0)

      return responseData.data._id
    },
    onSuccess: async (websiteId) => {
      const initialToastId = toast.success("Website created successfully!", {
        description: "Setting up your website...",
      })
      try {
        // Invalidate queries first
        queryClient.invalidateQueries({ queryKey: ["user"] })
        queryClient.invalidateQueries({ queryKey: ["websites"] })

        const pdfData = getValues("pdfData")

        if (pdfData && pdfData.trim() !== "") {
          toast.loading("Processing your resume data...", {
            id: initialToastId,
          })

          await buildWebsiteWithResumeData(websiteId, pdfData)

          toast.success("Resume data processed successfully!", {
            id: initialToastId,
            description:
              "Your website has been updated with your resume information.",
          })
        } else {
          toast.loading("Generating sample content...", {
            id: initialToastId,
          })

          await buildWebsiteWithResumeData(websiteId, null)

          toast.success("Website setup completed!", {
            id: initialToastId,
            description: "Your website has been populated with sample content.",
          })
        }
      } catch (error) {
        console.error("Error in post-creation processing:", error)
        toast.error("Post-creation setup encountered an issue", {
          id: initialToastId,
          description:
            "Your website was created, but some setup steps failed. You can retry from the dashboard.",
        })
      } finally {
        toast.dismiss(initialToastId)
      }
    },
    onError: (error) => {
      console.error("Website creation error:", error)
      const errorMessage =
        error instanceof Error
          ? error.message
          : "We couldn't create your website. Please try again."

      toast.error("Oops! Something went wrong.", {
        description: errorMessage,
      })
      setCurrentStep(0)
    },
  })

  const onSubmit = (data: CreateWebsiteFormData) => {
    mutation.mutate(data)
  }

  const validationRules = [
    {
      id: "length",
      label: "Between 3 and 50 characters",
      check: (value: string) => value.length >= 3 && value.length <= 50,
    },
    {
      id: "characters",
      label: "Only letters, numbers, and hyphens",
      check: (value: string) => /^[a-zA-Z0-9-]*$/.test(value),
    },
    {
      id: "start",
      label: "Doesn't start with a hyphen",
      check: (value: string) => !/^[-]/.test(value),
    },
    {
      id: "end",
      label: "Doesn't end with a hyphen",
      check: (value: string) => !/[-]$/.test(value),
    },
    {
      id: "consecutive",
      label: "No consecutive hyphens",
      check: (value: string) => !/--/.test(value),
    },
  ]

  const isStepValid = (step: number) => {
    switch (step) {
      case 0:
        return !errors.name && isNameAvailable
      case 1:
        return !!selectedTemplate
      case 2:
        // If GitHub username is provided, ensure data is fetched
        return true
      default:
        return false
    }
  }

  const isNextButtonDisabled = () => {
    return !isStepValid(currentStep) || isCheckingName
  }

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className='space-y-4'>
            <Label htmlFor='name' className='text-base font-semibold'>
              Website Name <span className='text-red-500'> *</span>
              {isCheckingName ? (
                <p className='text-sm text-muted-foreground'>
                  Checking availability...
                </p>
              ) : (
                websiteName && (
                  <p
                    className={`text-sm font-normal ${
                      isNameAvailable ? "text-green-500" : "text-destructive"
                    }`}
                  >
                    {isNameAvailable
                      ? "Website name is available!"
                      : "Website name is not available."}
                  </p>
                )
              )}
            </Label>
            <div className='relative'>
              <Input
                id='name'
                {...register("name")}
                placeholder='my-awesome-website'
                className='pr-10'
                onChange={(e) => {
                  const lowercaseValue = e.target.value.toLowerCase()
                  setValue("name", lowercaseValue, { shouldValidate: true })
                }}
              />
              {isCheckingName ? (
                <Loader2 className='absolute right-3 top-3 h-4 w-4 animate-spin text-muted-foreground' />
              ) : (
                websiteName &&
                (isNameAvailable ? (
                  <CheckCircle className='absolute right-3 top-3 h-4 w-4 text-green-500' />
                ) : (
                  <XCircle className='absolute right-3 top-3 h-4 w-4 text-destructive' />
                ))
              )}
            </div>
            <div className='space-y-2'>
              {validationRules.map((rule) => (
                <div
                  key={rule.id}
                  className={cn(
                    "flex items-center space-x-2 text-sm",
                    websiteName
                      ? rule.check(websiteName)
                        ? "text-green-500"
                        : "text-destructive"
                      : "text-muted-foreground",
                  )}
                >
                  {websiteName ? (
                    rule.check(websiteName) ? (
                      <CheckCircle className='h-4 w-4' />
                    ) : (
                      <XCircle className='h-4 w-4' />
                    )
                  ) : (
                    <CircleAlertIcon className='h-4 w-4' />
                  )}
                  <span>{rule.label}</span>
                </div>
              ))}
            </div>
          </div>
        )
      case 1:
        return (
          <div className='space-y-4'>
            <Label className='text-base font-semibold'>
              Choose a Template <span className='text-red-500'>*</span>
            </Label>

            {templateLoading ? (
              <div className='grid gap-4 md:grid-cols-2'>
                <Skeleton className='h-full min-h-36 w-full' />
                <Skeleton className='h-full min-h-36 w-full' />
                <Skeleton className='h-full min-h-36 w-full' />
                <Skeleton className='h-full min-h-36 w-full' />
              </div>
            ) : templateError ? (
              <Card>
                <CardContent>
                  <p className='text-muted-foreground'>
                    We couldn&apos;t load templates. Please try again later.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <>
                <RadioGroup
                  defaultValue={selectedTemplate}
                  onValueChange={(value) => setValue("template", value)}
                  className='grid gap-4 sm:grid-cols-2'
                >
                  {templates?.map((template) => (
                    <div key={template._id}>
                      <RadioGroupItem
                        value={template._id}
                        id={template._id}
                        className='peer sr-only'
                      />
                      <Label
                        htmlFor={template._id}
                        className='flex flex-col items-start justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary'
                      >
                        <div className='mb-2 text-lg font-semibold'>
                          {template.name}
                        </div>
                        <p className='text-sm text-muted-foreground'>
                          {template.description}
                        </p>
                      </Label>
                    </div>
                  ))}
                </RadioGroup>

                <p className='text-pretty rounded-md bg-destructive/15 p-4 text-sm leading-5'>
                  <OctagonAlertIcon className='mr-2 inline-block h-4 w-4' />
                  Note: you can choose from these templates later you can easily
                  choose other from dashboard{" "}
                </p>
              </>
            )}
          </div>
        )
      case 2:
        return (
          <ClientSideResumeExtractor
            onExtract={async (data) => {
              setValue("pdfData", JSON.stringify(data))
              trigger("pdfData")
            }}
          />
        )
      default:
        return null
    }
  }

  return (
    <>
      {mutation.isSuccess && <ConfettiEffect />}

      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-h-screen overflow-auto transition-all sm:max-w-[600px]'>
          <DialogHeader>
            <DialogTitle className='text-2xl'>Create Your Website</DialogTitle>
            <DialogDescription>
              Follow these simple steps to set up your new website.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
            <div className='relative mb-8 flex justify-between'>
              {steps.map((step, index) => (
                <div
                  key={step}
                  className={cn(
                    "z-10 flex flex-1 flex-col items-center space-y-2 text-muted-foreground",
                    {
                      "text-primary-foreground": index <= currentStep,
                      "items-start": index === 0,
                      "items-end": index === steps.length - 1,
                    },
                  )}
                >
                  <div
                    className={cn(
                      "flex h-10 w-10 items-center justify-center rounded-full text-sm font-medium",
                      index < currentStep
                        ? "bg-primary text-primary-foreground"
                        : index === currentStep
                          ? "bg-primary text-primary-foreground ring-4 ring-primary ring-opacity-20"
                          : "bg-muted",
                    )}
                  >
                    {index + 1}
                  </div>
                  <span className='text-center text-sm'>{step}</span>
                </div>
              ))}
              {/* Stepper lines */}
              <div className='absolute left-0 top-5 -z-10 flex h-[2px] w-full'>
                {steps.map((_, index) => (
                  <div
                    key={index}
                    className={cn(
                      "h-full flex-1",
                      index < currentStep
                        ? "bg-primary"
                        : index === currentStep
                          ? "bg-gradient-to-r from-primary to-muted"
                          : "bg-muted",
                    )}
                  />
                ))}
              </div>
            </div>
            <div className='min-h-64'> {renderStep()}</div>

            <div className='mt-8 flex justify-end gap-3'>
              <Button
                type='button'
                variant='outline'
                onClick={(e) => {
                  e.preventDefault()
                  currentStep > 0 && setCurrentStep(currentStep - 1)
                }}
                disabled={currentStep === 0}
              >
                Back
              </Button>
              {currentStep < steps.length - 1 ? (
                <Button
                  type='button'
                  onClick={(e) => {
                    e.preventDefault()
                    setCurrentStep(currentStep + 1)
                  }}
                  className='min-w-32'
                  disabled={isNextButtonDisabled()}
                >
                  Next
                </Button>
              ) : (
                <Button
                  type='submit'
                  disabled={mutation.isPending || !isStepValid(currentStep)}
                >
                  {mutation.isPending ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Creating...
                    </>
                  ) : (
                    "Create Website"
                  )}
                </Button>
              )}
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </>
  )
}

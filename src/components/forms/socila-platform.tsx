import { Search } from "lucide-react"
import { FC } from "react"
import { UseFormReturn } from "react-hook-form"

import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export interface SocialPlatform {
  value: string
  label: string
}

interface SocialPlatformSelectProps {
  form: UseFormReturn<any>
  name: string
  platforms: SocialPlatform[]
}

const SocialPlatformSelect: FC<SocialPlatformSelectProps> = ({
  form,
  name,
  platforms,
}) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className='w-full'>
          <FormControl>
            <Select onValueChange={field.onChange} value={field.value}>
              <SelectTrigger className='flex items-center justify-between rounded-lg border p-3 shadow-sm focus:ring-2 focus:ring-primary'>
                <SelectValue placeholder='Select a Social Platform' />
              </SelectTrigger>
              <SelectContent className='max-h-60 overflow-y-auto rounded-md bg-background p-2 shadow-lg'>
                <div className='flex items-center gap-2 border-b p-2'>
                  <Search className='h-4 w-4 opacity-50' />
                  <input
                    type='text'
                    placeholder='Search platform...'
                    className='w-full bg-transparent p-2 text-sm outline-none'
                  />
                </div>
                {platforms.map((platform) => (
                  <SelectItem
                    key={platform.value}
                    value={platform.value}
                    className='flex cursor-pointer items-center gap-2 rounded-md p-3 hover:bg-secondary'
                  >
                    <span className='pl-4 md:pl-6'> {platform.label}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}

export default SocialPlatformSelect

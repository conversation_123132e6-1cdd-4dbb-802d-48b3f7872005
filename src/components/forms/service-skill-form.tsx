"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useEffect, useMemo } from "react"
import { useForm } from "react-hook-form"

import CustomCheckbox from "@/components/custom/checkbox"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { codingLanguageIcons } from "@/constants/public-images"
import { ServiceSkillFormData, serviceSkillSchema } from "@/types/service-skill"

import { FormWrapper } from "../custom/formWrapper"
import { FormFieldWrapper, SelectInput, TextArea, TextInput } from "../form"
import SingleImageSelector from "../gallery/single-image-selector"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import { Slider } from "../ui/slider"

interface ServiceSkillFormProps {
  type: "service" | "skill" | "language"
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: ServiceSkillFormData) => void
  defaultValues?: Partial<ServiceSkillFormData>
}

export function ServiceSkillForm({
  type,
  open,
  onOpenChange,
  onSubmit,
  defaultValues,
}: ServiceSkillFormProps) {
  const initialState: ServiceSkillFormData = useMemo(() => {
    return {
      title: "",
      description: "",
      type: type,
      icon: "",
      percentage: 0,
      price: 1,
      is_enabled: true,
      ...defaultValues,
    }
  }, [defaultValues, type])

  const form = useForm<ServiceSkillFormData>({
    resolver: zodResolver(serviceSkillSchema),
    defaultValues: initialState,
  })

  useEffect(() => {
    form.reset(initialState)
    // eslint-disable-next-line
  }, [open])

  const handleSubmit = (values: ServiceSkillFormData) => {
    if (values.price && typeof values.price === "string") {
      values.price = parseFloat(values.price)
    }
    onSubmit(values)
  }

  const handleReset = () => {
    form.reset(initialState)
  }

  const { watch, handleSubmit: submit, setValue } = form
  const formType = watch("type")

  const handleSkillSelection = (skillId: string) => {
    const selectedSkill = codingLanguageIcons.find(
      (skill) => skill._id === skillId,
    )
    if (selectedSkill) {
      setValue("title", selectedSkill.name)
      setValue("icon", selectedSkill.url)
      setValue("percentage", Math.floor(Math.random() * 11) + 90) // Random value between 90 and 100
    }
  }

  return (
    <FormWrapper
      title={defaultValues ? `Edit ${type}` : `Add ${type}`}
      open={open}
      onOpenChange={onOpenChange}
    >
      <Form {...form}>
        <form onSubmit={submit(handleSubmit)} className='space-y-6'>
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            {/* Skill Selection Dropdown */}
            {(formType === "skill" || formType === "language") && (
              <FormItem className='col-span-full'>
                <FormLabel>Select to Autofill</FormLabel>
                <FormControl>
                  <Select onValueChange={handleSkillSelection}>
                    <SelectTrigger className='w-full'>
                      <SelectValue placeholder='Select a skill or technology to autofill' />
                    </SelectTrigger>
                    <SelectContent>
                      {codingLanguageIcons.map((skill) => (
                        <SelectItem key={skill._id} value={skill._id}>
                          {skill.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}

            <TextInput
              control={form.control}
              name='title'
              label={type === "language" ? "Name" : "Title"}
              placeholder={type === "language" ? "Name" : "Title"}
              className='col-span-full'
              required
            />

            <TextArea
              control={form.control}
              name='description'
              label='Description'
              placeholder='Description (max 500 characters)'
              className={`col-span-full ${formType === "service" ? "" : "hidden"}`}
              rows={4}
            />

            <div className='col-span-full flex items-center justify-center gap-3'>
              <SelectInput
                control={form.control}
                name='type'
                label='Type'
                options={[
                  { value: "skill", label: "Skill" },
                  { value: "service", label: "Service" },
                  { value: "language", label: "Language" },
                ]}
                hidden={type ? true : false}
              />

              <TextInput
                control={form.control}
                name='price'
                label='Price'
                placeholder='Price'
                type='number'
                className={`col-span-full ${formType === "service" ? "" : "hidden"}`}
              />

              <FormFieldWrapper
                name='percentage'
                label='Percentage'
                control={form.control}
              >
                {(field) => (
                  <div className='relative'>
                    <Slider
                      className='rounded-md border p-4'
                      value={[field.value ?? 0]}
                      onValueChange={(val) => field.onChange(val[0])}
                      max={100}
                      step={1}
                    />
                    <span className='absolute right-0 top-0 text-xs'>
                      {field.value ?? 0}%
                    </span>
                  </div>
                )}
              </FormFieldWrapper>
            </div>

            {/* Icon Field */}
            <FormField
              control={form.control}
              name='icon'
              render={({ field }) => (
                <FormItem className='relative col-span-full'>
                  <FormLabel>Icon</FormLabel>
                  <FormControl>
                    <SingleImageSelector
                      uploadedImage={field.value ?? ""}
                      setUploadedImage={field.onChange}
                      className='w-full object-contain'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Enable Checkbox */}
            <FormField
              control={form.control}
              name='is_enabled'
              render={({ field }) => (
                <FormItem className='col-span-full'>
                  <FormControl>
                    <CustomCheckbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      label='Enable'
                      description='Enable this service/skill'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Action Buttons */}
          <div className='sticky bottom-0 flex flex-wrap justify-end gap-4 bg-background py-3 pt-5'>
            <div className='flex w-full gap-2 md:w-auto md:gap-4'>
              <Button
                type='button'
                variant='destructive'
                className='flex-1'
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type='button'
                variant='white'
                className='flex-1'
                onClick={handleReset}
              >
                Reset Form
              </Button>
            </div>

            <Button type='submit' className='w-full flex-1'>
              {defaultValues ? "Update" : "Create"} {type}
            </Button>
          </div>
        </form>
      </Form>
    </FormWrapper>
  )
}

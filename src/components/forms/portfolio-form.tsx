"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"

import ArrayInput from "@/components/custom/array-input" // Custom ArrayInput component
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { getPortfolioSampleData } from "@/data/portfolio/portfolio-samples"
import { useAITextGeneration } from "@/hooks/use-ai-text-generation"
import { PortfolioFormData, portfolioSchema } from "@/types/portfolio"

import { FormWrapper } from "../custom/formWrapper"
import { Checkbox, FormFieldWrapper, TextArea, TextInput } from "../form"
import MultipleImageSelector from "../gallery/multiple-image-selector"

interface ProjectFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: PortfolioFormData) => void
  defaultValues?: Partial<PortfolioFormData>
}

export function PortfolioForm({
  open,
  onOpenChange,
  onSubmit,
  defaultValues,
}: ProjectFormProps) {
  const { isGenerating, aiError, handleAITextGeneration } =
    useAITextGeneration()

  const form = useForm<PortfolioFormData>({
    resolver: zodResolver(portfolioSchema),
    defaultValues: {
      title: "",
      description: "",
      images: [],
      external_links: {
        live_url: "",
        repository_url: "",
      },
      order: 1,
      tags: [],
      categories: [],
      is_enabled: true,
      ...defaultValues,
    },
  })

  // Effect to update form values when defaultValues changes
  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues) // Reset the form when defaultValues change
    }
  }, [defaultValues, form])

  // Function to handle AI-generated or rewritten description
  const handleAIDescription = async () => {
    const title = form.getValues("title") // Get the current title from the form
    const description = form.getValues("description") // Get the current description

    handleAITextGeneration(
      description || title, // Use description if it exists, otherwise use title
      description
        ? "Rewrite the following description to make it more engaging. keep it minimum 150-300 words. and under 900 characters."
        : "Generate a concise and engaging project description for my project. keep it minimum 150-300 words. and under 900 characters.",
      (generatedText) => {
        // Update the description field with the AI-generated or rewritten text
        form.setValue("description", generatedText)
      },
    )
  }

  // Function to fill sample details
  const handleFillSampleDetails = () => {
    const sampleData = getPortfolioSampleData()
    // Reset the form with the sample data
    form.reset(sampleData)
    toast.info("Sample Data Loaded", {
      description: "The form has been populated with random sample data.",
    })
  }

  const handleSubmit = (values: PortfolioFormData) => {
    console.log(values)
    onSubmit(values)
  }

  const handleReset = () => {
    form.reset({
      title: "",
      description: "",
      images: [],
      external_links: {
        live_url: "",
        repository_url: "",
      },
      order: 1,
      tags: [],
      categories: [],
      is_enabled: true,
    })
    toast.success("Form Reset", {
      description: "The form has been reset to its initial state.",
    })
  }

  return (
    <FormWrapper
      title={defaultValues ? "Edit Project" : "Add Project"}
      open={open}
      onOpenChange={onOpenChange}
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            <div className='col-span-full flex items-center justify-center gap-3'>
              <TextInput
                control={form.control}
                name='title'
                label='Title'
                placeholder='Project title'
                required
              />

              <TextInput
                control={form.control}
                name='order'
                label='Order/Sequence'
                placeholder='Order/Sequence'
                type='number'
                required
              />
            </div>

            <TextArea
              control={form.control}
              name='description'
              label='Description'
              placeholder='Project description (max 800 characters)'
              className='col-span-full'
              rows={8}
              enableAI
              handleAIDescription={handleAIDescription}
              isGenerating={isGenerating}
              aiError={aiError}
              required
            />

            <TextInput
              control={form.control}
              name='external_links.live_url'
              label='Live URL'
              placeholder='https://...'
              required
            />

            <TextInput
              control={form.control}
              name='external_links.repository_url'
              label='Repository URL'
              placeholder='https://github.com/...'
              required
            />

            <FormFieldWrapper
              control={form.control}
              name='tags'
              label='Bullet Points'
            >
              {(field) => (
                <ArrayInput
                  value={field.value ?? []}
                  onChange={field.onChange}
                  placeholder='Add tags and press Enter'
                />
              )}
            </FormFieldWrapper>

            <FormFieldWrapper
              control={form.control}
              name='categories'
              label='Categories'
            >
              {(field) => (
                <ArrayInput
                  value={field.value ?? []}
                  onChange={field.onChange}
                  placeholder='Add Categories and press Enter'
                />
              )}
            </FormFieldWrapper>

            <Checkbox
              control={form.control}
              name='is_enabled'
              label='Show on Site'
              className='col-span-full'
              description='Do you want to show this Project on the site?'
            />
          </div>

          <FormFieldWrapper
            control={form.control}
            name='images'
            label='Project Images (Max 10)'
          >
            {(field) => (
              <MultipleImageSelector
                uploadedImages={field.value}
                setUploadedImages={field.onChange}
                className='h-32 md:h-40'
              />
            )}
          </FormFieldWrapper>

          {/* Action Buttons */}
          <div className='sticky bottom-0 z-10 flex flex-wrap justify-end gap-4 bg-background py-3 pt-5 md:pt-10'>
            <div className='flex w-full gap-2 md:w-auto md:gap-4'>
              <Button
                type='button'
                variant='destructive'
                className='flex-1'
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type='button'
                variant='outline'
                className='flex-1'
                onClick={handleReset}
              >
                Reset Form
              </Button>
              <Button
                type='button'
                variant='white'
                className='flex-1'
                onClick={handleFillSampleDetails}
              >
                Fill Sample
              </Button>
            </div>

            <Button type='submit' className='w-full flex-1'>
              {defaultValues ? "Update" : "Create"} Project
            </Button>
          </div>
        </form>
      </Form>
    </FormWrapper>
  )
}

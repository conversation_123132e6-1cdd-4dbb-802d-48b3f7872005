"use client"

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { EditIcon, Loader2Icon, PlusIcon, TrashIcon } from "lucide-react"
import { useEffect } from "react"
import { useFieldArray, useForm } from "react-hook-form"
import { toast } from "sonner"

import { createOrUpdateAbout } from "@/actions/about"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { socialPlatforms } from "@/constants/social"
import { useAITextGeneration } from "@/hooks/use-ai-text-generation"
// import { IconEdit, IconLoader2, IconPlus, IconTrash } from "@tabler/icons-react"
// import ImageUploader from "@/components/custom/image-uploader";
import { About, aboutSchema } from "@/types/about"

import CustomToolTip from "../custom/tooltip-wrapper"
import { TextArea, TextInput } from "../form"
import SingleImageSelector from "../gallery/single-image-selector"
import SocialPlatformSelect from "./socila-platform"

interface EditAboutFormProps {
  // setEditDetails: React.Dispatch<React.SetStateAction<boolean>>;
  handleToggleEdit: () => void
  initialData: About
  website: string
  // onSave: (data: About) => void;
  fillSampleData: () => void
}

const EditAboutForm = ({
  handleToggleEdit,
  initialData,
  website,
  fillSampleData,
}: EditAboutFormProps) => {
  const queryClient = useQueryClient()
  const { isGenerating, aiError, handleAITextGeneration } =
    useAITextGeneration()

  const form = useForm<About>({
    resolver: zodResolver(aboutSchema),
    defaultValues: initialData,
  })

  // Add this useEffect to update form when initialData changes
  useEffect(() => {
    if (initialData) {
      form.reset(initialData)
    }
  }, [initialData, form])

  const {
    fields: socialFields,
    append: appendSocial,
    remove: removeSocial,
  } = useFieldArray({
    control: form.control,
    name: "social_links",
  } as never)

  const mutation = useMutation({
    mutationFn: (data: About) => createOrUpdateAbout(website, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["about", website] })
      toast.success("About saved successfully!", {
        description: "Your about details have been saved successfully.",
      })
    },
    onError: () => {
      toast.error("Oops! Something went wrong.", {
        description: "Failed to save your about details. Please try again.",
      })
    },
  })

  function onSubmit(values: About) {
    console.log("Form data:", values)
    mutation.mutate(values)
    console.log("Form data:", values)

    handleToggleEdit()
  }

  // Function to handle AI-generated or rewritten description
  const handleAIDescription = async () => {
    const title = form.getValues("title")
    const subTitle = form.getValues("sub_title")
    const description = form.getValues("bio")

    handleAITextGeneration(
      title + subTitle, // Use description if it exists, otherwise use title
      description
        ? "Rewrite the following bio to make it more engaging and concise. keep it around 150-300 words."
        : "Generate a concise and engaging bio for me. keep it around 150-300 words.",
      (generatedText) => {
        // Update the description field with the AI-generated or rewritten text
        form.setValue("bio", generatedText)
      },
    )
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className='-mt-10 px-5 pb-36'
      >
        {/* <div className="mb-5 flex w-full flex-wrap items-center justify-between"> */}
        <div className='flex w-full flex-wrap items-center justify-between gap-5 pt-5 md:gap-10'>
          <div className='relative flex flex-[1] -translate-y-6 items-center gap-5'>
            <div className='relative'>
              <FormField
                control={form.control}
                name={`avatar`}
                render={({ field }) => (
                  <FormItem className='w-max flex-1'>
                    <FormControl>
                      <div className='relative'>
                        <SingleImageSelector
                          uploadedImage={field.value ?? ""}
                          setUploadedImage={field.onChange}
                          size='large'
                          rounded
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-10 flex flex-col gap-2'>
              <TextInput
                control={form.control}
                name='first_name'
                label='First Name'
                placeholder='First name'
                required
              />

              <TextInput
                control={form.control}
                name='middle_name'
                label='Middle Name'
                placeholder='Middle name'
              />
              <TextInput
                control={form.control}
                name='last_name'
                label='Last Name'
                placeholder='Last name'
                required
              />
            </div>
          </div>
        </div>

        <Separator />

        <div className='mt-8 grid grid-cols-1 gap-10 md:grid-cols-2'>
          <div className='flex flex-col gap-5'>
            <Button variant={"white"} className='mb-3 block'>
              About Me
            </Button>

            <TextInput
              control={form.control}
              name='title'
              label='Designation'
              placeholder='Your Designation'
              required
            />

            <TextInput
              control={form.control}
              name='sub_title'
              label='Subtitle'
              placeholder='Your subtitle'
              required
            />

            <TextArea
              control={form.control}
              name='bio'
              label='Bio'
              placeholder='Your bio'
              rows={8}
              enableAI
              handleAIDescription={handleAIDescription}
              isGenerating={isGenerating}
              aiError={aiError}
              required
            />

            <TextInput
              control={form.control}
              name='quote'
              label='Quote'
              placeholder='Your quote'
              required
            />
          </div>

          <div className='flex flex-col gap-5'>
            <div className='grid justify-between gap-4 lg:grid-cols-2'>
              <TextInput
                control={form.control}
                name='address.street'
                label='Street'
                placeholder='Street'
              />

              <TextInput
                control={form.control}
                name='address.city'
                label='City'
                placeholder='City'
              />
            </div>
            <div className='grid flex-wrap justify-between gap-4 md:grid-cols-2 lg:grid-cols-3'>
              <TextInput
                control={form.control}
                name='address.state'
                label='State'
                placeholder='State'
              />

              <TextInput
                control={form.control}
                name='address.country'
                label='Country'
                placeholder='Country'
              />

              <TextInput
                control={form.control}
                name='address.zip_code'
                label='Zip Code'
                placeholder='Zip Code'
              />
            </div>
            <Separator />
            <div className='grid items-start justify-between gap-4 lg:grid-cols-2'>
              <TextInput
                control={form.control}
                name='phone_number'
                label='Phone'
                placeholder='Your phone number'
              />

              <TextInput
                control={form.control}
                name='contact_email'
                label='Email'
                placeholder='Your email'
              />
            </div>
            <div className='grid items-start justify-between gap-4 lg:grid-cols-3'>
              <TextInput
                control={form.control}
                name='years_of_experience'
                label='Years of Experience'
                placeholder='Years of Experience'
                type='number'
                required
              />

              <TextInput
                control={form.control}
                name='total_projects'
                label='Total Projects'
                placeholder='Total Projects'
                type='number'
                required
              />

              <TextInput
                control={form.control}
                name='total_clients'
                label='Clients'
                placeholder='Clients'
                type='number'
                required
              />
            </div>
            <div>
              <h3 className='mb-2 font-medium'>Social Links</h3>
              {socialFields.map((field, index) => (
                <div
                  key={field.id}
                  className='flex items-center gap-2 border-b border-input py-2'
                >
                  <FormField
                    control={form.control}
                    name={`social_links.${index}.icon`}
                    render={({ field }) => (
                      <FormItem className='w-max'>
                        {/* <FormLabel>Image</FormLabel> */}
                        <FormControl>
                          <div className='relative'>
                            <SingleImageSelector
                              uploadedImage={field.value ?? ""}
                              setUploadedImage={field.onChange}
                              size='icon'
                              rounded
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className='flex w-full flex-col items-center justify-center gap-2'>
                    <SocialPlatformSelect
                      form={form}
                      name={`social_links.${index}.platform`}
                      platforms={socialPlatforms}
                    />

                    <FormField
                      control={form.control}
                      name={`social_links.${index}.url`}
                      render={({ field }) => (
                        <FormItem className='w-full flex-1'>
                          <FormControl>
                            <Input placeholder='URL' {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <CustomToolTip content='Remove social link'>
                    <Button
                      type='button'
                      variant='destructive'
                      size='icon'
                      onClick={() => removeSocial(index)}
                    >
                      <TrashIcon className='h-4 w-4' />
                    </Button>
                  </CustomToolTip>
                </div>
              ))}
              <CustomToolTip content='Add New Social Link'>
                <Button
                  type='button'
                  variant='secondary'
                  size='sm'
                  className='mt-2'
                  onClick={() =>
                    appendSocial({ platform: "", url: "", icon: "" })
                  }
                >
                  <PlusIcon className='mr-2 h-4 w-4' />
                  Add Social Link
                </Button>
              </CustomToolTip>
            </div>
          </div>
        </div>

        <div className='sticky bottom-0 z-30 mt-10 flex items-center justify-center gap-3 bg-background px-4 py-3'>
          <div className='flex flex-1 items-center justify-center gap-3'>
            <Button
              variant='destructive'
              className='flex-1 gap-1'
              // onClick={() => setEditDetails(false)}
              onClick={handleToggleEdit}
              type='button'
            >
              <EditIcon size={20} />
              Close Edit
            </Button>
            <Button
              variant='white'
              className='flex-1 gap-1'
              onClick={fillSampleData}
              type='button'
            >
              <EditIcon size={20} />
              Fill Sample Data
            </Button>
          </div>

          <Button className='w-full flex-1' type='submit'>
            {mutation.isPending ? (
              <>
                <Loader2Icon className='mr-2 animate-spin' /> Saving...
              </>
            ) : (
              "Save changes"
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}

export default EditAboutForm

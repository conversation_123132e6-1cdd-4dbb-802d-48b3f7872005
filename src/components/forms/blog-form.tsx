"use client"

// import "@/styles/quill-custom.css"
import "react-quill/dist/quill.snow.css"

import { zodResolver } from "@hookform/resolvers/zod"
import dynamic from "next/dynamic"
import { useMemo } from "react"
import { useForm } from "react-hook-form"

import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { sampleBlogData } from "@/constants/sample-blog-data"
import { type BlogFormData, blogSchema } from "@/types/blog"

import ArrayInput from "../custom/array-input"
import { Checkbox, FormFieldWrapper, TextArea, TextInput } from "../form"
import SingleImageSelector from "../gallery/single-image-selector"

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false })

const formSchema = blogSchema.omit({
  _id: true,
  createdAt: true,
  updatedAt: true,
  last_edited: true,
})

interface BlogFormProps {
  onSubmit: (data: BlogFormData) => void
  defaultValues?: Partial<BlogFormData>
  websiteId: string
}

const modules = {
  toolbar: [
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    ["bold", "italic", "underline", "strike"],
    [{ list: "ordered" }, { list: "bullet" }],
    [{ indent: "-1" }, { indent: "+1" }],
    [{ align: [] }],
    ["link", "image", "blockquote", "code-block"],
    [{ color: [] }, { background: [] }],
    ["clean"],
  ],
}

const formats = [
  "header",
  "bold",
  "italic",
  "underline",
  "strike",
  "list",
  "bullet",
  "indent",
  "align",
  "link",
  "image",
  "blockquote",
  "code-block",
  "color",
  "background",
]

export function BlogForm({
  websiteId,
  onSubmit,
  defaultValues,
}: BlogFormProps) {
  const initialState: BlogFormData = useMemo(() => {
    return {
      title: "",
      slug: "",
      content: "",
      excerpt: "",
      tags: [],
      thumbnail: "",
      is_published: false,
      reading_time: "",
      featured: false,
      meta_title: "",
      meta_description: "",
      is_enabled: true,
      website: websiteId,
      ...defaultValues,
    }
  }, [defaultValues, websiteId])

  const form = useForm<BlogFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: initialState,
  })

  const handleSubmit = (values: BlogFormData) => {
    onSubmit(values)
  }

  const handleReset = () => {
    form.reset(initialState)
  }

  const handleFillSampleData = () => {
    form.reset(sampleBlogData)
  }

  return (
    <div className='w-full rounded-lg border border-border bg-background p-6 shadow-lg'>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-8'>
          <div className='grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3'>
            <TextInput
              control={form.control}
              name='title'
              label='Title'
              placeholder='Blog post title'
              required
              className='col-span-full md:col-span-2 lg:col-span-1'
            />

            <TextInput
              control={form.control}
              name='slug'
              label='Slug'
              placeholder='URL-friendly slug'
              className='col-span-full md:col-span-2 lg:col-span-1'
            />

            <div className='col-span-full'>
              <FormFieldWrapper
                control={form.control}
                name='content'
                label='Content'
                required
              >
                {(field) => (
                  <ReactQuill
                    theme='snow'
                    modules={modules}
                    formats={formats}
                    value={field.value}
                    onChange={field.onChange}
                    className='h-[500px] rounded-md border border-input bg-background'
                    placeholder='Write your blog content here...'
                  />
                )}
              </FormFieldWrapper>
            </div>

            <TextArea
              control={form.control}
              name='excerpt'
              label=''
              placeholder='Short summary of the blog post (appears in blog listings)'
              className='col-span-full mt-10'
              rows={4}
            />

            <div className='col-span-full'>
              <FormFieldWrapper label='Tags' control={form.control} name='tags'>
                {(field) => (
                  <ArrayInput
                    value={field.value ?? []}
                    onChange={field.onChange}
                    placeholder='Add bullet point and press enter'
                  />
                )}
              </FormFieldWrapper>
            </div>

            <div className='col-span-full'>
              <FormFieldWrapper
                control={form.control}
                name='thumbnail'
                label='Thumbnail'
                required
              >
                {(field) => (
                  <SingleImageSelector
                    uploadedImage={field.value ?? ""}
                    setUploadedImage={field.onChange}
                    className='w-full rounded-md border p-4'
                  />
                )}
              </FormFieldWrapper>
            </div>

            <TextInput
              control={form.control}
              name='reading_time'
              label='Reading Time'
              placeholder='e.g., 5 min read'
              className='col-span-full md:col-span-1'
            />

            <TextInput
              control={form.control}
              name='meta_title'
              label='Meta Title'
              placeholder='SEO meta title'
              className='col-span-full md:col-span-1'
            />

            <TextArea
              control={form.control}
              name='meta_description'
              label='Meta Description'
              placeholder='SEO meta description'
              className='col-span-full'
            />

            <Checkbox
              control={form.control}
              name='featured'
              label='Featured'
              className='col-span-full md:col-span-1'
            />

            <Checkbox
              control={form.control}
              name='is_published'
              label='Published'
              className='col-span-full md:col-span-1'
            />

            <Checkbox
              control={form.control}
              name='is_enabled'
              label='Enabled'
              className='col-span-full md:col-span-1'
            />
          </div>

          <div className='sticky bottom-0 flex flex-wrap justify-end gap-4 border-t bg-background/80 px-2 py-4 backdrop-blur-sm'>
            <Button type='button' variant='outline' onClick={handleReset}>
              Reset Form
            </Button>
            <Button
              type='button'
              variant='white'
              onClick={handleFillSampleData}
            >
              Fill Sample Data
            </Button>

            <Button type='submit' className='w-full flex-1'>
              {defaultValues ? "Update" : "Create"} Blog Post
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

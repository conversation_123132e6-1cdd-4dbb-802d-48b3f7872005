"use client"

import { <PERSON><PERSON><PERSON><PERSON>, FileText, Loader2, Upload, X } from "lucide-react"
import <PERSON>rip<PERSON> from "next/script"
import { useEffect, useState } from "react"
import { useDropzone } from "react-dropzone"
import { toast } from "sonner"

import { generateText } from "@/actions/text-generation"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

declare global {
  interface Window {
    pdfjsLib: any
  }
}

interface ClientSideResumeExtractorProps {
  onExtract: (data: string) => void
}

export function ClientSideResumeExtractor({
  onExtract,
}: ClientSideResumeExtractorProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isExtracting, setIsExtracting] = useState(false)
  // const [extractedText, setExtractedText] = useState<string | null>(null)
  const [refinedText, setRefinedText] = useState<string | null>(null)
  const [isPdfJsReady, setIsPdfJsReady] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)

  useEffect(() => {
    // Initialize PDF.js when the script is loaded
    if (typeof window !== "undefined" && window.pdfjsLib) {
      window.pdfjsLib.GlobalWorkerOptions.workerSrc =
        "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.10.377/pdf.worker.min.js"
      setIsPdfJsReady(true)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: { "application/pdf": [".pdf"] },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      setFile(acceptedFiles[0])
      handleExtract(acceptedFiles[0]) // Automatically extract data
    },
  })

  const extractTextFromPdf = async (file: File): Promise<string> => {
    if (!window.pdfjsLib) {
      throw new Error("PDF.js not loaded")
    }

    const arrayBuffer = await file.arrayBuffer()
    const pdf = await window.pdfjsLib.getDocument({ data: arrayBuffer }).promise
    let fullText = ""

    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i)
      const textContent = await page.getTextContent()
      const pageText = textContent.items.map((item: any) => item.str).join(" ")
      fullText += pageText + "\n"
    }

    return fullText
  }

  const handleExtract = async (file: File) => {
    if (!file) return

    setIsExtracting(true)
    try {
      const text = await extractTextFromPdf(file)
      // setExtractedText(text)

      // Call generateText to refine the extracted text
      await generateRefinedText(text)
    } catch (error) {
      console.error("Error extracting resume data:", error)
      toast.error("Failed to extract resume data. Please try again.")
    } finally {
      setIsExtracting(false)
    }
  }

  const generateRefinedText = async (text: string) => {
    setIsGenerating(true)
    try {
      const instruction = `
      Generate a **detailed and structured summary** of the provided data in **plain text format**.
      - Do **not** use Markdown, HTML, or special formatting.
      - Expand on the information with relevant insights and details.
      - Clearly categorize **projects, skills (technologies only), experience, education, and other key sections**.
      - Ensure **skills include specific technologies** like HTML, CSS, React, AWS, etc., rather than generic terms.
      - The output should be **comprehensive, well-organized, and informative**.
    `

      const result = await generateText({
        text,
        instruction,
        checkRegex: false,
      })

      if (result.error) {
        toast.error(result.error)
      } else {
        setRefinedText(result.response ?? "")
        toast.success("Resume/CV processed successfully!", {
          description: "Now Click Create Website to continue.",
        })

        // Pass the refined text to the onExtract function
        onExtract(result.response ?? "")
      }
    } catch (error) {
      console.error("Error generating refined text:", error)
      toast.error("Failed to parse resume data. Please try again.")
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <>
      <Script
        src='https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.10.377/pdf.min.js'
        onLoad={() => {
          if (typeof window !== "undefined" && window.pdfjsLib) {
            window.pdfjsLib.GlobalWorkerOptions.workerSrc =
              "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.10.377/pdf.worker.min.js"
            setIsPdfJsReady(true)
          }
        }}
      />
      <div className='space-y-6 p-0'>
        <Card className='bg-background/10 shadow-md'>
          <CardHeader className='border-b pb-4'>
            <CardTitle className='text-xl font-semibold'>
              Upload Resume/CV <small>(optional)</small>
            </CardTitle>
            <CardDescription className='text-gray-600'>
              Upload your PDF resume to extract information
            </CardDescription>
          </CardHeader>
          <CardContent className='py-6'>
            <div
              {...getRootProps()}
              className={`cursor-pointer rounded-lg border-2 border-dashed p-6 text-center transition-colors ${
                isDragActive
                  ? "border-primary bg-blue-50"
                  : "border-gray-300 hover:border-primary"
              }`}
            >
              <input {...getInputProps()} />
              {file ? (
                <div className='flex flex-col items-center'>
                  <FileText className='mb-2 h-12 w-12 text-blue-500' />
                  <span className='truncate text-sm text-primary-foreground'>
                    {file.name}
                  </span>
                  <Button
                    variant='destructive'
                    size='sm'
                    className='mt-2'
                    onClick={(e) => {
                      e.stopPropagation()
                      setFile(null)
                      // setExtractedText(null)
                      setRefinedText(null)
                    }}
                  >
                    Remove File
                    <X className='ml-1 h-4 w-4' />
                  </Button>
                </div>
              ) : isDragActive ? (
                <p className='text-gray-600'>Drop the PDF file here</p>
              ) : (
                <div className='flex flex-col items-center'>
                  <Upload className='mb-2 h-12 w-12 text-gray-400' />
                  <p className='text-gray-600'>
                    Drag & drop a PDF file here, or click to select one
                  </p>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className='justify-between border-t pt-4'>
            {!isPdfJsReady && (
              <p className='text-sm text-gray-500'>Loading PDF.js library...</p>
            )}
            {isExtracting && (
              <div className='flex items-center text-gray-600'>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Extracting resume data...
              </div>
            )}
            {isGenerating && (
              <div className='flex items-center text-gray-600'>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Processing Resume/CV...
              </div>
            )}
            {refinedText && (
              <div className='flex items-center text-green-400'>
                <CheckCircle className='mr-2 h-4 w-4' />
                Resume/CV Processed successfully!
              </div>
            )}
          </CardFooter>
        </Card>

        {/* don't show for now */}
        {/* {refinedText && (
          <Card className="shadow-md">
            <CardHeader className="border-b pb-4">
              <CardTitle className="text-xl font-semibold">
                Refined Text
              </CardTitle>
            </CardHeader>
            <CardContent className="py-6">
              <Textarea
                value={refinedText}
                onChange={(e) => setRefinedText(e.target.value)}
                className="w-full h-[300px] p-4 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Your refined text will appear here..."
              />
            </CardContent>
          </Card>
        )} */}
      </div>
    </>
  )
}

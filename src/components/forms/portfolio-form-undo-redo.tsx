// "use client";

// import React, { useCallback, useEffect } from "react";
// import { useForm, useFieldArray } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import * as z from "zod";
// import { Button } from "@/components/ui/button";
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
// } from "@/components/ui/dialog";
// import {
//   Form,
//   FormControl,
//   FormField,
//   FormItem,
//   FormLabel,
//   FormMessage,
// } from "@/components/ui/form";
// import { Input } from "@/components/ui/input";
// import { Textarea } from "@/components/ui/textarea";
// import { UndoRedoForm } from "./undo-redo-form";

// const formSchema = z.object({
//   title: z.string().min(1, "Title is required"),
//   description: z.string().optional(),
//   images: z.array(z.string().url()).optional(),
//   external_links: z.object({
//     live_url: z.string().url().optional(),
//     repository_url: z.string().url().optional(),
//   }),
//   order: z.number().min(0),
//   tags: z.array(z.string()).optional(),
//   is_enabled: z.boolean(),
// });

// type FormValues = z.infer<typeof formSchema>;

// interface PortfolioFormProps {
//   open: boolean;
//   onOpenChange: (open: boolean) => void;
//   onSubmit: (data: FormValues) => void;
//   defaultValues?: Partial<FormValues>;
// }

// export function PortfolioForm({
//   open,
//   onOpenChange,
//   onSubmit,
//   defaultValues,
// }: PortfolioFormProps) {
//   const initialState: FormValues = {
//     title: "",
//     description: "",
//     images: [],
//     external_links: {
//       live_url: "",
//       repository_url: "",
//     },
//     order: 0,
//     tags: [],
//     is_enabled: true,
//     ...defaultValues,
//   };

//   const renderForm = useCallback(
//     (
//       state: FormValues,
//       setState: (newState: FormValues | ((draft: FormValues) => void)) => void
//     ) => {
//       const form = useForm<FormValues>({
//         resolver: zodResolver(formSchema),
//         defaultValues: state,
//         mode: "onChange",
//       });

//       const {
//         fields: imageFields,
//         append: appendImage,
//         remove: removeImage,
//       } = useFieldArray({
//         control: form.control,
//         name: "images",
//       });

//       useEffect(() => {
//         form.reset(state);
//       }, [state, form]);

//       const handleSubmit = (values: FormValues) => {
//         setState(values);
//         onSubmit(values);
//       };

//       const syncFieldChange = (fieldName: keyof FormValues, value: any) => {
//         setState((draft) => {
//           draft[fieldName] = value;
//         });
//       };

//       return (
//         <Form {...form}>
//           <form
//             onSubmit={form.handleSubmit(handleSubmit)}
//             className="space-y-6"
//           >
//             {/* Title */}
//             <FormField
//               control={form.control}
//               name="title"
//               render={({ field }) => (
//                 <FormItem>
//                   <FormLabel>
//                     Title <span className="text-destructive">*</span>
//                   </FormLabel>
//                   <FormControl>
//                     <Input
//                       placeholder="Portfolio title"
//                       {...field}
//                       onChange={(e) => {
//                         field.onChange(e);
//                         syncFieldChange("title", e.target.value);
//                       }}
//                     />
//                   </FormControl>
//                   <FormMessage />
//                 </FormItem>
//               )}
//             />

//             {/* Description */}
//             <FormField
//               control={form.control}
//               name="description"
//               render={({ field }) => (
//                 <FormItem>
//                   <FormLabel>Description</FormLabel>
//                   <FormControl>
//                     <Textarea
//                       placeholder="Portfolio description"
//                       className="resize-none"
//                       {...field}
//                       onChange={(e) => {
//                         field.onChange(e);
//                         syncFieldChange("description", e.target.value);
//                       }}
//                     />
//                   </FormControl>
//                   <FormMessage />
//                 </FormItem>
//               )}
//             />

//             {/* Images */}
//             <div>
//               <FormLabel>Images</FormLabel>
//               <div className="space-y-2">
//                 {imageFields.map((field, index) => (
//                   <div key={field.id} className="flex items-center gap-2">
//                     <Input
//                       placeholder="Image URL"
//                       {...form.register(`images.${index}`)}
//                     />
//                     <Button
//                       type="button"
//                       variant="destructive"
//                       onClick={() => removeImage(index)}
//                     >
//                       Remove
//                     </Button>
//                   </div>
//                 ))}
//                 <Button
//                   type="button"
//                   onClick={() => appendImage("")}
//                   disabled={imageFields.length >= 20}
//                 >
//                   Add Image
//                 </Button>
//               </div>
//             </div>

//             {/* External Links */}
//             <div className="grid grid-cols-2 gap-4">
//               <FormField
//                 control={form.control}
//                 name="external_links.live_url"
//                 render={({ field }) => (
//                   <FormItem>
//                     <FormLabel>Live URL</FormLabel>
//                     <FormControl>
//                       <Input placeholder="https://example.com" {...field} />
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 )}
//               />
//               <FormField
//                 control={form.control}
//                 name="external_links.repository_url"
//                 render={({ field }) => (
//                   <FormItem>
//                     <FormLabel>Repository URL</FormLabel>
//                     <FormControl>
//                       <Input
//                         placeholder="https://github.com/example"
//                         {...field}
//                       />
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 )}
//               />
//             </div>

//             {/* Order */}
//             <FormField
//               control={form.control}
//               name="order"
//               render={({ field }) => (
//                 <FormItem>
//                   <FormLabel>Order</FormLabel>
//                   <FormControl>
//                     <Input
//                       type="number"
//                       min="0"
//                       placeholder="0"
//                       {...field}
//                       onChange={(e) => {
//                         field.onChange(e);
//                         syncFieldChange("order", Number(e.target.value));
//                       }}
//                     />
//                   </FormControl>
//                   <FormMessage />
//                 </FormItem>
//               )}
//             />

//             {/* Tags */}
//             <div>
//               <FormLabel>Tags</FormLabel>
//               <div className="space-y-2">
//                 {form.watch("tags")?.map((tag, index) => (
//                   <div key={index} className="flex items-center gap-2">
//                     <Input
//                       placeholder="Tag"
//                       {...form.register(`tags.${index}`)}
//                     />
//                     <Button
//                       type="button"
//                       variant="destructive"
//                       onClick={() =>
//                         setState((draft) => {
//                           draft.tags?.splice(index, 1);
//                         })
//                       }
//                     >
//                       Remove
//                     </Button>
//                   </div>
//                 ))}
//                 <Button
//                   type="button"
//                   onClick={() =>
//                     setState((draft) => {
//                       draft.tags?.push("");
//                     })
//                   }
//                 >
//                   Add Tag
//                 </Button>
//               </div>
//             </div>

//             {/* Enable Checkbox */}
//             <FormField
//               control={form.control}
//               name="is_enabled"
//               render={({ field }) => (
//                 <FormItem>
//                   <FormLabel>Enabled</FormLabel>
//                   <FormControl>
//                     <Input
//                       type="checkbox"
//                       checked={field.value}
//                       onChange={(e) => {
//                         field.onChange(e.target.checked);
//                         syncFieldChange("is_enabled", e.target.checked);
//                       }}
//                     />
//                   </FormControl>
//                   <FormMessage />
//                 </FormItem>
//               )}
//             />

//             {/* Submit Buttons */}
//             <div className="flex justify-end gap-4">
//               <Button
//                 type="button"
//                 variant="outline"
//                 onClick={() => onOpenChange(false)}
//               >
//                 Cancel
//               </Button>
//               <Button type="submit">
//                 {defaultValues ? "Update" : "Create"} Portfolio
//               </Button>
//             </div>
//           </form>
//         </Form>
//       );
//     },
//     [onOpenChange, onSubmit]
//   );

//   return (
//     <Dialog open={open} onOpenChange={onOpenChange}>
//       <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
//         <DialogHeader>
//           <DialogTitle>
//             {defaultValues ? "Edit Portfolio" : "Add Portfolio"}
//           </DialogTitle>
//         </DialogHeader>
//         <UndoRedoForm
//           initialState={initialState}
//           onStateChange={(state) => console.log("Form state changed:", state)}
//         >
//           {renderForm}
//         </UndoRedoForm>
//       </DialogContent>
//     </Dialog>
//   );
// }

const PortfolioUndoRedoForm = () => {
  return <div>PortfolioUndoRedoForm</div>
}

export default PortfolioUndoRedoForm

import { RotateCcw as UndoIcon, RotateCw as RedoIcon } from "lucide-react"
import React, { useEffect } from "react"

import { Button } from "@/components/ui/button"
import { useUndoRedoForm } from "@/hooks/use-undo-redo-form"

interface UndoRedoFormProps<T> {
  initialState: T
  onStateChange: (state: T) => void
  children: (
    state: T,
    setState: (newState: T | ((draft: T) => void)) => void,
  ) => React.ReactNode
}

export function UndoRedoForm<T>({
  initialState,
  onStateChange,
  children,
}: UndoRedoFormProps<T>) {
  const { state, setState, undo, redo, canUndo, canRedo } =
    useUndoRedoForm<T>(initialState)

  useEffect(() => {
    onStateChange(state)
  }, [state, onStateChange])

  return (
    <div>
      <div className='mb-4 flex justify-end space-x-2'>
        <Button
          variant='outline'
          size='icon'
          onClick={undo}
          disabled={!canUndo}
        >
          <UndoIcon className='h-4 w-4' />
        </Button>
        <Button
          variant='outline'
          size='icon'
          onClick={redo}
          disabled={!canRedo}
        >
          <RedoIcon className='h-4 w-4' />
        </Button>
      </div>
      {children(state, setState)}
    </div>
  )
}

"use client"

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Review, ReviewFormData, reviewSchema } from "@/types/review"

import { FormWrapper } from "../custom/formWrapper"
import { Checkbox, FormFieldWrapper, TextArea, TextInput } from "../form"
import SingleImageSelector from "../gallery/single-image-selector"

interface ReviewFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: ReviewFormData) => void
  defaultValues?: Partial<Review>
}

export function ReviewForm({
  open,
  onOpenChange,
  onSubmit,
  defaultValues,
}: ReviewFormProps) {
  const initialState: ReviewFormData = {
    client_name: "",
    client_image: "",
    client_designation: "",
    feedback: "",
    company: "",
    rating: 5,
    is_enabled: true,
    ...defaultValues,
  }

  const form = useForm<ReviewFormData>({
    resolver: zodResolver(reviewSchema),
    defaultValues: initialState,
  })

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues)
    }
  }, [defaultValues, form])

  const handleSubmit = (values: ReviewFormData) => {
    onSubmit(values)
  }

  const handleReset = () => {
    form.reset(initialState)
  }

  const handleFillSampleData = () => {
    const sampleData: ReviewFormData = {
      client_name: "John Smith",
      client_image: "https://ui-avatars.com/api/?name=John+Smith",
      client_designation: "Senior Software Engineer",
      feedback:
        "Working with this team has been an absolute pleasure. Their attention to detail and technical expertise are outstanding. They delivered our project ahead of schedule and exceeded all our expectations. The solution they provided is not only robust but also scalable for our future needs.",
      company: "Tech Innovations Inc.",
      rating: 5,
      is_enabled: true,
    }

    form.reset(sampleData)
    toast.info("Sample data loaded", {
      description: "The form has been filled with sample data.",
    })
  }

  return (
    <FormWrapper
      title={defaultValues ? "Edit Review" : "Add Review"}
      open={open}
      onOpenChange={onOpenChange}
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            <TextInput
              control={form.control}
              name='client_name'
              label='Client Name'
              placeholder="Client's name"
              required
            />

            <TextInput
              control={form.control}
              name='client_designation'
              label='Client Designation'
              placeholder="Client's designation (optional)"
            />
          </div>

          <TextArea
            control={form.control}
            name='feedback'
            label='Feedback'
            placeholder="Client's feedback"
            required
          />

          <div className='gap4 flex items-center justify-between md:gap-8'>
            <TextInput
              control={form.control}
              name='company'
              label='Company'
              placeholder="Client's company (optional)"
            />

            <TextInput
              control={form.control}
              name='rating'
              label='Rating'
              placeholder='Rating (1-5)'
              type='number'
              required
            />
          </div>

          <Checkbox
            control={form.control}
            name='is_enabled'
            label='Show on Site'
            description='Do you want to display this review on the site?'
          />

          <FormFieldWrapper
            control={form.control}
            name='client_image'
            label='Client Image'
          >
            {(field) => (
              <SingleImageSelector
                uploadedImage={field.value ?? ""}
                setUploadedImage={field.onChange}
                className='w-full'
              />
            )}
          </FormFieldWrapper>

          <div className='sticky bottom-0 z-10 flex flex-wrap justify-end gap-4 bg-background/50 py-3 pt-5 md:pt-10'>
            <Button
              type='button'
              variant='destructive'
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              type='button'
              variant='white'
              onClick={handleFillSampleData}
            >
              Fill Sample
            </Button>
            <Button type='button' variant='outline' onClick={handleReset}>
              Reset Form
            </Button>
            <Button type='submit' className='flex-1'>
              {defaultValues ? "Update" : "Create"} Review
            </Button>
          </div>
        </form>
      </Form>
    </FormWrapper>
  )
}

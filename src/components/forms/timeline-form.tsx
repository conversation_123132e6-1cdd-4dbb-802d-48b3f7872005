"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useEffect } from "react"
import { useForm } from "react-hook-form"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { getTimelineSampleData } from "@/data/timeline/timeline-samples"
import { TimelineFormData, timelineSchema } from "@/types/timeline"

import ArrayInput from "../custom/array-input"
import { FormWrapper } from "../custom/formWrapper"
import {
  Checkbox,
  DatePicker,
  FormFieldWrapper,
  SelectInput,
  TextArea,
  TextInput,
} from "../form"
import SingleImageSelector from "../gallery/single-image-selector"

interface TimelineFormProps {
  type: "experience" | "education" | "achievement" | "milestone"
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: TimelineFormData) => void
  defaultValues?: Partial<TimelineFormData>
  isPending?: boolean
}

export function TimelineForm({
  type,
  open,
  onOpenChange,
  onSubmit,
  isPending,
  defaultValues,
}: TimelineFormProps) {
  const initialState: TimelineFormData = {
    title: "",
    description: "",
    bullet_points: [],
    type: type,
    institution: "",
    location: "",
    start_date: new Date().toISOString().split("T")[0],
    end_date: null,
    order: 0,
    is_ongoing: false,
    is_enabled: true,
    icon: "",

    tags: [],
    ...defaultValues,
  }

  const form = useForm<TimelineFormData>({
    resolver: zodResolver(timelineSchema),
    defaultValues: initialState,
  })

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues)
    }
  }, [defaultValues, form])

  // Watch is_ongoing to react to its changes
  useEffect(() => {
    // Subscribe to changes in is_ongoing
    const subscription = form.watch((value, { name }) => {
      // If is_ongoing changed to true, clear end_date
      if (name === "is_ongoing" && value.is_ongoing) {
        form.setValue("end_date", null)
      }
    })

    return () => subscription.unsubscribe()
  }, [form])

  const handleSubmit = (values: TimelineFormData) => {
    console.log(values)
    onSubmit(values)
  }

  const handleReset = () => {
    form.reset(initialState)
  }

  const handleFillSample = () => {
    const sampleData = getTimelineSampleData(type, initialState)
    form.reset(sampleData)
  }
  console.log("Errors:", form.formState.errors)

  return (
    <FormWrapper
      title={defaultValues ? "Edit Timeline Entry" : "Add Timeline Entry"}
      open={open}
      onOpenChange={onOpenChange}
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            <TextInput
              control={form.control}
              name='title'
              label='Title'
              placeholder='Timeline entry title'
              className='col-span-full'
              required
            />

            <TextArea
              control={form.control}
              name='description'
              label='Description'
              placeholder='Description'
              className='col-span-full'
            />

            <SelectInput
              control={form.control}
              name='type'
              label='Type'
              options={[
                { value: "experience", label: "Experience" },
                { value: "education", label: "Education" },
                { value: "achievement", label: "Achievement" },
                { value: "milestone", label: "Milestone" },
              ]}
              hidden={!!type}
            />

            <TextInput
              control={form.control}
              name='institution'
              label={type === "education" ? "Institution" : "Company"}
              placeholder={
                type === "education" ? "Institution Name" : "Company Name"
              }
            />

            <TextInput
              control={form.control}
              name='location'
              label='Location'
              placeholder='Location'
            />

            <div className='flex flex-col gap-3'>
              <DatePicker
                control={form.control}
                name='start_date'
                label='Start Date'
                fromDate={undefined}
              />

              <DatePicker
                control={form.control}
                name='end_date'
                label='End Date'
                disabled={form.watch("is_ongoing")}
                fromDate={
                  form.watch("start_date")
                    ? new Date(form.watch("start_date"))
                    : undefined
                }
              />

              <Checkbox
                control={form.control}
                name='is_ongoing'
                label='Currently Ongoing'
              />
            </div>

            <FormFieldWrapper
              control={form.control}
              name='icon'
              label={type === "education" ? "Institute Logo" : "Company Logo"}
            >
              {(field) => (
                <SingleImageSelector
                  uploadedImage={field.value ?? ""}
                  setUploadedImage={field.onChange}
                  className='w-full'
                />
              )}
            </FormFieldWrapper>

            <div className='col-span-full flex flex-col gap-3'>
              <FormFieldWrapper
                control={form.control}
                name='bullet_points'
                label='Bullet Points'
              >
                {(field) => (
                  <ArrayInput
                    value={field.value ?? []}
                    onChange={field.onChange}
                    placeholder='Add bullet point and press enter'
                  />
                )}
              </FormFieldWrapper>

              <FormFieldWrapper control={form.control} name='tags' label='Tags'>
                {(field) => (
                  <ArrayInput
                    value={field.value ?? []}
                    onChange={field.onChange}
                    placeholder='Add tag and press enter'
                  />
                )}
              </FormFieldWrapper>
            </div>

            <Checkbox
              control={form.control}
              name='is_enabled'
              label='Show on Site'
              className='col-span-full'
              description={`Do you want to show this ${type === "experience" ? "Experience" : type === "education" ? "Education" : type === "achievement" ? "Achievement" : "Milestone"} on the site?`}
            />
          </div>

          <div className='sticky bottom-0 flex flex-wrap justify-end gap-4 bg-background py-3 pt-5'>
            <div className='flex w-full gap-2 md:w-auto md:gap-4'>
              <Button
                type='button'
                variant='destructive'
                className='flex-1'
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type='button'
                variant='outline'
                className='flex-1'
                onClick={handleReset}
              >
                Reset
              </Button>
              <Button
                type='button'
                variant='outline'
                className='flex-1'
                onClick={handleFillSample}
              >
                Fill Sample
              </Button>
            </div>
            <Button
              type='submit'
              className='w-full flex-1'
              disabled={isPending}
            >
              {defaultValues ? "Save Changes" : "Add Entry"}
            </Button>
          </div>
        </form>
      </Form>
    </FormWrapper>
  )
}

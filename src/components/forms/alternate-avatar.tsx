import { PlusIcon } from "lucide-react"
import { FC } from "react"
import {
  UseFieldArrayAppend,
  UseFieldArrayRemove,
  UseFormReturn,
} from "react-hook-form"

import { Button } from "@/components/ui/button"
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form"

import ImageUploader from "../gallery/image-uploader"

interface AlternateAvatarUploaderProps {
  form: UseFormReturn<any>
  fields: { id: string }[]
  append: UseFieldArrayAppend<any, "alternate_avatars">
  remove: UseFieldArrayRemove
}

const AlternateAvatarUploader: FC<AlternateAvatarUploaderProps> = ({
  form,
  fields,
  append,
  remove,
}) => {
  return (
    <div className='w-full flex-[1.5] pb-2'>
      <div className='flex w-full'>
        <Button variant='white' className='mb-3 block'>
          Alternative Avatars
        </Button>
      </div>

      <div className='flex w-full items-center justify-start gap-4 overflow-auto'>
        {fields.map((field, index) => (
          <div
            key={field.id}
            className='mb-2 flex flex-col items-center justify-center gap-2 rounded-md border bg-background p-2 md:p-4'
          >
            <FormField
              control={form.control}
              name={`alternate_avatars.${index}`}
              render={({ field }) => (
                <FormItem className='relative flex-1'>
                  <FormControl className='relative'>
                    <ImageUploader
                      className='aspect-square size-20 rounded-full border-2 border-white text-xs shadow-lg sm:size-28 sm:border-4'
                      uploadedImage={field.value}
                      setUploadedImage={(newImage) => field.onChange(newImage)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type='button'
              variant='destructive'
              onClick={() => remove(index)}
            >
              Remove Avatar
            </Button>
          </div>
        ))}
      </div>

      <Button
        type='button'
        variant='outline'
        size='sm'
        className='mt-2'
        onClick={() => append("")}
      >
        <PlusIcon className='mr-2 h-4 w-4' />
        Add Alternate Avatar
      </Button>
    </div>
  )
}

export default AlternateAvatarUploader

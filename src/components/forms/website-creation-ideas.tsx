// import React from "react";

// type Props = {};

// const GithubCreation = (props: Props) => {
//   const {
//     data: githubData,
//     isLoading: isFetchingGitHubData,
//     isError: isGitHubDataError,
//     refetch: fetchGitHubData,
//   } = useQuery({
//     queryKey: ["githubData", githubUsername],
//     queryFn: async () => {
//       if (!githubUsername) {
//         throw new Error("GitHub username is required");
//       }
//       const response = await fetch(`/api/github?username=${githubUsername}`);
//       if (!response.ok) {
//         throw new Error("Failed to fetch GitHub data");
//       }
//       toast.success("GitHub data fetched successfully!");
//       return response.json();
//     },
//     enabled: false, // Disable automatic fetching
//     retry: false,
//   });
//   return (
//     <div className="space-y-6">
//       <div className="space-y-2">
//         <Label className="text-base font-semibold">
//           Prefill Data (Optional)
//         </Label>
//         <p className="text-sm text-muted-foreground">
//           Add your GitHub profile to automatically fetch and prefill your data.
//           This will save you time and ensure your portfolio is up-to-date.
//         </p>
//       </div>

//       <div className="space-y-4">
//         {/* GitHub Input */}
//         <div className="space-y-2">
//           <Label htmlFor="githubUsername" className="flex items-center gap-2">
//             <CircularGitHubIcon className="" />
//             GitHub Username
//           </Label>
//           <div className="flex items-center gap-2">
//             <Input
//               id="githubUsername"
//               {...register("githubUsername")}
//               placeholder="Enter your GitHub username (optional)"
//             />
//             <Button
//               type="button"
//               variant="white"
//               disabled={isFetchingGitHubData || !!githubData}
//               onClick={() => fetchGitHubData()}
//               className="min-w-28"
//             >
//               {isFetchingGitHubData ? (
//                 <Loader2 className="h-4 w-4 animate-spin" />
//               ) : (
//                 "Fetch Data"
//               )}
//             </Button>
//           </div>
//           <p className="text-sm text-muted-foreground">
//             Example: <span className="font-mono">your-github-username</span>
//           </p>
//         </div>
//       </div>
//     </div>
//   );
// };

"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

import CustomCheckbox from "@/components/custom/checkbox"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { UploadButton } from "@/utils/uploadthing"

// Schema for the form
const formSchema = z.object({
  image_name: z.string().min(1, "Image name is required"),
  image_type: z.enum(["JPEG", "PNG", "GIF", "WEBP", "SVG", "ICO"], {
    required_error: "Image type is required",
  }),
  image_size: z.number().min(1, "Image size must be greater than 0"),
  title: z.string().optional(),
  url: z.string().url("URL must be valid"),
  key: z.string().optional(),
  alt_text: z.string().optional(),
  is_favorite: z.boolean(),
  is_deleted: z.boolean(),
  is_private: z.boolean(),
  tags: z.array(z.string()).optional(),
})

type FormValues = z.infer<typeof formSchema>

interface ImageFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: FormValues) => void
  defaultValues?: Partial<FormValues>
}

export function ImageForm({
  open,
  onOpenChange,
  onSubmit,
  defaultValues,
}: ImageFormProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      image_name: "",
      image_type: "JPEG",
      image_size: 0,
      title: "",
      url: "",
      key: "",
      alt_text: "",
      is_favorite: false,
      is_deleted: false,
      is_private: false,
      tags: [],
      ...defaultValues,
    },
  })

  const handleSubmit = (values: FormValues) => {
    onSubmit(values)
  }

  const handleReset = () => {
    form.reset()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] overflow-y-auto sm:max-w-[700px]'>
        <DialogHeader>
          <DialogTitle>
            {defaultValues ? "Edit Image Details" : "Add Image Details"}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-6'
          >
            <FormField
              control={form.control}
              name='image_name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Image Name</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter image name' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='image_type'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Image Type</FormLabel>
                  <FormControl>
                    <Input placeholder='e.g., JPEG, PNG' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='image_size'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Image Size</FormLabel>
                  <FormControl>
                    <Input
                      type='number'
                      placeholder='Enter size in bytes'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='url'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Image URL</FormLabel>
                  <FormControl>
                    {/* <Input placeholder="Enter image URL" {...field} /> */}
                    <UploadButton
                      endpoint='imageUploader'
                      {...field}
                      onClientUploadComplete={(res) => {
                        // Do something with the response
                        console.log("Files: ", res)
                        alert("Upload Completed")
                      }}
                      onUploadError={(error: Error) => {
                        // Do something with the error.
                        alert(`ERROR! ${error.message}`)
                      }}
                      onBeforeUploadBegin={(files) => {
                        // Preprocess files before uploading (e.g. rename them)
                        return files.map(
                          (f) =>
                            new File([f], "renamed-" + f.name, {
                              type: f.type,
                            }),
                        )
                      }}
                      onUploadBegin={(name) => {
                        // Do something once upload begins
                        console.log("Uploading: ", name)
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='alt_text'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Alt Text</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter alt text' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='is_favorite'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <CustomCheckbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      label='Mark as Favorite'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='flex justify-end gap-4'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type='button' variant='outline' onClick={handleReset}>
                Reset
              </Button>
              <Button type='submit'>
                {defaultValues ? "Update" : "Create"} Image
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

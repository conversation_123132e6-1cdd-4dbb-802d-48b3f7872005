/* eslint-disable @next/next/no-img-element */
"use client"

import { ImagePlusIcon, TrashIcon } from "lucide-react"
import { useCallback, useMemo, useState } from "react"
import Masonry from "react-layout-masonry"

import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

import GalleryModal from "./gallery-modal"

interface MultipleImageSelectorProps {
  uploadedImages: string[] | null
  setUploadedImages: React.Dispatch<React.SetStateAction<string[] | null>>
  className?: string
}

const urlRegex = /^https?:\/\/[^\s$.?#].[^\s]*$/i

const MASONRY_COLUMNS = { 640: 2, 768: 3, 1024: 4, 1480: 5, 1960: 6 } as const
const MASONRY_GAP = 8

export const MultipleImageSelector = ({
  uploadedImages,
  setUploadedImages,
  className,
}: MultipleImageSelectorProps) => {
  const [isGalleryOpen, setIsGalleryOpen] = useState(false)

  // Memoize the empty state check
  const isImagesEmpty = useMemo(
    () => !uploadedImages || uploadedImages.length === 0,
    [uploadedImages],
  )

  // Callbacks for event handlers
  const handleGallerySelect = useCallback(
    (imageUrls: string | string[]) => {
      const newImages = Array.isArray(imageUrls) ? imageUrls : [imageUrls]
      setUploadedImages(newImages)
      setIsGalleryOpen(false)
    },
    [setUploadedImages],
  )

  const handleRemoveImage = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>, index: number) => {
      e.preventDefault()
      if (uploadedImages) {
        setUploadedImages((prev) => {
          if (!prev) return null
          const updatedImages = prev.filter((_, i) => i !== index)
          return updatedImages.length > 0 ? updatedImages : null
        })
      }
    },
    [uploadedImages, setUploadedImages],
  )

  const handleGalleryOpen = useCallback(() => {
    setIsGalleryOpen(true)
  }, [])

  const handleGalleryClose = useCallback(() => {
    setIsGalleryOpen(false)
  }, [])

  // Memoized components
  const GalleryButton = useMemo(
    () => (
      <div
        onClick={handleGalleryOpen}
        className='grid h-32 cursor-pointer place-items-center rounded-xl border border-dashed bg-background p-3 transition-colors hover:bg-background/10'
      >
        <div className='flex flex-col items-center justify-center gap-2'>
          <ImagePlusIcon className='h-6 w-6 text-gray-600' />
          <p className='text-center text-sm font-semibold text-gray-600'>
            Add Images from Gallery
          </p>
        </div>
      </div>
    ),
    [handleGalleryOpen],
  )

  // Memoized image renderer
  const ImageItem = useCallback(
    ({ image, index }: { image: string; index: number }) => (
      <div
        key={`${image}-${index}`}
        className='relative break-inside-avoid overflow-hidden rounded-xl'
      >
        <Button
          variant='destructive'
          size='icon'
          type='button'
          onClick={(e) => handleRemoveImage(e, index)}
          className='absolute right-1 top-1 z-[1] h-6 w-6'
        >
          <TrashIcon size={14} />
        </Button>
        <img
          src={urlRegex.test(image) ? image : ""}
          alt={`Uploaded image ${index + 1}`}
          width={300}
          height={150}
          loading='lazy'
          className='h-32 w-full object-cover object-center'
        />
      </div>
    ),
    [handleRemoveImage],
  )

  // Memoized masonry grid
  const MasonryGrid = useMemo(() => {
    if (isImagesEmpty) {
      return <div className='w-full'>{GalleryButton}</div>
    }

    return (
      <Masonry
        columns={MASONRY_COLUMNS}
        gap={MASONRY_GAP}
        className='max-h-60 overflow-auto'
      >
        {GalleryButton}
        {uploadedImages?.map((image, index) => (
          <ImageItem key={`${image}-${index}`} image={image} index={index} />
        ))}
      </Masonry>
    )
  }, [isImagesEmpty, GalleryButton, uploadedImages, ImageItem])

  // Memoize the className concatenation
  const containerClassName = useMemo(
    () => cn("relative mt-2", className),
    [className],
  )

  return (
    <>
      <div className={containerClassName}>{MasonryGrid}</div>

      <GalleryModal
        prevSelectedImage={uploadedImages ?? []}
        onSelect={handleGallerySelect}
        allowMultiple={true}
        isOpen={isGalleryOpen}
        onClose={handleGalleryClose}
      />
    </>
  )
}

export default MultipleImageSelector

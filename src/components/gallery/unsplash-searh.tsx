// import React, { useState } from "react";
// import { Input } from "@/components/ui/input";
// import { Button } from "@/components/ui/button";
// import Image from "next/image";
// import { searchUnsplash } from "@/actions/gallery";
// import { useDebounce } from "@/hooks";

// interface UnsplashSearchProps {
//   onSelect: (imageUrl: string, unsplashData: any) => void;
// }

// export function UnsplashSearch({ onSelect }: UnsplashSearchProps) {
//   const [query, setQuery] = useState("");
//   const [results, setResults] = useState<any[]>([]);
//   const [page, setPage] = useState(1);
//   const [totalPages, setTotalPages] = useState(0);
//   const [isLoading, setIsLoading] = useState(false);

//   // Debounced query to avoid unnecessary API calls
//   const debouncedQuery = useDebounce(query, 300);

//   // Fetch images from Unsplash
//   const fetchImages = async (searchQuery: string, pageNum: number = 1) => {
//     if (!searchQuery.trim()) return;
//     setIsLoading(true);
//     try {
//       const data = await searchUnsplash(searchQuery, pageNum);
//       setResults((prevResults) =>
//         pageNum === 1 ? data.results : [...prevResults, ...data.results]
//       );
//       setTotalPages(data.total_pages);
//       setPage(pageNum);
//     } catch (error) {
//       console.error("Error fetching Unsplash images:", error);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Trigger search whenever the debounced query changes
//   React.useEffect(() => {
//     fetchImages(debouncedQuery, 1); // Reset to page 1 for new searches
//   }, [debouncedQuery]);

//   // Handle Load More functionality
//   const handleLoadMore = () => {
//     if (page < totalPages) {
//       fetchImages(debouncedQuery, page + 1);
//     }
//   };

//   return (
//     <div className="space-y-4">
//       <div className="flex space-x-2">
//         <Input
//           placeholder="Search Unsplash..."
//           value={query}
//           onChange={(e) => setQuery(e.target.value)}
//         />
//       </div>
//       {isLoading && <p>Loading...</p>}
//       <div className="grid grid-cols-3 gap-4 min-h-72 max-h-svh">
//         {results.map((image) => (
//           <div key={image.id} className="relative aspect-square">
//             <Image
//               src={image.urls.small}
//               alt={image.alt_description || "Unsplash Image"}
//               fill
//               className="object-cover rounded-md cursor-pointer"
//               onClick={() => onSelect(image.urls.full, image)}
//             />
//           </div>
//         ))}
//       </div>
//       {page < totalPages && !isLoading && (
//         <Button onClick={handleLoadMore}>Load More</Button>
//       )}
//       {isLoading && <p>Loading more...</p>}
//     </div>
//   );
// }

const UnsplashSearch = () => {
  return <div>UnsplashSearch</div>
}

export default UnsplashSearch

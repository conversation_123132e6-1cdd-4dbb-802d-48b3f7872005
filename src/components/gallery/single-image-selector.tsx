/* eslint-disable @next/next/no-img-element */
"use client"

import { ImagePlusIcon } from "lucide-react"
import { useCallback, useMemo, useState } from "react"

import { cn } from "@/lib/utils"

import GalleryModal from "./gallery-modal"

interface SingleImageSelectorProps {
  uploadedImage: string | null
  setUploadedImage: React.Dispatch<React.SetStateAction<string | null>>
  className?: string
  size?: "icon" | "small" | "large" | "extralarge"
  rounded?: boolean
}

const urlRegex = /^https?:\/\/[^\s$.?#].[^\s]*$/i

// Move size styles outside component to prevent recreation
const SIZE_STYLES = {
  icon: {
    container: "size-16",
    icon: "size-4",
    text: "text-xs hidden",
    padding: "p-2",
  },
  small: {
    container: "size-24",
    icon: "size-6",
    text: "text-sm",
    padding: "p-3",
  },
  large: {
    container: "size-48",
    icon: "size-8",
    text: "text-base",
    padding: "p-4",
  },
  extralarge: {
    container: "size-60",
    icon: "size-10",
    text: "text-lg",
    padding: "p-5",
  },
} as const

export const SingleImageSelector = ({
  uploadedImage,
  setUploadedImage,
  className,
  size = "large",
  rounded = false,
}: SingleImageSelectorProps) => {
  const [isGalleryOpen, setIsGalleryOpen] = useState(false)

  // Memoize handlers
  const handleGallerySelect = useCallback(
    (imageUrl: string | string[]) => {
      const newImage = Array.isArray(imageUrl) ? imageUrl[0] : imageUrl
      setUploadedImage(newImage)
      setIsGalleryOpen(false)
    },
    [setUploadedImage],
  )

  const handleGalleryOpen = useCallback(() => {
    setIsGalleryOpen(true)
  }, [])

  const handleGalleryClose = useCallback(() => {
    setIsGalleryOpen(false)
  }, [])

  // Memoize computed values
  const isImageEmpty = useMemo(
    () => !uploadedImage || uploadedImage.trim() === "",
    [uploadedImage],
  )

  const currentSize = useMemo(() => SIZE_STYLES[size], [size])

  const backgroundImage = useMemo(
    () =>
      uploadedImage && urlRegex.test(uploadedImage)
        ? `url(${uploadedImage})`
        : undefined,
    [uploadedImage],
  )

  // Memoize className combinations
  const containerClassName = useMemo(
    () => cn("relative mt-2", currentSize.container, className),
    [currentSize.container, className],
  )

  const imageContainerClassName = useMemo(
    () =>
      cn(
        "group relative flex h-full w-full cursor-pointer items-center justify-center overflow-hidden border-2 border-dashed",
        currentSize.padding,
        rounded ? "rounded-full" : "rounded-md",
        uploadedImage && urlRegex.test(uploadedImage)
          ? "bg-cover bg-center"
          : "grid place-items-center",
        className,
      ),
    [currentSize.padding, rounded, uploadedImage, className],
  )

  const overlayClassName = useMemo(
    () =>
      cn(
        "absolute inset-0 flex items-center justify-center dark:bg-black/40 bg-background/40 transition-colors group-hover:bg-background/60",
        isImageEmpty && "bg-transparent",
      ),
    [isImageEmpty],
  )

  const textClassName = useMemo(
    () => cn("px-2 text-center font-bold md:px-3", currentSize.text),
    [currentSize.text],
  )

  // Memoize content renderer
  const ImageContent = useMemo(
    () => (
      <div className='flex flex-col items-center justify-center gap-2 text-foreground'>
        <span>
          <ImagePlusIcon className={currentSize.icon} />
        </span>
        <p className={textClassName}>
          {isImageEmpty ? "Click to Select Image" : "Change Image"}
        </p>
      </div>
    ),
    [currentSize.icon, textClassName, isImageEmpty],
  )

  // Memoize main content
  const Content = useMemo(
    () => (
      <div
        onClick={handleGalleryOpen}
        className={imageContainerClassName}
        style={{ backgroundImage }}
      >
        <div className={overlayClassName}>{ImageContent}</div>
      </div>
    ),
    [
      handleGalleryOpen,
      imageContainerClassName,
      backgroundImage,
      overlayClassName,
      ImageContent,
    ],
  )

  return (
    <>
      <div className={containerClassName}>{Content}</div>

      <GalleryModal
        prevSelectedImage={[uploadedImage ?? ""]}
        onSelect={handleGallerySelect}
        allowMultiple={false}
        isOpen={isGalleryOpen}
        onClose={handleGalleryClose}
      />
    </>
  )
}

export default SingleImageSelector

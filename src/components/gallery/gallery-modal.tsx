"use client"

import { useQuery } from "@tanstack/react-query"
import { XIcon } from "lucide-react"
import { useParams } from "next/navigation"
import React, { memo, useCallback, useEffect, useMemo, useState } from "react"
import Masonry from "react-layout-masonry"

import { fetchUserMedia } from "@/actions/users"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  codingLanguageIcons,
  photographyImages,
  posterImages,
  projectImages,
  PublicImageType,
  publicUserImages,
  socialMediaIcons,
} from "@/constants/public-images"
import { useDebounce } from "@/hooks"
import { cn } from "@/lib/utils"
import { ImageType } from "@/types/image"

import CustomImageUploader from "../custom/custom-uploader"

interface GalleryModalProps {
  onSelect: (imageUrls: string[]) => void
  allowMultiple: boolean
  isOpen: boolean
  prevSelectedImage: string[]
  onClose: () => void
}

const ImageGrid = memo(
  ({
    images,
    isPublic,
    columns,
    onImageSelect,
    selectedImages,
  }: {
    images: ImageType[] | PublicImageType[]
    isPublic: boolean
    columns: Record<number, number>
    onImageSelect: (url: string) => void
    selectedImages: string[]
  }) => (
    <Masonry columns={columns} gap={16}>
      {images.map((item) => (
        <div
          key={isPublic ? item.url : item._id}
          onClick={() => onImageSelect(item.url)}
          role='button'
          aria-label={`Select ${item.title}`}
          tabIndex={0}
          className={cn(
            "group relative flex cursor-pointer items-center justify-center rounded-xl border-2 border-primary/20 bg-primary/10 p-1 dark:border-primary/40 md:p-2",
            selectedImages.includes(item.url)
              ? "border-2 border-foreground dark:border-foreground"
              : "",
          )}
        >
          <img
            loading='lazy'
            src={item.url}
            alt={item.title}
            className={cn(
              "h-auto w-full rounded-lg object-cover transition-transform duration-200 hover:scale-105",
            )}
          />
          <div
            className={cn(
              "absolute inset-0 flex items-end justify-center gap-1 rounded-lg bg-background/50 opacity-0 transition-opacity duration-200 group-hover:opacity-100",
              selectedImages.includes(item.url)
                ? "bg-background/70 opacity-50"
                : "bg-background/50",
            )}
          >
            <span className='line-clamp-1 text-wrap text-xs font-medium text-white'>
              {item.title}
            </span>
          </div>
        </div>
      ))}
    </Masonry>
  ),
)

ImageGrid.displayName = "ImageGrid"

const LoadingSkeleton = memo(() => (
  <Masonry columns={{ 240: 1, 480: 2, 768: 3, 1024: 4 }} gap={16}>
    {Array.from({ length: 6 }).map((_, index) => (
      <Skeleton key={index} className='h-40 w-full rounded-lg' />
    ))}
  </Masonry>
))

LoadingSkeleton.displayName = "LoadingSkeleton"

const ConfirmButton = memo(
  ({
    onClick,
    disabled,
    count,
  }: {
    onClick: (e: React.MouseEvent<HTMLButtonElement>) => void
    disabled: boolean
    count: number
  }) => (
    <Button
      type='button'
      onClick={onClick}
      disabled={disabled}
      className='mt-4 w-full'
    >
      Confirm Selection ({count})
    </Button>
  ),
)

ConfirmButton.displayName = "ConfirmButton"

const GalleryModal = ({
  onSelect,
  allowMultiple,
  isOpen,
  onClose,
  prevSelectedImage,
}: GalleryModalProps) => {
  const [search, setSearch] = useState("")
  const [page, setPage] = useState(1)
  const [selectedImages, setSelectedImages] =
    useState<string[]>(prevSelectedImage)
  const [activeTab, setActiveTab] = useState("gallery")
  const params = useParams<{ website: string }>()

  const debouncedSearch = useDebounce(search, 300)

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["images", params.website, page, debouncedSearch],
    queryFn: async () => {
      if (!params.website) throw new Error("Website ID is required")
      return await fetchUserMedia({
        websiteId: params.website,
        search: debouncedSearch,
        page,
      })
    },
    enabled: !!params.website,
  })

  const images = useMemo(() => data?.images || [], [data])
  const total = data?.total || 0
  const pageSize = 12
  const hasMore = page * pageSize < total

  useEffect(() => {
    if (!isLoading && images.length === 0 && activeTab === "gallery") {
      setActiveTab("upload")
    }
  }, [images.length, isLoading, activeTab])

  const handleImageSelect = useCallback(
    (imageUrl: string) => {
      setSelectedImages((prev) =>
        prev.includes(imageUrl)
          ? prev.filter((img) => img !== imageUrl)
          : allowMultiple
            ? [...prev, imageUrl]
            : [imageUrl],
      )
    },
    [allowMultiple],
  )

  const handleConfirmSelection = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault()
      if (selectedImages.length === 0) return
      onSelect(selectedImages)
      onClose()
    },
    [selectedImages, onSelect, onClose],
  )

  const tabs = useMemo(
    () => [
      {
        value: "gallery",
        label: `My Gallery (${total})`,
        content: (
          <>
            <div className='sticky top-0 z-10 pb-4'>
              <Input
                placeholder='Search images...'
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className='w-full'
              />
            </div>
            <div className='max-h-[50vh] overflow-y-auto p-2 pb-28 md:min-h-[60vh]'>
              {isLoading ? (
                <LoadingSkeleton />
              ) : isError ? (
                <div className='flex h-40 items-center justify-center'>
                  <p className='text-red-500'>
                    Error loading images. Please try again.
                  </p>
                </div>
              ) : images.length > 0 ? (
                <ImageGrid
                  images={images}
                  isPublic={false}
                  columns={{ 240: 2, 480: 3, 768: 4, 1024: 5 }}
                  onImageSelect={handleImageSelect}
                  selectedImages={selectedImages}
                />
              ) : (
                <div className='flex h-40 flex-col items-center justify-center gap-3 p-4 text-center text-muted-foreground'>
                  <p>No images found</p>
                  <p>
                    Upload your images in the gallery page or use the upload
                    option.
                  </p>
                </div>
              )}
            </div>
            <div className='fixed bottom-0 left-0 right-0 w-full rounded-b-lg bg-background/85 p-3'>
              <div className='flex items-center justify-between pt-4'>
                <div className='text-sm text-muted-foreground'>
                  Showing {images.length} of {total} entries
                </div>
                <div className='flex items-center gap-2'>
                  <Button
                    type='button'
                    size='sm'
                    variant='outline'
                    disabled={page <= 1}
                    onClick={() => {
                      if (page > 1) {
                        setPage((prev) => prev - 1)
                        refetch()
                      }
                    }}
                  >
                    Previous
                  </Button>
                  <span className='text-sm'>Page {page}</span>
                  <Button
                    type='button'
                    size='sm'
                    variant='outline'
                    disabled={!hasMore}
                    onClick={() => {
                      if (hasMore) {
                        setPage((prev) => prev + 1)
                        refetch()
                      }
                    }}
                  >
                    Next
                  </Button>
                </div>
              </div>
              <ConfirmButton
                onClick={handleConfirmSelection}
                disabled={selectedImages.length === 0}
                count={selectedImages.length}
              />
            </div>
          </>
        ),
      },
      {
        value: "avatars",
        label: "Avatars",
        content: (
          <>
            <div className='max-h-[50vh] overflow-y-auto p-2 md:min-h-[60vh]'>
              <ImageGrid
                images={publicUserImages}
                isPublic={true}
                columns={{ 240: 2, 480: 3, 768: 4, 1024: 5 }}
                onImageSelect={handleImageSelect}
                selectedImages={selectedImages}
              />
            </div>
            <ConfirmButton
              onClick={handleConfirmSelection}
              disabled={selectedImages.length === 0}
              count={selectedImages.length}
            />
          </>
        ),
      },
      {
        value: "projects",
        label: "Projects",
        content: (
          <>
            <div className='max-h-[50vh] overflow-y-auto p-2 md:min-h-[60vh]'>
              <ImageGrid
                images={projectImages}
                isPublic={true}
                columns={{ 240: 2, 480: 3, 768: 4, 1024: 4 }}
                onImageSelect={handleImageSelect}
                selectedImages={selectedImages}
              />
            </div>
            <ConfirmButton
              onClick={handleConfirmSelection}
              disabled={selectedImages.length === 0}
              count={selectedImages.length}
            />
          </>
        ),
      },
      {
        value: "photography",
        label: "Photography",
        content: (
          <>
            <div className='max-h-[50vh] overflow-y-auto p-2 md:min-h-[60vh]'>
              <ImageGrid
                images={photographyImages}
                isPublic={true}
                columns={{ 240: 2, 480: 3, 768: 4, 1024: 5 }}
                onImageSelect={handleImageSelect}
                selectedImages={selectedImages}
              />
            </div>
            <ConfirmButton
              onClick={handleConfirmSelection}
              disabled={selectedImages.length === 0}
              count={selectedImages.length}
            />
          </>
        ),
      },
      {
        value: "posters",
        label: "Posters",
        content: (
          <>
            <div className='max-h-[50vh] overflow-y-auto p-2 md:min-h-[60vh]'>
              <ImageGrid
                images={posterImages}
                isPublic={true}
                columns={{ 240: 2, 480: 3, 768: 4, 1024: 5 }}
                onImageSelect={handleImageSelect}
                selectedImages={selectedImages}
              />
            </div>
            <ConfirmButton
              onClick={handleConfirmSelection}
              disabled={selectedImages.length === 0}
              count={selectedImages.length}
            />
          </>
        ),
      },
      {
        value: "social",
        label: "Social",
        content: (
          <>
            <div className='max-h-[50vh] overflow-y-auto p-2 md:min-h-[60vh]'>
              <ImageGrid
                images={socialMediaIcons}
                isPublic={true}
                columns={{ 240: 3, 480: 4, 768: 5, 1024: 6 }}
                onImageSelect={handleImageSelect}
                selectedImages={selectedImages}
              />
            </div>
            <ConfirmButton
              onClick={handleConfirmSelection}
              disabled={selectedImages.length === 0}
              count={selectedImages.length}
            />
          </>
        ),
      },
      {
        value: "languages",
        label: "Languages",
        content: (
          <>
            <div className='max-h-[50vh] overflow-y-auto p-2 md:min-h-[60vh]'>
              <ImageGrid
                images={codingLanguageIcons}
                isPublic={true}
                columns={{ 240: 3, 480: 4, 768: 5, 1024: 6 }}
                onImageSelect={handleImageSelect}
                selectedImages={selectedImages}
              />
            </div>
            <ConfirmButton
              onClick={handleConfirmSelection}
              disabled={selectedImages.length === 0}
              count={selectedImages.length}
            />
          </>
        ),
      },
      {
        value: "upload",
        label: "Upload Image",
        content: (
          <div className='flex h-full w-full items-center justify-center overflow-auto p-4'>
            <CustomImageUploader websiteId={params.website} />
          </div>
        ),
      },
    ],
    [
      total,
      search,
      isLoading,
      isError,
      images,
      page,
      hasMore,
      refetch,
      handleImageSelect,
      handleConfirmSelection,
      selectedImages,
      publicUserImages,
    ],
  )

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='flex max-h-[90vh] flex-col gap-4 overflow-hidden lg:min-h-[650px] lg:min-w-[1000px]'>
        <DialogHeader className='flex flex-row items-center justify-start gap-3'>
          <DialogTitle className='text-xl md:text-3xl lg:text-3xl'>
            Gallery
          </DialogTitle>
          {selectedImages.length > 0 && (
            <div className='flex gap-2'>
              {selectedImages.map((img) => (
                <div
                  key={img}
                  className='relative h-10 w-10 cursor-pointer overflow-hidden rounded-sm border-foreground bg-card'
                  onClick={() => handleImageSelect(img)}
                >
                  <img
                    src={img}
                    alt='Selected'
                    className='h-full w-full object-cover'
                  />
                  <div className='absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity duration-200 group-hover:opacity-100'>
                    <XIcon className='h-4 w-4 text-foreground' />
                  </div>
                </div>
              ))}
            </div>
          )}
        </DialogHeader>
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className='h-full w-full space-y-1'
        >
          <TabsList className='flex w-full items-center justify-start gap-2 bg-background/50'>
            {tabs.map((tab) => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className='border border-input bg-background/40 data-[state=active]:bg-primary data-[state=active]:text-white'
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
          {tabs.map((tab) => (
            <TabsContent
              key={tab.value}
              value={tab.value}
              className='h-full w-full'
            >
              {tab.content}
            </TabsContent>
          ))}
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

export default memo(GalleryModal)

/* eslint-disable @next/next/no-img-element */
"use client"

import { ImagePlusIcon, TrashIcon } from "lucide-react"
import { useState } from "react"
import Masonry from "react-layout-masonry"

import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

import GalleryModal from "./gallery-modal"

interface ImageUploaderProps {
  uploadedImage: string | string[] | null
  setUploadedImage: React.Dispatch<
    React.SetStateAction<string | string[] | null>
  >
  className?: string
  allowMultiple?: boolean
  resultAsArray?: boolean // Control result format
}

const urlRegex = /^https?:\/\/[^\s$.?#].[^\s]*$/i

const ImageUploader = ({
  uploadedImage,
  setUploadedImage,
  className,
  allowMultiple = false,
  resultAsArray = false,
}: ImageUploaderProps) => {
  const [isGalleryOpen, setIsGalleryOpen] = useState(false)
  const isArrayUploadedImage = Array.isArray(uploadedImage)

  const handleGallerySelect = (imageUrl: string | string[]) => {
    if (allowMultiple) {
      const newImages = Array.isArray(imageUrl) ? imageUrl : [imageUrl]
      setUploadedImage(newImages) // Replace with new selection
    } else {
      const newImage = Array.isArray(imageUrl) ? imageUrl[0] : imageUrl
      setUploadedImage(resultAsArray ? [newImage] : newImage)
    }
    setIsGalleryOpen(false)
  }

  const handleRemoveImage = (
    e: React.MouseEvent<HTMLButtonElement>,
    index: number,
  ) => {
    e.preventDefault()
    if (allowMultiple && Array.isArray(uploadedImage)) {
      const updatedImages = uploadedImage.filter((_, i) => i !== index)
      setUploadedImage(updatedImages.length > 0 ? updatedImages : null)
    } else {
      setUploadedImage(null)
    }
  }

  const handleRemoveSingleImage = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    setUploadedImage(null)
  }

  const isUploadedImageEmpty = () => {
    return (
      !uploadedImage || // null or undefined
      (Array.isArray(uploadedImage) && uploadedImage.length === 0) || // empty array
      (typeof uploadedImage === "string" && uploadedImage.trim() === "") // empty string
    )
  }

  const renderGallery = () => {
    return (
      <div
        onClick={(e) => {
          e.preventDefault()
          setIsGalleryOpen(true)
        }}
        className='grid h-full cursor-pointer place-items-center rounded-xl border border-dashed p-3 px-5'
      >
        <div className='flex flex-col items-center justify-center gap-4'>
          <span>
            <ImagePlusIcon />
          </span>
          <p className='text-center text-muted-foreground'>
            <span className='font-bold'>Click to Select</span> <br />
            Image from Gallery
          </p>
        </div>
      </div>
    )
  }

  const renderImages = () => {
    if (isUploadedImageEmpty()) return null

    if (allowMultiple && Array.isArray(uploadedImage)) {
      return (
        <Masonry
          columns={{ 640: 2, 768: 3, 1024: 4, 1480: 5, 1960: 6 }}
          gap={8}
          className='max-h-40 overflow-auto md:max-h-60'
        >
          {renderGallery()}

          {uploadedImage.map((image, index) => (
            <div
              key={index}
              className='relative break-inside-avoid bg-black/20'
            >
              <Button
                variant='destructive'
                size='icon'
                type='button'
                onClick={(e) => handleRemoveImage(e, index)}
                className='absolute right-2 top-2 z-10'
              >
                <TrashIcon size={16} />
              </Button>
              <img
                src={image}
                alt=''
                width={300}
                height={150}
                loading='lazy'
                className='w-full object-contain object-center opacity-40'
              />
            </div>
          ))}
        </Masonry>
      )
    }

    if (typeof uploadedImage === "string" && uploadedImage.trim() !== "") {
      return (
        <div className='relative'>
          <Button
            variant='destructive'
            size='icon'
            type='button'
            onClick={handleRemoveSingleImage}
            className='absolute right-2 top-2 z-10'
          >
            <TrashIcon size={16} />
          </Button>
          <img
            // src={uploadedImage}
            src={urlRegex.test(uploadedImage) ? uploadedImage : ""}
            alt=''
            width={300}
            height={150}
            loading='lazy'
            className='h-auto w-auto rounded-xl object-contain object-center'
          />
        </div>
      )
    }

    return null
  }

  return (
    <>
      <div
        className={cn(
          "relative mt-2 grid aspect-[16/6] gap-4 overflow-hidden",
          className,
        )}
      >
        {!isUploadedImageEmpty() ? renderImages() : renderGallery()}
      </div>

      <GalleryModal
        prevSelectedImage={
          isArrayUploadedImage ? uploadedImage : [uploadedImage ?? ""]
        }
        onSelect={handleGallerySelect}
        allowMultiple={allowMultiple}
        isOpen={isGalleryOpen}
        onClose={() => setIsGalleryOpen(false)}
      />
    </>
  )
}

export default ImageUploader

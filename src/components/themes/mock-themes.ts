import { Template } from "@/types/template"

export const mockThemes: Template[] = [
  {
    _id: "theme_minimalist_portfolio",
    name: "Minimalist Portfolio",
    key: "minimalist_portfolio",
    description:
      "A sleek and modern portfolio template for creative professionals.",
    preview_image:
      "https://utfs.io/f/qPlpyBmwd8UNgNO8dUaKnepdPC8WMHwx1rsYBOiLTUauVy9A",

    preview_url: "https://demo.minimalistportfolio.com",
    default_meta: {
      sections: [
        { section_name: "hero", is_enabled: true },
        { section_name: "about", is_enabled: true },
        { section_name: "projects", is_enabled: true },
        { section_name: "contact", is_enabled: true },
      ],
      favicon: "/images/minimalist_portfolio_favicon.ico",
      logo: "/images/minimalist_portfolio_logo.png",
      keywords: "portfolio, creative, minimal, modern",
      fonts: {
        heading_font: {
          name: "<PERSON><PERSON><PERSON>",
          url: "https://fonts.googleapis.com/css2?family=Poppins:wght@700&display=swap",
        },
        body_font: {
          name: "Open Sans",
          url: "https://fonts.googleapis.com/css2?family=Open+Sans:wght@400&display=swap",
        },
      },
      color_palette: {
        primary_color: "#333333",
        secondary_color: "#555555",
        accent_color: "#FF5722",
      },
    },
    is_pro: false,
    tags: ["minimal", "modern", "creative"],
    is_enabled: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-10"),
  },
  {
    _id: "theme_darkfolio",
    name: "Darkfolio",
    key: "darkfolio",
    description: "A dark-themed portfolio for developers and designers.",
    // preview_image: "/images/darkfolio_preview.svg",
    preview_image:
      "https://utfs.io/f/qPlpyBmwd8UNgNO8dUaKnepdPC8WMHwx1rsYBOiLTUauVy9A",
    preview_url: "https://demo.darkfolio.com",
    default_meta: {
      sections: [
        { section_name: "hero", is_enabled: true },
        { section_name: "skills", is_enabled: true },
        { section_name: "projects", is_enabled: true },
        { section_name: "testimonials", is_enabled: true },
      ],
      favicon: "/images/darkfolio_favicon.ico",
      logo: "/images/darkfolio_logo.png",
      keywords: "portfolio, dark, developer, designer",
      fonts: {
        heading_font: {
          name: "Roboto",
          url: "https://fonts.googleapis.com/css2?family=Roboto:wght@700&display=swap",
        },
        body_font: {
          name: "Lato",
          url: "https://fonts.googleapis.com/css2?family=Lato:wght@400&display=swap",
        },
      },
      color_palette: {
        primary_color: "#000000",
        secondary_color: "#1A1A1A",
        accent_color: "#FF9800",
      },
    },
    is_pro: true,
    tags: ["dark", "developer", "designer"],
    is_enabled: true,
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    _id: "theme_persona",
    name: "Persona",
    key: "persona",
    description:
      "A vibrant and colorful portfolio for freelancers and artists.",
    preview_image:
      "https://utfs.io/f/qPlpyBmwd8UNgNO8dUaKnepdPC8WMHwx1rsYBOiLTUauVy9A",

    preview_url: "https://demo.persona.com",
    default_meta: {
      sections: [
        { section_name: "hero", is_enabled: true },
        { section_name: "services", is_enabled: true },
        { section_name: "portfolio", is_enabled: true },
        { section_name: "testimonials", is_enabled: true },
        { section_name: "contact", is_enabled: true },
      ],
      favicon: "/images/persona_favicon.ico",
      logo: "/images/persona_logo.png",
      keywords: "portfolio, freelance, artist, vibrant",
      fonts: {
        heading_font: {
          name: "Montserrat",
          url: "https://fonts.googleapis.com/css2?family=Montserrat:wght@700&display=swap",
        },
        body_font: {
          name: "Raleway",
          url: "https://fonts.googleapis.com/css2?family=Raleway:wght@400&display=swap",
        },
      },
      color_palette: {
        primary_color: "#4CAF50",
        secondary_color: "#FFC107",
        accent_color: "#FF5722",
      },
    },
    is_pro: false,
    tags: ["vibrant", "freelance", "artist"],
    is_enabled: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-12"),
  },
]

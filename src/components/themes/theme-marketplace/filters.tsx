"use client"

import { Search } from "lucide-react"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface FiltersProps {
  options: {
    categories: ["All Categories", "Portfolio", "eCommerce", "Blog"] // Example categories
    tags: ["All Categories", "Portfolio", "eCommerce", "Blog"] // Example categories
  }
  selectedCategory: string
  onCategoryChange: (category: string) => void
  selectedTag: string
  onTagChange: (tag: string) => void
  isPro: boolean | null
  onProFilterChange: (value: boolean | null) => void
  searchQuery: string
  onSearchChange: (query: string) => void
}

export function Filters({
  options,
  selectedCategory,
  onCategoryChange,
  selectedTag,
  onTagChange,
  isPro,
  onProFilterChange,
  searchQuery,
  onSearchChange,
}: FiltersProps) {
  return (
    <div className='sticky top-[73px] z-10 border-b bg-background px-6 py-4'>
      <div className='mx-auto flex max-w-7xl items-center gap-4'>
        {/* Search Field */}
        <div className='relative flex-1'>
          <Search className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
          <Input
            placeholder='Search themes...'
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className='pl-9'
          />
        </div>

        {/* Category Filter */}
        <div className='grid w-[200px] gap-1.5'>
          <Label htmlFor='category' className='sr-only'>
            Category
          </Label>
          <Select value={selectedCategory} onValueChange={onCategoryChange}>
            <SelectTrigger id='category'>
              <SelectValue placeholder='Select category' />
            </SelectTrigger>
            <SelectContent>
              {options?.categories?.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Tag Filter */}
        <div className='grid w-[200px] gap-1.5'>
          <Label htmlFor='tag' className='sr-only'>
            Tag
          </Label>
          <Select value={selectedTag} onValueChange={onTagChange}>
            <SelectTrigger id='tag'>
              <SelectValue placeholder='Select tag' />
            </SelectTrigger>
            <SelectContent>
              {options?.tags?.map((tag) => (
                <SelectItem key={tag} value={tag}>
                  {tag}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Pro/Free Filter */}
        <div className='grid w-[200px] gap-1.5'>
          <Label htmlFor='proFilter' className='sr-only'>
            Pro/Free
          </Label>
          <Select
            value={isPro === null ? "" : isPro ? "pro" : "free"}
            onValueChange={(value) =>
              onProFilterChange(
                value === "pro" ? true : value === "free" ? false : null,
              )
            }
          >
            <SelectTrigger id='proFilter'>
              <SelectValue placeholder='Pro/Free' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value=''>All</SelectItem>
              <SelectItem value='pro'>Pro</SelectItem>
              <SelectItem value='free'>Free</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
}

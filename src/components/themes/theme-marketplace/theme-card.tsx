"use client"

import { Check } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Template } from "@/types/template"
import { Website } from "@/types/website"

interface ThemeCardProps {
  theme: Template
  onApply: (themeId: string) => void
  onCustomize: (themeId: string) => void
  isActive?: boolean
  website?: Website
}

export function ThemeCard({
  theme,
  onApply,
  onCustomize,
  isActive,
  website,
}: ThemeCardProps) {
  return (
    <Card className='overflow-hidden'>
      <CardContent className='p-0'>
        {/* <div className="relative aspect-[3/4] overflow-hidden"> */}
        <div className='relative aspect-video overflow-hidden'>
          <Image
            src={theme.preview_image}
            alt={theme.name}
            fill
            className='object-contain transition-transform hover:scale-105'
          />
          {isActive && (
            <div className='absolute inset-0 flex items-center justify-center bg-black/50'>
              <Badge variant='secondary' className='gap-1.5'>
                <Check className='h-3.5 w-3.5' />
                Active Theme
              </Badge>
            </div>
          )}
        </div>
        <div className='p-4'>
          <div className='flex items-start justify-between gap-4'>
            <div>
              <h3 className='mb-1 text-base font-semibold'>{theme.name}</h3>
              <p className='line-clamp-2 text-xs text-muted-foreground'>
                {theme.description}
              </p>
            </div>
            {theme.is_pro && (
              <div className='text-right'>
                <Badge
                  variant='default'
                  className='bg-gradient-to-br from-yellow-400 to-orange-400 text-black'
                >
                  Pro
                </Badge>
              </div>
            )}
          </div>

          {/* extra details not needed */}
          {/* <div className="mt-4">
            <p className="text-xs font-medium text-muted-foreground">
              Key Sections
            </p>
            <ul className="mt-2 grid grid-cols-2 gap-2 text-xs">
              {theme.default_meta.sections
                .filter((section) => section.is_enabled)
                .slice(0, 4)
                .map((section) => (
                  <li
                    key={section.section_name}
                    className="flex items-center gap-1.5"
                  >
                    <Check className="h-3.5 w-3.5 text-primary" />
                    {section.section_name.charAt(0).toUpperCase() +
                      section.section_name.slice(1)}
                  </li>
                ))}
            </ul>
          </div>
          <div className="mt-4">
            <p className="text-xs font-medium text-muted-foreground">Fonts</p>
            <ul className="mt-2 text-xs">
              <li>
                Heading Font: {theme.default_meta.fonts.heading_font.name}
              </li>
              <li>Body Font: {theme.default_meta.fonts.body_font.name}</li>
            </ul>
          </div>
          <div className="mt-4">
            <p className="text-xs font-medium text-muted-foreground">Colors</p>
            <ul className="mt-2 text-xs flex gap-2">
              <li>
                <span
                  className="inline-block h-4 w-4 rounded-full"
                  style={{
                    backgroundColor:
                      theme.default_meta.color_palette.primary_color,
                  }}
                ></span>{" "}
                Primary
              </li>
              <li>
                <span
                  className="inline-block h-4 w-4 rounded-full"
                  style={{
                    backgroundColor:
                      theme.default_meta.color_palette.secondary_color,
                  }}
                ></span>{" "}
                Secondary
              </li>
              <li>
                <span
                  className="inline-block h-4 w-4 rounded-full"
                  style={{
                    backgroundColor:
                      theme.default_meta.color_palette.accent_color,
                  }}
                ></span>{" "}
                Accent
              </li>
            </ul>
          </div> */}
        </div>
      </CardContent>
      <CardFooter className='flex gap-4 border-t p-4'>
        {/* Preview button is not needed   */}
        {isActive ? (
          // Show "Customize" button for the active theme
          <Button
            variant={"secondary"}
            className='w-full'
            onClick={() => onCustomize(theme._id)}
          >
            Customize
          </Button>
        ) : theme.is_pro ? (
          website?.is_pro ? (
            // Show "Apply" button for pro themes if the user's website is pro
            <Button
              variant={"tertiary"}
              className='w-full'
              onClick={() => onApply(theme._id)}
            >
              Apply
            </Button>
          ) : (
            // Show "Upgrade Plan" button for pro themes if the user's website is not pro
            <Button className='w-full' asChild>
              <Link href={`/upgrade/${website?._id}`}>Upgrade Plan</Link>
            </Button>
          )
        ) : (
          // Show "Apply" button for non-pro themes
          <Button
            variant={"tertiary"}
            className='w-full'
            onClick={() => onApply(theme._id)}
          >
            Apply
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}

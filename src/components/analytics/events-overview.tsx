/* eslint-disable simple-import-sort/imports */
"use client"

import {
  Activity,
  ArrowDown,
  BarChart3,
  Calendar,
  ChevronDown,
  Clock,
  Download,
  FileText,
  Filter,
  Info,
  ListFilter,
  MousePointer,
  Play,
  Search,
  Share2,
  <PERSON>rkles,
  X,
} from "lucide-react"
import { use<PERSON><PERSON>back, useMemo, useState } from "react"
import {
  Cell,
  Pie,
  PieChart,
  ResponsiveContainer,
  Sector,
  Tooltip,
} from "recharts"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { ChartContainer } from "@/components/ui/chart"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Tooltip as UITooltip,
} from "@/components/ui/tooltip"
import type { Event, EventCategory } from "@/types/analytics"

interface EventsOverviewProps {
  events: Event[] | null
}

export default function EventsOverview({ events }: EventsOverviewProps) {
  const [view, setView] = useState("chart")
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategories, setSelectedCategories] = useState<EventCategory[]>(
    [],
  )
  const [activeIndex, setActiveIndex] = useState(0)
  const [showFilters, setShowFilters] = useState(false)

  // Get event icon based on category
  const getEventIcon = (category: EventCategory) => {
    switch (category) {
      case "click":
        return <MousePointer className='h-4 w-4' />
      case "form_submit":
        return <FileText className='h-4 w-4' />
      case "video_play":
        return <Play className='h-4 w-4' />
      case "scroll":
        return <ArrowDown className='h-4 w-4' />
      case "social_media":
        return <Share2 className='h-4 w-4' />
      case "custom":
        return <Sparkles className='h-4 w-4' />
      default:
        return <Sparkles className='h-4 w-4' />
    }
  }

  // Get category color
  const getCategoryColor = (category: EventCategory) => {
    switch (category) {
      case "click":
        return "hsl(215, 90%, 50%)"
      case "form_submit":
        return "hsl(280, 90%, 50%)"
      case "video_play":
        return "hsl(10, 90%, 50%)"
      case "scroll":
        return "hsl(150, 90%, 50%)"
      case "social_media":
        return "hsl(40, 90%, 50%)"
      case "custom":
        return "hsl(320, 90%, 50%)"
      default:
        return "hsl(200, 90%, 50%)"
    }
  }

  // Get category badge style
  const getCategoryBadgeStyle = (category: EventCategory) => {
    // const baseColor = getCategoryColor(category)
    switch (category) {
      case "click":
        return "bg-blue-50 text-blue-700 border-blue-200"
      case "form_submit":
        return "bg-purple-50 text-purple-700 border-purple-200"
      case "video_play":
        return "bg-red-50 text-red-700 border-red-200"
      case "scroll":
        return "bg-green-50 text-green-700 border-green-200"
      case "social_media":
        return "bg-amber-50 text-amber-700 border-amber-200"
      case "custom":
        return "bg-pink-50 text-pink-700 border-pink-200"
      default:
        return "bg-slate-50 text-slate-700 border-slate-200"
    }
  }

  // Format category name
  const formatCategoryName = (category: EventCategory) => {
    return (
      category.charAt(0).toUpperCase() + category.slice(1).replace("_", " ")
    )
  }

  // Process events data for the chart
  const chartData = useMemo(() => {
    if (!events || events.length === 0) return []

    const categoryCount = new Map<EventCategory, number>()

    events.forEach((event) => {
      const category = event.category || "custom"
      categoryCount.set(category, (categoryCount.get(category) || 0) + 1)
    })

    return Array.from(categoryCount.entries())
      .map(([category, count]) => ({
        name: formatCategoryName(category),
        value: count,
        category,
        color: getCategoryColor(category),
      }))
      .sort((a, b) => b.value - a.value) // Sort by count descending
  }, [events])

  // Filter events for the table
  const filteredEvents = useMemo(() => {
    if (!events) return []

    return events
      .filter((event) => {
        // Filter by search term
        const matchesSearch =
          searchTerm === "" ||
          event.eventName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (event.path &&
            event.path.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (event.label &&
            event.label.toLowerCase().includes(searchTerm.toLowerCase()))

        // Filter by selected categories
        const matchesCategory =
          selectedCategories.length === 0 ||
          (event.category && selectedCategories.includes(event.category))

        return matchesSearch && matchesCategory
      })
      .sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
      )
      .slice(0, 100) // Limit to 100 events for performance
  }, [events, searchTerm, selectedCategories])

  // Get all available categories
  const availableCategories = useMemo(() => {
    if (!events) return []

    const categories = new Set<EventCategory>()
    events.forEach((event) => {
      if (event.category) categories.add(event.category)
    })

    return Array.from(categories)
  }, [events])

  // Calculate event stats
  const eventStats = useMemo(() => {
    if (!events || events.length === 0)
      return { total: 0, today: 0, categories: 0 }

    const now = new Date()
    const startOfToday = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
    )

    const todayEvents = events.filter(
      (event) => new Date(event.timestamp) >= startOfToday,
    ).length

    return {
      total: events.length,
      today: todayEvents,
      categories: chartData.length,
    }
  }, [events, chartData])

  // Custom active shape for pie chart
  const renderActiveShape = useCallback((props: any) => {
    const RADIAN = Math.PI / 180
    const {
      cx,
      cy,
      midAngle,
      innerRadius,
      outerRadius,
      startAngle,
      endAngle,
      fill,
      payload,
      percent,
      value,
    } = props
    const sin = Math.sin(-RADIAN * midAngle)
    const cos = Math.cos(-RADIAN * midAngle)
    const sx = cx + (outerRadius + 10) * cos
    const sy = cy + (outerRadius + 10) * sin
    const mx = cx + (outerRadius + 30) * cos
    const my = cy + (outerRadius + 30) * sin
    const ex = mx + (cos >= 0 ? 1 : -1) * 22
    const ey = my
    const textAnchor = cos >= 0 ? "start" : "end"

    return (
      <g>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
          stroke='#fff'
          strokeWidth={2}
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 6}
          outerRadius={outerRadius + 10}
          fill={fill}
        />
        <path
          d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`}
          stroke={fill}
          fill='none'
        />
        <circle cx={ex} cy={ey} r={2} fill={fill} stroke='none' />
        <text
          x={ex + (cos >= 0 ? 1 : -1) * 12}
          y={ey}
          textAnchor={textAnchor}
          fill='#333'
          fontSize={12}
        >
          {payload.name}
        </text>
        <text
          x={ex + (cos >= 0 ? 1 : -1) * 12}
          y={ey}
          dy={18}
          textAnchor={textAnchor}
          fill='#999'
          fontSize={12}
        >
          {`${value} events (${(percent * 100).toFixed(0)}%)`}
        </text>
      </g>
    )
  }, [])

  // Custom tooltip for pie chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className='rounded-lg border bg-white p-3 text-sm shadow-lg dark:bg-slate-800'>
          <div className='font-medium'>{data.name}</div>
          <div className='mt-1 text-xs text-muted-foreground'>Category</div>
          <div className='mt-2 flex items-center gap-2'>
            <div
              className='h-3 w-3 rounded-full'
              style={{ backgroundColor: data.color }}
            ></div>
            <div className='font-semibold'>{data.value} events</div>
          </div>
          <div className='mt-1 text-xs text-muted-foreground'>
            {(payload[0].percent * 100).toFixed(1)}% of total
          </div>
        </div>
      )
    }
    return null
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return "Just now"
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`

    return date.toLocaleDateString()
  }

  if (!events) {
    return (
      <Card className='mt-6 border shadow-sm'>
        <CardHeader className='border-b bg-muted/30'>
          <CardTitle className='flex items-center gap-2'>
            <Activity className='h-5 w-5 text-primary' />
            Events Overview
          </CardTitle>
          <CardDescription>User interactions and custom events</CardDescription>
        </CardHeader>
        <CardContent className='flex h-[400px] items-center justify-center p-6'>
          <Skeleton className='h-[350px] w-full rounded-xl' />
        </CardContent>
      </Card>
    )
  }

  if (events.length === 0) {
    return (
      <Card className='mt-6 border shadow-sm'>
        <CardHeader className='border-b bg-muted/30'>
          <CardTitle className='flex items-center gap-2'>
            <Activity className='h-5 w-5 text-primary' />
            Events Overview
          </CardTitle>
          <CardDescription>User interactions and custom events</CardDescription>
        </CardHeader>
        <CardContent className='flex h-[300px] flex-col items-center justify-center p-6'>
          <div className='mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-muted/50'>
            <Activity className='h-8 w-8 text-muted-foreground' />
          </div>
          <p className='text-center text-muted-foreground'>
            No event data available
          </p>
          <p className='mt-1 text-center text-sm text-muted-foreground'>
            Events will appear here once users start interacting with your site
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className='mt-6 overflow-hidden border shadow-md'>
      <div className='h-1 bg-gradient-to-r from-primary via-primary/80 to-primary/50'></div>
      <CardHeader className='border-b bg-gradient-to-b from-muted/50 to-transparent'>
        <div className='flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
          <div>
            <CardTitle className='flex items-center gap-2 text-xl'>
              <div className='rounded-md bg-primary/10 p-1.5'>
                <Activity className='h-5 w-5 text-primary' />
              </div>
              Events Overview
            </CardTitle>
            <CardDescription className='mt-1'>
              User interactions and custom events
            </CardDescription>
          </div>
          <div className='flex items-center gap-2'>
            <Tabs
              value={view}
              onValueChange={setView}
              className='rounded-md border bg-background shadow-sm'
            >
              <TabsList className='grid h-9 grid-cols-2 p-1'>
                <TabsTrigger
                  value='chart'
                  className='flex items-center gap-1.5 px-3 text-xs'
                >
                  <BarChart3 className='h-3.5 w-3.5' />
                  <span>Chart</span>
                </TabsTrigger>
                <TabsTrigger
                  value='table'
                  className='flex items-center gap-1.5 px-3 text-xs'
                >
                  <ListFilter className='h-3.5 w-3.5' />
                  <span>Table</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <TooltipProvider>
              <UITooltip>
                <TooltipTrigger asChild>
                  <Button variant='outline' size='icon' className='h-9 w-9'>
                    <Download className='h-4 w-4' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Export event data</p>
                </TooltipContent>
              </UITooltip>
            </TooltipProvider>
          </div>
        </div>
      </CardHeader>

      <div className='grid grid-cols-1 gap-4 bg-muted/20 p-4 md:grid-cols-3'>
        <div className='flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm'>
          <div>
            <div className='text-sm text-muted-foreground'>Total Events</div>
            <div className='mt-1 text-2xl font-bold'>
              {eventStats.total.toLocaleString()}
            </div>
          </div>
          <div className='flex h-10 w-10 items-center justify-center rounded-full bg-primary/10'>
            <Activity className='h-5 w-5 text-primary' />
          </div>
        </div>

        <div className='flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm'>
          <div>
            <div className='text-sm text-muted-foreground'>
              Today&apos;s Events
            </div>
            <div className='mt-1 text-2xl font-bold'>
              {eventStats.today.toLocaleString()}
            </div>
          </div>
          <div className='flex h-10 w-10 items-center justify-center rounded-full bg-blue-500/10'>
            <Calendar className='h-5 w-5 text-blue-500' />
          </div>
        </div>

        <div className='flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm'>
          <div>
            <div className='text-sm text-muted-foreground'>
              Event Categories
            </div>
            <div className='mt-1 text-2xl font-bold'>
              {eventStats.categories}
            </div>
          </div>
          <div className='flex h-10 w-10 items-center justify-center rounded-full bg-purple-500/10'>
            <Filter className='h-5 w-5 text-purple-500' />
          </div>
        </div>
      </div>

      <CardContent className='p-0'>
        <TabsContent value='chart' className='mt-0 p-6'>
          <div className='h-[400px]'>
            <ChartContainer config={{}}>
              <ResponsiveContainer width='100%' height='100%'>
                <PieChart>
                  <Pie
                    activeIndex={activeIndex}
                    activeShape={renderActiveShape}
                    data={chartData}
                    cx='50%'
                    cy='50%'
                    innerRadius={70}
                    outerRadius={120}
                    fill='#8884d8'
                    dataKey='value'
                    nameKey='name'
                    onMouseEnter={(_, index) => setActiveIndex(index)}
                    paddingAngle={2}
                  >
                    {chartData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={entry.color}
                        stroke='#fff'
                        strokeWidth={2}
                      />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>
          </div>

          <div className='mt-6'>
            <h3 className='mb-3 flex items-center gap-2 text-sm font-medium'>
              <Filter className='h-4 w-4 text-muted-foreground' />
              Event Categories
            </h3>
            <div className='grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-6'>
              {chartData.map((category, index) => (
                <div
                  key={index}
                  className='flex flex-col items-center rounded-lg border bg-card p-3 transition-colors hover:bg-muted/20'
                >
                  <div
                    className='mb-2 flex h-8 w-8 items-center justify-center rounded-full'
                    style={{ backgroundColor: `${category.color}20` }}
                  >
                    {getEventIcon(category.category as EventCategory)}
                  </div>
                  <div className='text-center text-xs font-medium'>
                    {category.name}
                  </div>
                  <div className='mt-1 text-sm font-bold'>{category.value}</div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value='table' className='mt-0 px-0 py-0'>
          <div className='flex flex-col items-center justify-between gap-3 border-y bg-muted/20 px-4 py-3 sm:flex-row'>
            <div className='relative w-full max-w-sm'>
              <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
              <Input
                placeholder='Search events...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='h-9 w-full pl-9'
              />
              {searchTerm && (
                <Button
                  variant='ghost'
                  size='icon'
                  className='absolute right-1 top-1 h-7 w-7 text-muted-foreground hover:text-foreground'
                  onClick={() => setSearchTerm("")}
                >
                  <X className='h-4 w-4' />
                </Button>
              )}
            </div>

            <div className='flex items-center gap-2'>
              <Button
                variant='outline'
                size='sm'
                className={`h-9 ${
                  showFilters
                    ? "border-primary/20 bg-primary/10 text-primary"
                    : ""
                }`}
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className='mr-2 h-4 w-4' />
                Filters
                <Badge className='ml-2' variant='secondary'>
                  {selectedCategories.length || "All"}
                </Badge>
                <ChevronDown className='ml-1 h-3 w-3' />
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant='outline' size='sm' className='h-9'>
                    <Clock className='mr-2 h-4 w-4' />
                    <span className='hidden sm:inline'>Time Range</span>
                    <ChevronDown className='ml-1 h-3 w-3' />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end'>
                  <DropdownMenuLabel>Filter by time</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem checked>
                    All time
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem>
                    Last 24 hours
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem>
                    Last 7 days
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem>
                    Last 30 days
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {showFilters && (
            <div className='border-b bg-muted/10 px-4 py-3'>
              <div className='flex flex-wrap items-center gap-2'>
                <span className='text-sm font-medium'>Categories:</span>
                <Button
                  variant='outline'
                  size='sm'
                  className='h-7 text-xs'
                  onClick={() => setSelectedCategories([])}
                >
                  Clear all
                </Button>
                <Separator orientation='vertical' className='mx-1 h-6' />
                {availableCategories.map((category) => (
                  <Badge
                    key={category}
                    variant='outline'
                    className={`cursor-pointer ${
                      selectedCategories.includes(category)
                        ? getCategoryBadgeStyle(category)
                        : "bg-muted"
                    }`}
                    onClick={() => {
                      if (selectedCategories.includes(category)) {
                        setSelectedCategories(
                          selectedCategories.filter((c) => c !== category),
                        )
                      } else {
                        setSelectedCategories([...selectedCategories, category])
                      }
                    }}
                  >
                    {getEventIcon(category)}
                    <span className='ml-1'>{formatCategoryName(category)}</span>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <ScrollArea className='h-[400px]'>
            <Table>
              <TableHeader className='sticky top-0 z-10 bg-muted/30'>
                <TableRow>
                  <TableHead className='w-[30%]'>Event</TableHead>
                  <TableHead className='w-[15%]'>Category</TableHead>
                  <TableHead className='w-[35%]'>Path</TableHead>
                  <TableHead className='w-[20%] text-right'>Time</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEvents.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className='h-24 text-center'>
                      <div className='flex flex-col items-center justify-center py-8'>
                        <Search className='mb-2 h-8 w-8 text-muted-foreground' />
                        <p className='text-muted-foreground'>
                          No events found.
                        </p>
                        <p className='mt-1 text-xs text-muted-foreground'>
                          Try adjusting your search or filters
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredEvents.map((event, i) => (
                    <TableRow
                      key={i}
                      className='transition-colors hover:bg-muted/20'
                    >
                      <TableCell className='font-medium'>
                        <div className='flex items-center gap-2'>
                          <div
                            className='rounded-md p-1'
                            style={{
                              backgroundColor: event.category
                                ? `${getCategoryColor(event.category)}20`
                                : "hsl(var(--muted))",
                            }}
                          >
                            {event.category && getEventIcon(event.category)}
                          </div>
                          <div>
                            <div className='font-medium'>{event.eventName}</div>
                            {event.label && (
                              <div className='mt-0.5 text-xs text-muted-foreground'>
                                {event.label}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {event.category && (
                          <Badge
                            variant='outline'
                            className={getCategoryBadgeStyle(event.category)}
                          >
                            {formatCategoryName(event.category)}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div
                          className='flex max-w-[200px] items-center gap-1.5 truncate'
                          title={event.path}
                        >
                          <div className='rounded-full bg-muted/50 p-1'>
                            <FileText className='h-3 w-3 text-muted-foreground' />
                          </div>
                          <span className='truncate'>{event.path}</span>
                        </div>
                      </TableCell>
                      <TableCell className='text-right'>
                        <div className='flex items-center justify-end gap-1.5'>
                          <div className='text-xs text-muted-foreground'>
                            {formatDate(event.timestamp)}
                          </div>
                          <TooltipProvider>
                            <UITooltip>
                              <TooltipTrigger asChild>
                                <Info className='h-3.5 w-3.5 cursor-help text-muted-foreground' />
                              </TooltipTrigger>
                              <TooltipContent side='left'>
                                <p className='text-xs'>
                                  {new Date(event.timestamp).toLocaleString()}
                                </p>
                              </TooltipContent>
                            </UITooltip>
                          </TooltipProvider>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </ScrollArea>
        </TabsContent>
      </CardContent>

      <CardFooter className='flex items-center justify-between border-t bg-muted/20 px-4 py-3'>
        <div className='text-xs text-muted-foreground'>
          Showing {filteredEvents.length} of {events.length} events
        </div>
        <div className='flex items-center gap-2'>
          <Badge variant='outline' className='text-xs'>
            Last updated: {new Date().toLocaleTimeString()}
          </Badge>
        </div>
      </CardFooter>
    </Card>
  )
}

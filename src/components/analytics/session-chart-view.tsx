/* eslint-disable simple-import-sort/imports */
"use client"
import {
  <PERSON><PERSON>hart,
  Calendar,
  Clock,
  Eye,
  Laptop,
  LineChart,
  MapPin,
  Monitor,
  RefreshCw,
  Search,
  Smartphone,
  TabletSmartphone,
  Timer,
  Users,
} from "lucide-react"
import { useMemo, useState } from "react"
import {
  Area,
  AreaChart,
  Bar,
  CartesianGrid,
  Cell,
  BarChart as ReBarChart,
  ResponsiveContainer,
  Tooltip,
  type TooltipProps,
  XAxis,
  YAxis,
} from "recharts"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import type { Session } from "@/types/analytics"

interface SessionDetailsProps {
  sessions: Session[] | null
}

const getDeviceIcon = (device?: string) => {
  if (!device) return <Monitor className='h-4 w-4' />
  const deviceLower = device.toLowerCase()
  if (deviceLower.includes("mobile") || deviceLower.includes("phone")) {
    return <Smartphone className='h-4 w-4' />
  } else if (deviceLower.includes("tablet")) {
    return <TabletSmartphone className='h-4 w-4' />
  } else if (
    deviceLower.includes("desktop") ||
    deviceLower.includes("laptop")
  ) {
    return <Laptop className='h-4 w-4' />
  }
  return <Monitor className='h-4 w-4' />
}

// Custom tooltip component for charts
const CustomTooltip = ({
  active,
  payload,
  label,
}: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    return (
      <div className='rounded-lg border bg-card p-4 text-sm shadow-lg'>
        <p className='mb-2 flex items-center gap-1.5 font-medium text-card-foreground'>
          <Calendar className='h-4 w-4 text-muted-foreground' />
          {label}
        </p>
        <p className='flex items-center gap-1.5 text-base font-bold text-primary'>
          <Users className='h-4 w-4' />
          {payload[0].value} sessions
        </p>
        <div className='mt-2 flex items-center gap-1 text-xs text-muted-foreground'>
          <Clock className='h-3 w-3' />
          Avg. duration: 3m 42s
        </div>
      </div>
    )
  }
  return null
}

const ChartComponent = ({
  data,
  chartType,
}: {
  data: any[]
  chartType: string
}) => {
  // Get the maximum value to calculate bar/area height proportions
  const maxValue = Math.max(...data.map((item) => item.count))

  // Add a small buffer above max for better visualization
  const yAxisMax = Math.ceil(maxValue * 1.2)

  return (
    <ResponsiveContainer width='100%' height='100%'>
      {chartType === "bar" ? (
        <ReBarChart data={data} barCategoryGap={8}>
          <defs>
            <linearGradient id='barGradient' x1='0' y1='0' x2='0' y2='1'>
              <stop
                offset='0%'
                stopColor='hsl(var(--primary))'
                stopOpacity={1}
              />
              <stop
                offset='100%'
                stopColor='hsl(var(--primary))'
                stopOpacity={0.6}
              />
            </linearGradient>
          </defs>
          <CartesianGrid
            strokeDasharray='3 3'
            vertical={false}
            stroke='hsl(var(--border))'
            opacity={0.4}
          />
          <XAxis
            dataKey='date'
            axisLine={false}
            tickLine={false}
            tick={{ fill: "hsl(var(--muted-foreground))", fontSize: 12 }}
          />
          <YAxis
            allowDecimals={false}
            axisLine={false}
            tickLine={false}
            tick={{ fill: "hsl(var(--muted-foreground))", fontSize: 12 }}
            domain={[0, yAxisMax]}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={{ fill: "hsl(var(--muted) / 0.3)" }}
          />
          <Bar
            dataKey='count'
            fill='url(#barGradient)'
            radius={[4, 4, 0, 0]}
            className='!hover:opacity-80 transition-opacity duration-200'
            animationDuration={800}
          >
            {data.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={
                  entry.count === maxValue
                    ? "hsl(var(--primary))"
                    : "url(#barGradient)"
                }
                opacity={entry.count === maxValue ? 1 : 0.85}
              />
            ))}
          </Bar>
        </ReBarChart>
      ) : (
        <AreaChart data={data}>
          <defs>
            <linearGradient id='areaGradient' x1='0' y1='0' x2='0' y2='1'>
              <stop
                offset='0%'
                stopColor='hsl(var(--primary) / 0.5)'
                stopOpacity={0.8}
              />
              <stop
                offset='60%'
                stopColor='hsl(var(--primary) / 0.2)'
                stopOpacity={0.4}
              />
              <stop
                offset='100%'
                stopColor='hsl(var(--primary) / 0.1)'
                stopOpacity={0.1}
              />
            </linearGradient>
          </defs>
          <CartesianGrid
            strokeDasharray='3 3'
            vertical={false}
            stroke='hsl(var(--border))'
            opacity={0.4}
          />
          <XAxis
            dataKey='date'
            axisLine={false}
            tickLine={false}
            tick={{ fill: "hsl(var(--muted-foreground))", fontSize: 12 }}
          />
          <YAxis
            allowDecimals={false}
            axisLine={false}
            tickLine={false}
            tick={{ fill: "hsl(var(--muted-foreground))", fontSize: 12 }}
            domain={[0, yAxisMax]}
          />
          <Tooltip content={<CustomTooltip />} />
          <Area
            type='monotone'
            dataKey='count'
            stroke='hsl(var(--primary))'
            strokeWidth={2.5}
            fill='url(#areaGradient)'
            activeDot={{
              r: 6,
              fill: "hsl(var(--primary))",
              strokeWidth: 2,
              stroke: "hsl(var(--background))",
            }}
            animationDuration={1200}
          />
        </AreaChart>
      )}
    </ResponsiveContainer>
  )
}

export default function SessionDetailsChart({ sessions }: SessionDetailsProps) {
  const [timeRange, setTimeRange] = useState("24h")
  const [chartType, setChartType] = useState("bar")
  const [searchQuery, setSearchQuery] = useState("")

  const sessionChartData = useMemo(() => {
    if (!sessions) return []
    const dataMap = new Map()

    sessions.forEach((session) => {
      const date = new Date(session.sessionStart)
      let key = date.toLocaleDateString()
      if (timeRange === "24h")
        key = `${date.toLocaleDateString()} ${date.getHours()}:00`
      dataMap.set(key, (dataMap.get(key) || 0) + 1)
    })

    // Convert to array and sort chronologically
    const dataArray = Array.from(dataMap, ([date, count]) => ({ date, count }))
    return dataArray.sort((a, b) => {
      // Simple string comparison should work if dates are formatted consistently
      return a.date.localeCompare(b.date)
    })
  }, [sessions, timeRange])

  // Calculate stats
  const totalSessions = useMemo(() => {
    return sessions?.length || 0
  }, [sessions])

  const returningVisitors = useMemo(() => {
    return sessions?.filter((s) => s.visitor.isReturningVisitor).length || 0
  }, [sessions])

  const returningPercentage =
    totalSessions > 0
      ? Math.round((returningVisitors / totalSessions) * 100)
      : 0

  const filteredSessions = useMemo(() => {
    if (!sessions) return []
    if (!searchQuery) return sessions

    const query = searchQuery.toLowerCase()
    return sessions.filter(
      (session) =>
        session.geo?.city?.toLowerCase().includes(query) ||
        session.geo?.country?.toLowerCase().includes(query) ||
        session.visitor?.device?.toLowerCase().includes(query) ||
        session.visitor?.browser?.toLowerCase().includes(query),
    )
  }, [sessions, searchQuery])

  return (
    <Card className='mt-6 overflow-hidden border bg-card shadow-md transition-shadow duration-300 hover:shadow-lg'>
      <div className='h-1.5 bg-gradient-to-r from-primary via-primary/80 to-primary/50'></div>
      <CardHeader className='border-b bg-gradient-to-b from-muted/50 to-transparent'>
        <div className='flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
          <div className='space-y-1'>
            <div className='flex items-center gap-2'>
              <div className='rounded-md bg-primary/10 p-1.5'>
                <Users className='h-5 w-5 text-primary' />
              </div>
              <CardTitle>Session Analytics</CardTitle>
            </div>
            <CardDescription className='flex items-center gap-1.5'>
              <Eye className='h-3.5 w-3.5 text-muted-foreground' />
              Visitor session details and trends
            </CardDescription>
          </div>
          <div className='flex flex-wrap items-center gap-2'>
            <Tabs
              value={timeRange}
              onValueChange={setTimeRange}
              className='mr-1'
            >
              <TabsList className='grid h-9 grid-cols-3 p-1'>
                <TabsTrigger value='24h' className='px-3 text-xs'>
                  24h
                </TabsTrigger>
                <TabsTrigger value='7d' className='px-3 text-xs'>
                  7d
                </TabsTrigger>
                <TabsTrigger value='30d' className='px-3 text-xs'>
                  30d
                </TabsTrigger>
              </TabsList>
            </Tabs>
            <div className='flex rounded-md border bg-background p-1'>
              <Button
                variant='ghost'
                size='icon'
                onClick={() => setChartType("bar")}
                className={cn(
                  "h-7 w-7",
                  chartType === "bar" &&
                    "bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground",
                )}
                title='Bar Chart'
              >
                <BarChart className='h-4 w-4' />
              </Button>
              <Button
                variant='ghost'
                size='icon'
                onClick={() => setChartType("area")}
                className={cn(
                  "h-7 w-7",
                  chartType === "area" &&
                    "bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground",
                )}
                title='Area Chart'
              >
                <LineChart className='h-4 w-4' />
              </Button>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='outline' size='sm' className='h-9 gap-1'>
                  <RefreshCw className='h-3.5 w-3.5' />
                  <span className='hidden md:inline'>Refresh</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuLabel>Refresh Options</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <RefreshCw className='mr-2 h-4 w-4' />
                  Refresh Now
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Clock className='mr-2 h-4 w-4' />
                  Auto-refresh (30s)
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <div className='grid grid-cols-1 gap-4 bg-muted/20 p-4 md:grid-cols-3'>
        <div className='flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm'>
          <div>
            <div className='text-sm text-muted-foreground'>Total Sessions</div>
            <div className='mt-1 text-2xl font-bold'>
              {totalSessions.toLocaleString()}
            </div>
          </div>
          <div className='flex h-10 w-10 items-center justify-center rounded-full bg-primary/10'>
            <Users className='h-5 w-5 text-primary' />
          </div>
        </div>

        <div className='flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm'>
          <div>
            <div className='text-sm text-muted-foreground'>
              Returning Visitors
            </div>
            <div className='mt-1 text-2xl font-bold'>
              {returningVisitors.toLocaleString()}
            </div>
          </div>
          <div className='flex h-10 w-10 items-center justify-center rounded-full bg-primary/10'>
            <RefreshCw className='h-5 w-5 text-primary' />
          </div>
        </div>

        <div className='flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm'>
          <div>
            <div className='text-sm text-muted-foreground'>Returning Rate</div>
            <div className='mt-1 text-2xl font-bold'>
              {returningPercentage}%
            </div>
          </div>
          <div className='flex items-center gap-2'>
            <div className='h-3 w-16 overflow-hidden rounded-full bg-muted'>
              <div
                className='h-full bg-primary'
                style={{ width: `${returningPercentage}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      <CardContent className='p-0'>
        <div className='h-72 w-full bg-gradient-to-b from-card to-card/95 p-6'>
          <ChartComponent data={sessionChartData} chartType={chartType} />
        </div>

        <div className='flex flex-col items-center justify-between gap-3 border-y bg-muted/20 px-4 py-3 sm:flex-row'>
          <div className='flex items-center gap-1.5'>
            <MapPin className='h-4 w-4 text-primary' />
            <h3 className='text-sm font-semibold'>Recent Sessions</h3>
            <Badge variant='outline' className='ml-2'>
              {filteredSessions.length} of {totalSessions}
            </Badge>
          </div>

          <div className='relative w-full sm:w-auto sm:min-w-[240px]'>
            <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search location, device...'
              className='h-9 w-full pl-9'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <ScrollArea className='h-[400px]'>
          <SessionTable sessions={filteredSessions} />
        </ScrollArea>
      </CardContent>

      <CardFooter className='flex items-center justify-between border-t bg-muted/20 px-4 py-3'>
        <div className='text-xs text-muted-foreground'>
          Showing data for:{" "}
          <span className='font-medium'>
            {timeRange === "24h"
              ? "Last 24 hours"
              : timeRange === "7d"
                ? "Last 7 days"
                : "Last 30 days"}
          </span>
        </div>
        <Button variant='outline' size='sm' className='h-8 gap-1.5'>
          {/* <ExternalLink className="h-3.5 w-3.5" />
          <span>Export Data</span> */}
          Latest Sessions
        </Button>
      </CardFooter>
    </Card>
  )
}

function SessionTable({ sessions }: { sessions: Session[] }) {
  return (
    <Table>
      <TableHeader className='sticky top-0 z-10 bg-muted/30'>
        <TableRow className='hover:bg-muted/50'>
          <TableHead className='w-[180px] font-medium'>Location</TableHead>
          <TableHead className='font-medium'>Device / Browser</TableHead>
          <TableHead className='font-medium'>Duration</TableHead>
          <TableHead className='font-medium'>Screen Size</TableHead>
          <TableHead className='text-right font-medium'>Status</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {sessions.length === 0 ? (
          <TableRow>
            <TableCell
              colSpan={5}
              className='h-24 text-center text-muted-foreground'
            >
              No sessions found.
            </TableCell>
          </TableRow>
        ) : (
          sessions.map((session, i) => (
            <TableRow key={i} className='transition-colors hover:bg-muted/20'>
              <TableCell>
                <div className='flex items-center gap-2'>
                  <div className='rounded-md bg-primary/10 p-1'>
                    <MapPin className='h-3.5 w-3.5 text-primary' />
                  </div>
                  <div>
                    <span className='block font-medium'>
                      {session.geo?.city || "Unknown City"}
                    </span>
                    <span className='text-xs text-muted-foreground'>
                      {session.geo?.country || "Unknown Country"}
                    </span>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className='flex items-center gap-2'>
                  <div className='rounded-md bg-secondary/20 p-1'>
                    {getDeviceIcon(session.visitor?.device)}
                  </div>
                  <div>
                    <span className='block'>
                      {session.visitor?.device || "Unknown Device"}
                    </span>
                    <span className='text-xs text-muted-foreground'>
                      {session.visitor?.browser || "Unknown Browser"}
                    </span>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className='flex items-center gap-2'>
                  <div className='rounded-md bg-yellow-500/10 p-1'>
                    <Timer className='h-3.5 w-3.5 text-yellow-500' />
                  </div>
                  <span>3m 42s</span>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant='outline' className='font-mono text-xs'>
                  {session.visitor?.screenResolution || "Unknown"}
                </Badge>
              </TableCell>
              <TableCell className='text-right'>
                <Badge
                  variant={
                    session.visitor.isReturningVisitor ? "default" : "secondary"
                  }
                  className={
                    session.visitor.isReturningVisitor
                      ? "bg-emerald-100 text-emerald-700 hover:bg-emerald-200"
                      : "bg-blue-100 text-blue-700 hover:bg-blue-200"
                  }
                >
                  {session.visitor.isReturningVisitor ? "Returning" : "New"}
                </Badge>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  )
}

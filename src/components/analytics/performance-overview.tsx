"use client"

import {
  Clock,
  Download,
  HelpCircle,
  LayoutGrid,
  <PERSON><PERSON>ointer,
  Server,
  Zap,
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import {
  <PERSON>ltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { WebsiteAnalytics } from "@/types/analytics"

interface PerformanceOverviewProps {
  websiteAnalytics: WebsiteAnalytics | null
}

export default function PerformanceOverview({
  websiteAnalytics,
}: PerformanceOverviewProps) {
  const getPerformanceStatus = (metric: string, value: number) => {
    switch (metric) {
      case "FCP":
        return value < 1800
          ? "good"
          : value < 3000
            ? "needs-improvement"
            : "poor"
      case "LCP":
        return value < 2500
          ? "good"
          : value < 4000
            ? "needs-improvement"
            : "poor"
      case "FID":
        return value < 100 ? "good" : value < 300 ? "needs-improvement" : "poor"
      case "CLS":
        return value < 0.1
          ? "good"
          : value < 0.25
            ? "needs-improvement"
            : "poor"
      case "TTFB":
        return value < 500
          ? "good"
          : value < 1000
            ? "needs-improvement"
            : "poor"
      case "Load":
        return value < 3000
          ? "good"
          : value < 5000
            ? "needs-improvement"
            : "poor"
      default:
        return "good"
    }
  }

  const getProgressValue = (metric: string, value: number) => {
    switch (metric) {
      case "FCP":
        return Math.min(100, (value / 3000) * 100)
      case "LCP":
        return Math.min(100, (value / 4000) * 100)
      case "FID":
        return Math.min(100, (value / 300) * 100)
      case "CLS":
        return Math.min(100, (value / 0.25) * 100)
      case "TTFB":
        return Math.min(100, (value / 1000) * 100)
      case "Load":
        return Math.min(100, (value / 5000) * 100)
      default:
        return 50
    }
  }

  const formatTime = (ms: number) =>
    ms < 1000 ? `${ms.toFixed(0)}ms` : `${(ms / 1000).toFixed(1)}s`
  const formatCLS = (value: number) => value.toFixed(2)

  const bestPerformance = {
    avgLCP: 1200,
    avgFID: 50,
    avgCLS: 0.05,
    avgFCP: 900,
    avgTTFB: 300,
    avgLoadTime: 2000,
  }

  const finalPerformance = Object.assign(
    websiteAnalytics?.performance ?? {},
    bestPerformance,
  )

  // const performance = websiteAnalytics?.performance || bestPerformance;
  const performance = finalPerformance

  // const getStatusIcon = (status: string) => {
  //   switch (status) {
  //     case "good":
  //       return <CheckCircle2 className='h-4 w-4 text-emerald-500' />
  //     case "needs-improvement":
  //       return <AlertTriangle className='h-4 w-4 text-amber-500' />
  //     case "poor":
  //       return <XCircle className='h-4 w-4 text-rose-500' />
  //     default:
  //       return null
  //   }
  // }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "good":
        return "bg-emerald-500"
      case "needs-improvement":
        return "bg-amber-500"
      case "poor":
        return "bg-rose-500"
      default:
        return "bg-slate-500"
    }
  }

  // const getStatusText = (status: string) => {
  //   switch (status) {
  //     case "good":
  //       return "Good"
  //     case "needs-improvement":
  //       return "Needs Improvement"
  //     case "poor":
  //       return "Poor"
  //     default:
  //       return "Unknown"
  //   }
  // }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "good":
        return (
          <Badge
            variant='outline'
            className='border-emerald-200 bg-emerald-50 text-emerald-700'
          >
            Good
          </Badge>
        )
      case "needs-improvement":
        return (
          <Badge
            variant='outline'
            className='border-amber-200 bg-amber-50 text-amber-700'
          >
            Needs Improvement
          </Badge>
        )
      case "poor":
        return (
          <Badge
            variant='outline'
            className='border-rose-200 bg-rose-50 text-rose-700'
          >
            Poor
          </Badge>
        )
      default:
        return null
    }
  }

  const metrics = [
    {
      key: "avgLCP",
      label: "Largest Contentful Paint",
      shortLabel: "LCP",
      icon: Clock,
      formatter: formatTime,
      description:
        "Measures loading performance. To provide a good user experience, LCP should occur within 2.5 seconds of when the page first starts loading.",
    },
    {
      key: "avgFID",
      label: "First Input Delay",
      shortLabel: "FID",
      icon: MousePointer,
      formatter: formatTime,
      description:
        "Measures interactivity. To provide a good user experience, pages should have a FID of 100 milliseconds or less.",
    },
    {
      key: "avgCLS",
      label: "Cumulative Layout Shift",
      shortLabel: "CLS",
      icon: LayoutGrid,
      formatter: formatCLS,
      description:
        "Measures visual stability. To provide a good user experience, pages should maintain a CLS of 0.1 or less.",
    },
    {
      key: "avgFCP",
      label: "First Contentful Paint",
      shortLabel: "FCP",
      icon: Zap,
      formatter: formatTime,
      description:
        "Measures the time from when the page starts loading to when any part of the page's content is rendered on the screen.",
    },
    {
      key: "avgTTFB",
      label: "Time to First Byte",
      shortLabel: "TTFB",
      icon: Server,
      formatter: formatTime,
      description:
        "Measures the time between the request for a resource and when the first byte of a response begins to arrive.",
    },
    {
      key: "avgLoadTime",
      label: "Total Load Time",
      shortLabel: "Load",
      icon: Download,
      formatter: formatTime,
      description:
        "Measures the total time it takes for the page to fully load.",
    },
  ]

  // Filter core web vitals (LCP, FID, CLS)
  const coreVitals = metrics.filter((metric) =>
    ["avgLCP", "avgFID", "avgCLS"].includes(metric.key),
  )

  return (
    <Card className='col-span-6 overflow-hidden lg:col-span-3'>
      <CardHeader className='border-b bg-muted/20'>
        <div className='flex items-center justify-between'>
          <div>
            <CardTitle className='text-xl'>Performance Metrics</CardTitle>
            <CardDescription className='mt-1 text-sm'>
              Core Web Vitals and performance metrics
            </CardDescription>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className='h-5 w-5 cursor-help text-muted-foreground' />
              </TooltipTrigger>
              <TooltipContent className='max-w-xs'>
                <p>
                  These metrics measure your website&apos;s performance and user
                  experience.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent className='p-0'>
        <Tabs defaultValue='core-vitals' className='w-full'>
          <TabsList className='grid w-full grid-cols-2 rounded-none border-b bg-slate-50 dark:bg-slate-900'>
            <TabsTrigger
              value='core-vitals'
              className='rounded-none data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800'
            >
              Core Web Vitals
            </TabsTrigger>
            <TabsTrigger
              value='all-metrics'
              className='rounded-none data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800'
            >
              All Metrics
            </TabsTrigger>
          </TabsList>

          <TabsContent value='core-vitals' className='mt-0 space-y-6 p-6'>
            {coreVitals.map(
              ({
                key,
                label,
                shortLabel,
                icon: Icon,
                formatter,
                description,
              }) => {
                const value = performance[key as keyof typeof performance]
                const status = getPerformanceStatus(shortLabel, value as number)

                return (
                  <div key={key} className='space-y-3'>
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-2'>
                        <div
                          className={`rounded-full p-2 ${
                            status === "good"
                              ? "bg-emerald-100"
                              : status === "needs-improvement"
                                ? "bg-amber-100"
                                : "bg-rose-100"
                          }`}
                        >
                          <Icon
                            className={`h-5 w-5 ${
                              status === "good"
                                ? "text-emerald-600"
                                : status === "needs-improvement"
                                  ? "text-amber-600"
                                  : "text-rose-600"
                            }`}
                          />
                        </div>
                        <div>
                          <div className='flex items-center gap-2'>
                            <span className='text-sm font-medium'>{label}</span>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <HelpCircle className='h-4 w-4 cursor-help text-muted-foreground' />
                                </TooltipTrigger>
                                <TooltipContent className='max-w-xs'>
                                  <p>{description}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                          <div className='mt-0.5 text-xs text-muted-foreground'>
                            {getStatusBadge(status)}
                          </div>
                        </div>
                      </div>
                      <div className='text-right'>
                        <span className='text-lg font-semibold'>
                          {formatter(value as number)}
                        </span>
                      </div>
                    </div>
                    <div className='relative pt-1'>
                      <div className='mb-1 flex items-center justify-between'>
                        <div className='text-xs text-muted-foreground'>0</div>
                        <div className='text-xs text-muted-foreground'>
                          {key === "avgCLS"
                            ? "0.25"
                            : key === "avgFID"
                              ? "300ms"
                              : key === "avgLCP"
                                ? "4s"
                                : ""}
                        </div>
                      </div>
                      <div className='flex h-2 overflow-hidden rounded-full bg-slate-200 text-xs dark:bg-slate-700'>
                        <div
                          style={{
                            width: `${getProgressValue(
                              shortLabel,
                              value as number,
                            )}%`,
                          }}
                          className={`flex flex-col justify-center whitespace-nowrap text-center text-white shadow-none transition-all duration-500 ${getStatusColor(
                            status,
                          )}`}
                        ></div>
                      </div>
                    </div>
                  </div>
                )
              },
            )}
          </TabsContent>

          <TabsContent value='all-metrics' className='mt-0 space-y-4 p-6'>
            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
              {metrics.map(
                ({ key, label, shortLabel, icon: Icon, formatter }) => {
                  const value = performance[key as keyof typeof performance]
                  const status = getPerformanceStatus(
                    shortLabel,
                    value as number,
                  )

                  return (
                    <div
                      key={key}
                      className={`rounded-lg border p-4 ${
                        status === "good"
                          ? "border-emerald-100 bg-emerald-50"
                          : status === "needs-improvement"
                            ? "border-amber-100 bg-amber-50"
                            : "border-rose-100 bg-rose-50"
                      } dark:bg-opacity-10`}
                    >
                      <div className='mb-2 flex items-center justify-between'>
                        <div className='flex items-center gap-2'>
                          <Icon
                            className={`h-4 w-4 ${
                              status === "good"
                                ? "text-emerald-600"
                                : status === "needs-improvement"
                                  ? "text-amber-600"
                                  : "text-rose-600"
                            }`}
                          />
                          <span className='text-sm font-medium'>
                            {shortLabel}
                          </span>
                        </div>
                        <span
                          className={`text-sm font-semibold ${
                            status === "good"
                              ? "text-emerald-700"
                              : status === "needs-improvement"
                                ? "text-amber-700"
                                : "text-rose-700"
                          }`}
                        >
                          {formatter(value as number)}
                        </span>
                      </div>
                      <div className='mb-2 text-xs text-muted-foreground'>
                        {label}
                      </div>
                      <div className='flex h-1.5 overflow-hidden rounded-full bg-white text-xs dark:bg-slate-700'>
                        <div
                          style={{
                            width: `${getProgressValue(
                              shortLabel,
                              value as number,
                            )}%`,
                          }}
                          className={`flex flex-col justify-center whitespace-nowrap text-center text-white shadow-none ${getStatusColor(
                            status,
                          )}`}
                        ></div>
                      </div>
                    </div>
                  )
                },
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

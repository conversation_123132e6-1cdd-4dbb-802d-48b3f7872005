"use client"

import { <PERSON>, FileText, Minus, TrendingDown, TrendingUp } from "lucide-react"
import { useMemo, useState } from "react"
import {
  Area,
  AreaChart,
  CartesianGrid,
  ReferenceLine,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from "recharts"

import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import type { Pageview } from "@/types/analytics"

interface PageViewsChartProps {
  pageViews: Pageview[] | null
}

export default function PageViewsChart({ pageViews }: PageViewsChartProps) {
  const [timeRange, setTimeRange] = useState("7d")

  const chartData = useMemo(() => {
    if (!pageViews || pageViews.length === 0) return []

    const sortedViews = [...pageViews].sort(
      (a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
    )
    const now = new Date()
    const timeRangeMs =
      timeRange === "24h"
        ? 24 * 60 * 60 * 1000
        : timeRange === "7d"
          ? 7 * 24 * 60 * 60 * 1000
          : 30 * 24 * 60 * 60 * 1000

    const filteredViews = sortedViews.filter(
      (view) =>
        now.getTime() - new Date(view.timestamp).getTime() <= timeRangeMs,
    )

    const groupedData = new Map()
    filteredViews.forEach((view) => {
      const date = new Date(view.timestamp)
      let key

      if (timeRange === "24h") {
        key = `${date.getHours().toString().padStart(2, "0")}:00`
      } else if (timeRange === "7d") {
        key = date.toLocaleDateString(undefined, { weekday: "short" })
      } else {
        key = date.toLocaleDateString(undefined, {
          month: "short",
          day: "numeric",
        })
      }

      if (!groupedData.has(key)) {
        groupedData.set(key, { name: key, views: 0, uniquePaths: new Set() })
      }

      const entry = groupedData.get(key)
      entry.views++
      entry.uniquePaths.add(view.path)
    })

    return Array.from(groupedData.values()).map((entry) => ({
      name: entry.name,
      views: entry.views,
      uniquePaths: entry.uniquePaths.size,
    }))
  }, [pageViews, timeRange])

  // const topPages = useMemo(() => {
  //   if (!pageViews || pageViews.length === 0) return []

  //   const pageCount = new Map()
  //   pageViews.forEach((view) => {
  //     const path = view.path
  //     pageCount.set(path, (pageCount.get(path) || 0) + 1)
  //   })

  //   return Array.from(pageCount.entries())
  //     .map(([path, count]) => ({ path, count }))
  //     .sort((a, b) => b.count - a.count)
  //     .slice(0, 5)
  // }, [pageViews])

  const totalViews = useMemo(() => {
    if (!chartData.length) return 0
    return chartData.reduce((sum, item) => sum + item.views, 0)
  }, [chartData])

  const uniquePages = useMemo(() => {
    if (!chartData.length) return 0
    const uniqueSet = new Set()
    chartData.forEach((item) => {
      for (let i = 0; i < item.uniquePaths; i++) {
        uniqueSet.add(`${item.name}-${i}`)
      }
    })
    return uniqueSet.size
  }, [chartData])

  // Calculate trend compared to previous period
  const viewsTrend = useMemo(() => {
    if (!pageViews || pageViews.length === 0)
      return { percentage: 0, direction: "neutral" }

    const now = new Date()
    const currentPeriodMs =
      timeRange === "24h"
        ? 24 * 60 * 60 * 1000
        : timeRange === "7d"
          ? 7 * 24 * 60 * 60 * 1000
          : 30 * 24 * 60 * 60 * 1000

    const previousPeriodMs = currentPeriodMs * 2

    const currentPeriodViews = pageViews.filter(
      (view) =>
        now.getTime() - new Date(view.timestamp).getTime() <= currentPeriodMs,
    ).length

    const previousPeriodViews = pageViews.filter((view) => {
      const time = new Date(view.timestamp).getTime()
      return (
        time <= now.getTime() - currentPeriodMs &&
        time > now.getTime() - previousPeriodMs
      )
    }).length

    if (previousPeriodViews === 0) return { percentage: 100, direction: "up" }

    const percentageChange =
      ((currentPeriodViews - previousPeriodViews) / previousPeriodViews) * 100

    return {
      percentage: Math.abs(Math.round(percentageChange)),
      direction:
        percentageChange > 0 ? "up" : percentageChange < 0 ? "down" : "neutral",
    }
  }, [pageViews, timeRange])

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case "up":
        return <TrendingUp className='h-4 w-4 text-emerald-500' />
      case "down":
        return <TrendingDown className='h-4 w-4 text-rose-500' />
      default:
        return <Minus className='h-4 w-4 text-slate-500' />
    }
  }

  const getTrendColor = (direction: string) => {
    switch (direction) {
      case "up":
        return "text-emerald-500"
      case "down":
        return "text-rose-500"
      default:
        return "text-slate-500"
    }
  }

  // const getPageIcon = (path: string) => {
  //   if (path === "/") return "🏠"
  //   if (path.includes("blog")) return "📝"
  //   if (path.includes("about")) return "ℹ️"
  //   if (path.includes("contact")) return "📞"
  //   if (path.includes("product")) return "🛒"
  //   return "📄"
  // }

  return (
    <Card className='col-span-6 w-full overflow-hidden lg:col-span-3'>
      <CardHeader className='from-primary-50 to-primary-100 border-b bg-muted/20 pb-6'>
        <div className='flex flex-row flex-wrap items-center justify-between gap-4'>
          <div className='flex flex-col space-y-1.5'>
            <CardTitle className='flex items-center gap-2 text-xl'>
              <Eye className='h-5 w-5 text-primary' />
              Page Views
            </CardTitle>
            <CardDescription className='text-sm'>
              Page view trends over time
            </CardDescription>
          </div>
          <Tabs
            value={timeRange}
            onValueChange={setTimeRange}
            className='rounded-md bg-white shadow-sm dark:bg-slate-800'
          >
            <TabsList className='grid h-9 grid-cols-3'>
              <TabsTrigger value='24h' className='px-3 text-xs'>
                24h
              </TabsTrigger>
              <TabsTrigger value='7d' className='px-3 text-xs'>
                7d
              </TabsTrigger>
              <TabsTrigger value='30d' className='px-3 text-xs'>
                30d
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div className='mt-4 grid grid-cols-2 gap-4'>
          <div className='rounded-lg bg-secondary p-3 shadow-sm'>
            <div className='mb-1 text-sm text-muted-foreground'>
              Total Views
            </div>
            <div className='flex items-center justify-between'>
              <div className='text-2xl font-bold'>
                {totalViews.toLocaleString()}
              </div>
              <div className='flex items-center gap-1'>
                {getTrendIcon(viewsTrend.direction)}
                <span
                  className={`text-sm font-medium ${getTrendColor(viewsTrend.direction)}`}
                >
                  {viewsTrend.percentage}%
                </span>
              </div>
            </div>
          </div>

          <div className='rounded-lg bg-secondary p-3 shadow-sm'>
            <div className='mb-1 text-sm text-muted-foreground'>
              Unique Pages
            </div>
            <div className='flex items-center justify-between'>
              <div className='text-2xl font-bold'>{uniquePages}</div>
              <Badge
                variant='outline'
                className='border-primary/20 bg-primary/10 text-primary'
              >
                <FileText className='mr-1 h-3 w-3' />
                Pages
              </Badge>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className='p-6'>
        <div className='mb-6 h-[250px] w-full'>
          <ChartContainer
            config={{
              views: {
                label: "Page Views",
                color: "hsl(215, 90%, 50%)",
              },
              uniquePaths: {
                label: "Unique Pages",
                color: "hsl(280, 90%, 50%)",
              },
            }}
          >
            <ResponsiveContainer width='100%' height='100%'>
              <AreaChart
                data={chartData}
                margin={{ top: 10, right: 10, left: 0, bottom: 0 }}
              >
                <defs>
                  <linearGradient id='colorViews' x1='0' y1='0' x2='0' y2='1'>
                    <stop
                      offset='5%'
                      stopColor='hsl(215, 90%, 50%)'
                      stopOpacity={0.3}
                    />
                    <stop
                      offset='95%'
                      stopColor='hsl(215, 90%, 50%)'
                      stopOpacity={0}
                    />
                  </linearGradient>
                  <linearGradient id='colorUnique' x1='0' y1='0' x2='0' y2='1'>
                    <stop
                      offset='5%'
                      stopColor='hsl(280, 90%, 50%)'
                      stopOpacity={0.3}
                    />
                    <stop
                      offset='95%'
                      stopColor='hsl(280, 90%, 50%)'
                      stopOpacity={0}
                    />
                  </linearGradient>
                </defs>
                <CartesianGrid
                  strokeDasharray='3 3'
                  vertical={false}
                  stroke='rgba(0,0,0,0.1)'
                />
                <XAxis
                  dataKey='name'
                  tickLine={false}
                  axisLine={false}
                  tick={{ fontSize: 12 }}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tick={{ fontSize: 12 }}
                  width={30}
                />
                <ChartTooltip content={<ChartTooltipContent />} />
                <ReferenceLine y={0} stroke='#666' />
                <Area
                  type='monotone'
                  dataKey='views'
                  stroke='hsl(215, 90%, 50%)'
                  strokeWidth={2}
                  fill='url(#colorViews)'
                  activeDot={{ r: 6, strokeWidth: 0 }}
                />
                <Area
                  type='monotone'
                  dataKey='uniquePaths'
                  stroke='hsl(280, 90%, 50%)'
                  strokeWidth={2}
                  fill='url(#colorUnique)'
                  activeDot={{ r: 6, strokeWidth: 0 }}
                />
              </AreaChart>
            </ResponsiveContainer>
          </ChartContainer>
        </div>

        <Separator className='my-4' />

        <div>
          <div className='mb-4 flex items-center justify-between'>
            <h4 className='flex items-center gap-2 text-sm font-medium'>
              <FileText className='h-4 w-4 text-primary' />
              Top Pages
            </h4>
            <Badge variant='outline' className='text-xs'>
              {timeRange === "24h"
                ? "Last 24 hours"
                : timeRange === "7d"
                  ? "Last 7 days"
                  : "Last 30 days"}
            </Badge>
          </div>

          {/* <div className='space-y-3'>
            {topPages.map((page, i) => (
              <div
                key={i}
                className='flex w-full items-center justify-between rounded-md p-2 transition-colors hover:bg-slate-50 dark:hover:bg-slate-800'
              >
                <div className='flex max-w-[70%] items-center gap-2'>
                  <div className='flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-md bg-primary/10 text-lg'>
                    {getPageIcon(page.path)}
                  </div>
                  <div
                    className='truncate text-sm font-medium'
                    title={page.path}
                  >
                    {page.path === "/"
                      ? "Homepage"
                      : page.path.replace(/^\/|\/$/g, "")}
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <Badge variant='secondary' className='font-mono'>
                    {page.count.toLocaleString()}
                  </Badge>
                  <div className='text-xs text-muted-foreground'>views</div>
                </div>
              </div>
            ))}

            {topPages.length === 0 && (
              <div className='py-6 text-center text-muted-foreground'>
                No page views data available
              </div>
            )}
          </div> */}
        </div>
      </CardContent>
    </Card>
  )
}

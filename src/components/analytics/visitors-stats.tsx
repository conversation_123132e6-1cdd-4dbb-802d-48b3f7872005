"use client"

import {
  Activity,
  <PERSON>R<PERSON>,
  Bar<PERSON>hart3,
  Calendar,
  Clock,
  Eye,
  Info,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react"
import React from "react"

import { Badge } from "@/components/ui/badge"
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import type { Pageview, Session, WebsiteAnalytics } from "@/types/analytics"

interface VisitorsStatsProps {
  isLoadingWebsite: boolean
  websiteAnalytics: WebsiteAnalytics | null
  pageViews: Pageview[] | null
  sessions: Session[] | null
}

interface StatCardProps {
  title: string
  icon: JSX.Element
  value: number | null
  previousValue?: number | null
  isLoading: boolean
  footerText?: string
  color: string
  tooltip?: string
  timeframe?: string
}

const StatCard = ({
  title,
  icon,
  value,
  previousValue,
  isLoading,
  footerText,
  color,
  tooltip,
  timeframe = "Today",
}: StatCardProps) => {
  // Calculate percentage change
  const percentChange =
    previousValue && value
      ? Math.round(((value - previousValue) / previousValue) * 100)
      : null

  const isPositive = percentChange !== null ? percentChange > 0 : true
  const isNeutral = percentChange !== null ? percentChange === 0 : false

  return (
    <Card className='overflow-hidden border transition-all duration-200 hover:shadow-md'>
      <div className={`h-1.5 ${color}`} />
      <CardHeader className='flex flex-row items-center justify-between pb-2 pt-4'>
        <div className='flex items-center gap-2'>
          <CardTitle className='text-sm font-medium'>{title}</CardTitle>
          {tooltip && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className='h-3.5 w-3.5 cursor-help text-muted-foreground' />
                </TooltipTrigger>
                <TooltipContent>
                  <p className='max-w-xs text-xs'>{tooltip}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
        <div
          className={`rounded-full p-2 ${color
            .replace("bg-", "bg-")
            .replace("500", "500/15")}`}
        >
          {icon}
        </div>
      </CardHeader>
      <CardContent className='pb-1'>
        {isLoading ? (
          <div className='space-y-2'>
            <Skeleton className='h-10 w-[120px]' />
            <Skeleton className='h-4 w-[80px]' />
          </div>
        ) : (
          <div className='flex flex-col'>
            <div className='flex items-center gap-2'>
              <div className='text-3xl font-bold'>
                {value !== null ? value.toLocaleString() : 0}
              </div>
              {percentChange !== null && (
                <Badge
                  variant='outline'
                  className={cn(
                    "flex items-center gap-1 text-xs font-medium",
                    isPositive
                      ? "border-emerald-200 bg-emerald-50 text-emerald-700"
                      : isNeutral
                        ? "border-blue-200 bg-blue-50 text-blue-700"
                        : "border-rose-200 bg-rose-50 text-rose-700",
                  )}
                >
                  {isPositive ? (
                    <TrendingUp className='h-3 w-3' />
                  ) : isNeutral ? (
                    <ArrowRight className='h-3 w-3' />
                  ) : (
                    <TrendingDown className='h-3 w-3' />
                  )}
                  {isPositive ? "+" : ""}
                  {percentChange}%
                </Badge>
              )}
            </div>
            <div className='mt-1 flex items-center gap-1'>
              <Badge
                variant='secondary'
                className='bg-muted/50 text-xs font-normal'
              >
                <Calendar className='mr-1 h-3 w-3' />
                {timeframe}
              </Badge>
              {previousValue !== null && (
                <span className='text-xs text-muted-foreground'>
                  vs {previousValue?.toLocaleString()} previous
                </span>
              )}
            </div>
          </div>
        )}
      </CardContent>
      {footerText && (
        <CardFooter className='pb-3 pt-0'>
          <div className='flex items-center gap-1.5 text-xs text-muted-foreground'>
            <Clock className='h-3 w-3' />
            {footerText}
          </div>
        </CardFooter>
      )}
    </Card>
  )
}

export default function VisitorsStats({
  isLoadingWebsite,
  websiteAnalytics,
  pageViews,
  sessions,
}: VisitorsStatsProps) {
  const formatLastViewTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)

    if (diffMins < 1) return "Just now"
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffMins < 24 * 60) return `${Math.floor(diffMins / 60)}h ago`

    return new Intl.DateTimeFormat("en-US", {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    }).format(date)
  }

  // Calculate previous period values (mocked for demonstration)
  const previousUniqueVisitors = websiteAnalytics?.uniqueVisitors
    ? Math.round(websiteAnalytics.uniqueVisitors * 0.85)
    : null

  const previousPageViews = websiteAnalytics?.totalPageViews
    ? Math.round(websiteAnalytics.totalPageViews * 0.9)
    : null

  const previousSessions = websiteAnalytics?.totalSessions
    ? Math.round(websiteAnalytics.totalSessions * 0.8)
    : null

  const previousEvents = websiteAnalytics?.totalEvents
    ? Math.round(websiteAnalytics.totalEvents * 0.95)
    : null

  return (
    <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
      <StatCard
        title='Unique Visitors'
        icon={<Users className='h-5 w-5 text-blue-500' />}
        value={websiteAnalytics?.uniqueVisitors || 0}
        previousValue={previousUniqueVisitors}
        isLoading={isLoadingWebsite}
        footerText='Based on unique browser fingerprints'
        color='bg-blue-500'
        tooltip='The number of distinct users who have visited your site, based on browser fingerprinting and cookies.'
        timeframe='Last 30 days'
      />
      <StatCard
        title='Page Views'
        icon={<Eye className='h-5 w-5 text-purple-500' />}
        value={websiteAnalytics?.totalPageViews || 0}
        previousValue={previousPageViews}
        isLoading={isLoadingWebsite}
        footerText={
          pageViews && pageViews.length > 0
            ? `Last view: ${formatLastViewTime(
                pageViews[pageViews.length - 1].timestamp,
              )}`
            : "No recent page views"
        }
        color='bg-purple-500'
        tooltip='The total number of pages viewed. Repeated views of a single page are counted.'
        timeframe='Last 30 days'
      />
      <StatCard
        title='Sessions'
        icon={<Activity className='h-5 w-5 text-emerald-500' />}
        value={websiteAnalytics?.totalSessions || 0}
        previousValue={previousSessions}
        isLoading={isLoadingWebsite}
        footerText={
          sessions && sessions.length > 0
            ? `Avg. duration: ${
                Math.floor(Math.random() * 5) + 2
              }m ${Math.floor(Math.random() * 60)}s`
            : "No session data available"
        }
        color='bg-emerald-500'
        tooltip='A session is a group of user interactions with your website that take place within a given time frame.'
        timeframe='Last 30 days'
      />
      <StatCard
        title='Events'
        icon={<BarChart3 className='h-5 w-5 text-amber-500' />}
        value={websiteAnalytics?.totalEvents || 0}
        previousValue={previousEvents}
        isLoading={isLoadingWebsite}
        footerText={`${Math.floor(Math.random() * 20) + 10} events today`}
        color='bg-amber-500'
        tooltip='User interactions with content that can be measured independently from a page load, such as clicks, form submissions, and video plays.'
        timeframe='Last 30 days'
      />
    </div>
  )
}

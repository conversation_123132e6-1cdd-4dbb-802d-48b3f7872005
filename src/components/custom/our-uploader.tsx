// import { OurFileRouter } from "@/api/uploadthing/core";

import { useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"
import { ClientUploadedFileData } from "uploadthing/types"

import { uploadUserMedia } from "@/actions/users"
import { compressMultipleImages } from "@/utils/image-compression"
import { UploadDropzone } from "@/utils/uploadthing"

type ImageUploadDropZoneProps = {
  websiteId: string
}

export const ImageUploadDropZone = ({
  websiteId,
}: ImageUploadDropZoneProps) => {
  const queryClient = useQueryClient()

  const { mutate } = useMutation({
    mutationFn: async (
      files: ClientUploadedFileData<{ uploadedBy: string }>[],
    ) => await uploadUserMedia(files, websiteId),
    onSuccess: (data) => {
      if (data.uploaded) {
        // queryClient.invalidateQueries(["about", website]);
        queryClient.invalidateQueries({
          // queryKey: ["images", websiteId, 1, "", ""],
          queryKey: ["images", websiteId, 1, ""],
        })

        toast.success("Images Uploaded Successfully !", {
          description: "Your images have save uploaded successfully.",
        })
      }
    },
    onError: (error) => {
      console.error(error)
      toast.error("Oops! Something went wrong.", {
        description: "Failed to save your images. Please try again.",
      })
    },
  })

  return (
    <UploadDropzone
      // multiple
      endpoint='imageUploader'
      onClientUploadComplete={async (res) => {
        // Do something with the response
        // console.log("Files: ", res);
        mutate(res)
        // alert("Upload Completed");
        // const data = await uploadUserMedia(res, websiteId);
        // if (data.uploaded) {
        //   toast.success("Upload Completed");
        // }
      }}
      onUploadError={(error: Error) => {
        // alert(`ERROR! ${error.message}`);
        toast.error(`ERROR! ${error.message}`)
      }}
      onUploadBegin={(name) => {
        // Do something once upload begins
        toast.info("Uploading... ")
        console.log("Uploading: ", name)
      }}
      onBeforeUploadBegin={async (files) => {
        // Do something before upload begins
        // console.log("Before Uploading: ", files);
        // Compress the files
        // console.log("Received files for compression:", files);

        const compressionOptions = {
          quality: 0.8, // Adjust compression quality as needed
          maxSizeMB: 1, // Maximum file size in MB
        }

        const compressedFiles = await compressMultipleImages(
          files,
          compressionOptions,
        )

        const filesToUpload = compressedFiles.map(
          (blob, index) =>
            new File([blob], files[index].name, {
              type: blob.type,
            }),
        )

        return filesToUpload
      }}
      onChange={async (acceptedFiles) => {
        // Do something with the accepted files
        console.log("Accepted files: ", acceptedFiles)
      }}
    />
  )
}

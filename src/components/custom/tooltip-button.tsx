"use client"

import * as React from "react"

import { Button, ButtonProps } from "@/components/ui/button"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface TooltipButtonProps extends ButtonProps {
  tooltipContent: React.ReactNode
  children: React.ReactNode
}

export function TooltipButton({
  tooltipContent,
  children,
  ...buttonProps
}: TooltipButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button {...buttonProps}>{children}</Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltipContent}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

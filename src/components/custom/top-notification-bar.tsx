"use client"

import { AnimatePresence, motion } from "framer-motion"
import { InfoIcon, X } from "lucide-react"
import { useEffect, useState } from "react"

export function TopBanner() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Check localStorage on component mount
    const bannerClosed = localStorage.getItem("betaBannerClosed")
    if (!bannerClosed) {
      setIsVisible(true)
    }
  }, [])

  const closeBanner = () => {
    setIsVisible(false)
    // Set a flag in localStorage when the banner is closed
    localStorage.setItem("betaBannerClosed", "true")
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.5 }}
          className='absolute left-0 right-0 top-0 z-[999]'
        >
          <div className='relative z-[999] bg-gradient-to-r from-purple-500 to-primary/85 py-1.5 text-white shadow-lg'>
            <div className='container mx-auto flex items-center justify-between px-4'>
              {/* Message */}
              <div className='flex flex-wrap items-center justify-start text-xs font-medium md:gap-2 md:text-sm'>
                <InfoIcon className='mr-2 h-4 w-4' />
                <p>
                  This is currently in <span className='font-bold'>beta</span>.
                </p>
                Stay tuned for updates and improvements!
              </div>
              {/* Close Button */}
              <button
                onClick={closeBanner}
                className='rounded-full p-2 text-white hover:bg-white/10'
                aria-label='Close beta banner'
              >
                <X className='h-5 w-5' />
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

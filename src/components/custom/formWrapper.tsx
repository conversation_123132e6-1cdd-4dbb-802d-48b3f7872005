import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet"
import { cn } from "@/lib/utils"

interface FormWrapperProps {
  title: string
  description?: string
  open: boolean
  className?: string
  onOpenChange: (open: boolean) => void
  children: React.ReactNode
}

export function FormWrapper({
  title,
  description,
  open,
  onOpenChange,
  className,
  children,
}: FormWrapperProps) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        className={cn(
          "max-h-screen max-w-full pb-0 md:max-w-lg md:overflow-auto xl:max-w-2xl",
          className,
        )}
      >
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          <SheetDescription>{description}</SheetDescription>
        </SheetHeader>
        {children}
      </SheetContent>
    </Sheet>
  )
}

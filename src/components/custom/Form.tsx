"use client"

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod"
import React, { useCallback } from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"

import { FormProps } from "@/types/form"

import { Button } from "../ui/button"
import { Form as FormProvider } from "../ui/form"
import { FormFieldComponent } from "./FormField"

export function Form<T extends z.ZodType>({
  fields,
  schema,
  onSubmit,
  defaultValues,
}: FormProps<T>) {
  const form = useForm<z.infer<T>>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as z.infer<T>,
  })

  const handleSubmit = useCallback(
    (values: z.infer<T>) => {
      console.log("Form values:", values)
      onSubmit(values)
    },
    [onSubmit],
  )

  return (
    <FormProvider {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className='grid grid-flow-row-dense gap-3 md:grid-cols-2 md:gap-6 xl:grid-cols-3'
      >
        {fields.map((field) => (
          <FormFieldComponent key={field.name} field={field} form={form} />
        ))}
        <Button className='col-span-full' type='submit'>
          {form.formState.isSubmitting ? "Submitting..." : "Submit"}
        </Button>
      </form>
    </FormProvider>
  )
}

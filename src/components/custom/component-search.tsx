"use client"
import { useRouter } from "next/navigation"
import React, { useState } from "react"

import { PlaceholdersAndVanishInput } from "../ui/placeholders-and-vanish-input"

const ComponentSearch = () => {
  const [query, setQuery] = useState("")

  const router = useRouter()
  const placeholders = [
    "Search your component here",
    "Who is <PERSON>?",
    "Where is <PERSON>?",
    "Write a Javascript method to reverse a string",
    "How to assemble your own PC?",
  ]

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchQuery = e.target.value
    setQuery(searchQuery)
  }
  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    console.log("submitted")
    router.push(`/components/search?q=${encodeURIComponent(query)}`)
  }
  return (
    <>
      <PlaceholdersAndVanishInput
        placeholders={placeholders}
        onChange={handleChange}
        onSubmit={onSubmit}
      />
    </>
  )
}

export default ComponentSearch

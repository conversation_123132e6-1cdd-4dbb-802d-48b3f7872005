"use client"

import { useMutation, useQueryClient } from "@tanstack/react-query"
import { Loader2, UploadCloud, X } from "lucide-react"
import { useCallback, useState } from "react"
import { useDropzone } from "react-dropzone"
import { toast } from "sonner"

import { uploadUserMedia } from "@/actions/users"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useUserStore } from "@/store/use-user-store"
import {
  ACCEPTED_IMAGE_TYPES,
  FileResponse,
  MAX_FILE_SIZE,
  UploadResult,
} from "@/types/custom-upload"
import { processFiles, uploadFilesToS3 } from "@/utils/custom-upload"

interface CustomImageUploaderProps {
  websiteId: string
  // username: string;
}

export default function CustomImageUploader({
  websiteId,
}: CustomImageUploaderProps) {
  const [files, setFiles] = useState<File[]>([])
  const [uploadResults, setUploadResults] = useState<UploadResult>({
    files: [],
    message: "",
  })
  const [uploadProgress, setUploadProgress] = useState(0) // Separate state for progress
  const { currentWebsite } = useUserStore()
  const queryClient = useQueryClient()

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    try {
      const processedFiles = await processFiles(acceptedFiles)
      setFiles((prev) => [...prev, ...processedFiles])
    } catch (error) {
      // Error handling is done in processFiles
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive, isDragReject } =
    useDropzone({
      onDrop,
      accept: ACCEPTED_IMAGE_TYPES.reduce(
        (acc, type) => ({
          ...acc,
          [type]: [],
        }),
        {} as Record<string, string[]>,
      ),
      multiple: true,
      maxSize: MAX_FILE_SIZE,
    })

  const removeFile = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index))
  }

  const uploadMutation = useMutation({
    mutationFn: async (files: File[]) => {
      return uploadFilesToS3(files, currentWebsite?.name, (progress) => {
        if (uploadMutation.isPending) {
          console.log("progress", progress)
          console.log(uploadResults)
          setUploadProgress(progress)
        }
      })
    },
    onSuccess: (results) => {
      setUploadResults(results)
      console.log(results)
      setFiles([])
      setUploadProgress(100)
      toast.success("Files uploaded successfully")
      serverMutation.mutate(results.files)
    },
    onError: (error) => {
      toast.error(
        error instanceof Error ? error.message : "Failed to upload images",
      )
    },
    onSettled: () => {
      setUploadProgress(0) // Reset progress when done
    },
  })

  const serverMutation = useMutation({
    mutationFn: (urls: FileResponse[]) => uploadUserMedia(urls, websiteId),
    onSuccess: (data) => {
      if (data.uploaded) {
        queryClient.invalidateQueries({
          queryKey: ["images", websiteId, 1, ""],
        })
        toast.success("Images Uploaded Successfully!", {
          description: "Your images have been uploaded successfully.",
        })
      }
    },
    onError: (error: Error) => {
      console.error(error)
      toast.error("Failed to save images on server")
    },
  })

  return (
    <Card className='w-full max-w-2xl rounded-lg border-none shadow-sm'>
      <CardHeader className='text-center'>
        <CardTitle className='text-2xl font-bold text-foreground'>
          Upload Images
        </CardTitle>
        <CardDescription className='text-gray-400'>
          Upload up to 10 images, {MAX_FILE_SIZE / (1024 * 1024)}MB each
        </CardDescription>
      </CardHeader>
      <CardContent className='p-6'>
        <div
          {...getRootProps()}
          className={`flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed p-10 text-center transition-colors ${isDragActive ? "border-blue-500 bg-blue-500/20" : ""} ${isDragReject ? "border-red-500 bg-red-500/20" : ""} ${
            !isDragActive && !isDragReject
              ? "border-primary/50 hover:border-primary/70"
              : ""
          }`}
        >
          <input {...getInputProps()} />
          <UploadCloud className='mb-4 h-16 w-16 text-gray-400' />
          <p className='text-lg text-gray-400'>
            Drag & drop files here or click to select
          </p>
          <p className='mt-2 text-sm text-gray-500'>
            Images up to {MAX_FILE_SIZE / (1024 * 1024)}MB, max 10
          </p>
          <Button variant='secondary' className='mt-6'>
            Choose File(s)
          </Button>
        </div>

        {files.length > 0 && (
          <div className='mt-8 space-y-4'>
            <div className='flex items-center justify-between'>
              <h3 className='text-lg text-gray-300'>
                Selected Files ({files.length}/10)
              </h3>
              <Button
                onClick={() => uploadMutation.mutate(files)}
                disabled={uploadMutation.isPending || files.length === 0}
                className='rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700'
              >
                {uploadMutation.isPending ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Uploading...
                  </>
                ) : (
                  "Upload All"
                )}
              </Button>
            </div>

            {uploadMutation.isPending && (
              <Progress value={uploadProgress} className='h-2' />
            )}

            <div className='space-y-2'>
              {files.map((file, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between rounded-lg bg-gray-800/50 p-4 shadow'
                >
                  <div className='flex items-center space-x-4'>
                    <div className='min-w-0 flex-1'>
                      <p className='line-clamp-1 max-w-[500px] truncate text-sm text-gray-300'>
                        {file.name}
                      </p>
                      <p className='text-xs text-gray-500'>
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant='ghost'
                          size='icon'
                          className='h-8 w-8 text-gray-400 hover:text-gray-300'
                          onClick={() => removeFile(index)}
                        >
                          <X className='h-4 w-4' />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Remove file</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* {uploadResults.length > 0 && (
          <div className="mt-8 space-y-2">
            <h3 className="text-lg text-gray-300">Upload Results</h3>
            {uploadResults.map((result, index) => (
              <Card
                key={index}
                className="bg-gray-800/50 border-gray-700 rounded-lg shadow"
              >
                <CardContent className="p-4">
                  <a
                    href={result.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300 text-sm break-all"
                  >
                    {result.url}
                  </a>
                </CardContent>
              </Card>
            ))}
          </div>
        )} */}
      </CardContent>
    </Card>
  )
}

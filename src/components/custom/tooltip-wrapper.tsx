// components/ui/Tooltip.tsx
import { ReactNode } from "react"

import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface TooltipProps {
  content: ReactNode
  children: ReactNode
}

const CustomToolTip = ({ content, children }: TooltipProps) => {
  return (
    <Tooltip>
      <TooltipTrigger>{children}</TooltipTrigger>
      <TooltipContent>{content}</TooltipContent>
    </Tooltip>
  )
}

export default CustomToolTip

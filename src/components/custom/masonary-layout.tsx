"use client"

import React from "react"

// import Masonry from "react-layout-masonry";
import { MainCard } from "../main/main-card"

type Props = {
  data: any
}

const MasonaryLayout = ({ data }: Props) => {
  return (
    // <Masonry
    //   // columns={3}
    //   columns={{ 640: 1, 768: 2, 1024: 2, 1480: 3, 1960: 4 }}
    //   gap={16}
    // >
    <div className='grid w-full gap-4 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4'>
      {data?.map((item: any) => {
        return <MainCard key={item.id} data={item} />
      })}
    </div>

    // </Masonry>
  )
}

export default MasonaryLayout

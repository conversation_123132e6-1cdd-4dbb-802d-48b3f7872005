interface EmptyStateProps {
  title: string
  description: string
}

export function EmptyState({ title, description }: EmptyStateProps) {
  return (
    <div className='flex h-[400px] flex-col items-center justify-center text-center'>
      {/* <FolderOpen className='mb-4 h-16 w-16 text-muted-foreground' /> */}
      <FolderIcon />
      <h3 className='text-lg font-semibold'>{title}</h3>
      <p className='mt-2 max-w-xs text-pretty text-center text-muted-foreground'>
        {description}
      </p>
    </div>
  )
}

export const FolderIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='600'
    height='548'
    fill='none'
    className='h-40 w-auto max-w-[16rem] text-gray-800 dark:text-white'
    viewBox='0 0 600 548'
    {...props}
  >
    <path
      fill='url(#paint0_linear_411_1557)'
      d='M572 274c0 151.326-122.674 274-274 274S24 425.326 24 274 146.674 0 298 0s274 122.674 274 274'
    ></path>
    <path
      fill='#374151'
      d='M502 390h-66.5C468.7 326.8 559 273.333 600 254.5 574.4 317.3 524 371 502 390'
    ></path>
    <path
      fill='url(#paint1_linear_411_1557)'
      fillOpacity='0.7'
      d='M502 390h-66.5C468.7 326.8 559 273.333 600 254.5 574.4 317.3 524 371 502 390'
    ></path>
    <path
      fill='#374151'
      d='M445.5 390H379c33.2-63.2 130-199.5 183-242.5C544.5 231 467.5 371 445.5 390'
    ></path>
    <path
      fill='url(#paint2_linear_411_1557)'
      fillOpacity='0.7'
      d='M445.5 390H379c33.2-63.2 130-199.5 183-242.5C544.5 231 467.5 371 445.5 390'
    ></path>
    <path
      fill='#374151'
      d='M98 390h66.5C131.3 326.8 41 273.333 0 254.5 25.6 317.3 76 371 98 390'
    ></path>
    <path
      fill='url(#paint3_linear_411_1557)'
      fillOpacity='0.7'
      d='M98 390h66.5C131.3 326.8 41 273.333 0 254.5 25.6 317.3 76 371 98 390'
    ></path>
    <path
      fill='#374151'
      d='M154.5 390H221C187.8 326.8 91 190.5 38 147.5 55.5 231 132.5 371 154.5 390'
    ></path>
    <path
      fill='url(#paint4_linear_411_1557)'
      fillOpacity='0.7'
      d='M154.5 390H221C187.8 326.8 91 190.5 38 147.5 55.5 231 132.5 371 154.5 390'
    ></path>
    <path
      fill='url(#paint5_linear_411_1557)'
      fillRule='evenodd'
      d='M546.773 389c-43.47 93.878-138.516 159-248.771 159-110.256 0-205.301-65.122-248.771-159z'
      clipRule='evenodd'
    ></path>
    <path fill='#1F2A37' d='M219 253h270l-50 186H169z'></path>
    <path
      fill='url(#paint6_linear_411_1557)'
      fillOpacity='0.7'
      d='M219 253h270l-50 186H169z'
    ></path>
    <path
      fill='#d6e2fb'
      d='m205.672 139.85 64.183 14.54 5.446 22.766 186.132 42.168-35.573 157.021-260.401-58.993z'
    ></path>
    <path
      fill='url(#paint7_linear_411_1557)'
      d='m205.672 139.85 64.183 14.54 5.446 22.766 186.132 42.168-35.573 157.021-260.401-58.993z'
    ></path>
    <path
      fill='#2563eb'
      fillRule='evenodd'
      d='m247.819 164.266-34.622-7.844.442-1.951 34.622 7.844z'
      clipRule='evenodd'
    ></path>
    <path
      fill='#d6e2fb'
      d='m182.973 172.88 65.27 8.407 7.575 22.149 189.285 24.379-20.567 159.681-264.812-34.107z'
    ></path>
    <path
      fill='url(#paint8_linear_411_1557)'
      d='m182.973 172.88 65.27 8.407 7.575 22.149 189.285 24.379-20.567 159.681-264.812-34.107z'
    ></path>
    <path
      fill='#2563eb'
      fillRule='evenodd'
      d='m227.239 193.201-35.209-4.535.255-1.983 35.209 4.535z'
      clipRule='evenodd'
    ></path>
    <path fill='#d6e2fb' d='M158 213h65.81l10.341 21H425v161H158z'></path>
    <path
      fill='url(#paint9_linear_411_1557)'
      d='M158 213h65.81l10.341 21H425v161H158z'
    ></path>
    <path
      fill='#2563eb'
      fillRule='evenodd'
      d='M204.5 227.5H169v-2h35.5z'
      clipRule='evenodd'
    ></path>
    <path fill='#d6e2fb' d='M119 253h270l50 186H169z'></path>
    <path
      fill='url(#paint10_linear_411_1557)'
      d='M119 253h270l50 186H169z'
    ></path>
    <path
      fill='url(#paint11_linear_411_1557)'
      d='M119 253h270l50 186H169z'
    ></path>
    <path
      fill='url(#paint12_linear_411_1557)'
      d='M119 253h270l50 186H169z'
    ></path>
    <defs>
      <linearGradient
        id='paint0_linear_411_1557'
        x1='298'
        x2='298'
        y1='0'
        y2='548'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#1F2A37'></stop>
        <stop offset='1' stopColor='#1F2A37' stopOpacity='0'></stop>
      </linearGradient>
      <linearGradient
        id='paint1_linear_411_1557'
        x1='551.959'
        x2='489.878'
        y1='462.105'
        y2='307.716'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#111928'></stop>
        <stop offset='1' stopColor='#111928' stopOpacity='0'></stop>
      </linearGradient>
      <linearGradient
        id='paint2_linear_411_1557'
        x1='508.556'
        x2='362.173'
        y1='519.045'
        y2='292.756'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#111928'></stop>
        <stop offset='1' stopColor='#111928' stopOpacity='0'></stop>
      </linearGradient>
      <linearGradient
        id='paint3_linear_411_1557'
        x1='48.041'
        x2='110.122'
        y1='462.105'
        y2='307.716'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#111928'></stop>
        <stop offset='1' stopColor='#111928' stopOpacity='0'></stop>
      </linearGradient>
      <linearGradient
        id='paint4_linear_411_1557'
        x1='91.444'
        x2='237.827'
        y1='519.045'
        y2='292.756'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#111928'></stop>
        <stop offset='1' stopColor='#111928' stopOpacity='0'></stop>
      </linearGradient>
      <linearGradient
        id='paint5_linear_411_1557'
        x1='298.002'
        x2='298.002'
        y1='389'
        y2='518.085'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#2F3948'></stop>
        <stop offset='1' stopColor='#2F3948' stopOpacity='0'></stop>
      </linearGradient>
      <linearGradient
        id='paint6_linear_411_1557'
        x1='377.5'
        x2='421.605'
        y1='450'
        y2='311.767'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#111928'></stop>
        <stop offset='1' stopColor='#111928' stopOpacity='0'></stop>
      </linearGradient>
      <linearGradient
        id='paint7_linear_411_1557'
        x1='372.5'
        x2='311.238'
        y1='39'
        y2='278.083'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#9ab7f6' stopOpacity='0'></stop>
        <stop offset='1' stopColor='#9ab7f6'></stop>
      </linearGradient>
      <linearGradient
        id='paint8_linear_411_1557'
        x1='341.5'
        x2='244'
        y1='97.5'
        y2='371.5'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#9ab7f6' stopOpacity='0'></stop>
        <stop offset='1' stopColor='#9ab7f6'></stop>
      </linearGradient>
      <linearGradient
        id='paint9_linear_411_1557'
        x1='326.5'
        x2='273'
        y1='201'
        y2='379.5'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#9ab7f6' stopOpacity='0'></stop>
        <stop offset='1' stopColor='#9ab7f6'></stop>
      </linearGradient>
      <linearGradient
        id='paint10_linear_411_1557'
        x1='279'
        x2='279'
        y1='414.904'
        y2='239.07'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#fff' stopOpacity='0'></stop>
        <stop offset='1' stopColor='#fff'></stop>
      </linearGradient>
      <linearGradient
        id='paint11_linear_411_1557'
        x1='279'
        x2='279'
        y1='255.914'
        y2='482.465'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#c8d8fa' stopOpacity='0'></stop>
        <stop offset='1' stopColor='#c8d8fa'></stop>
      </linearGradient>
      <linearGradient
        id='paint12_linear_411_1557'
        x1='279'
        x2='279'
        y1='359.105'
        y2='458.445'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#fff' stopOpacity='0'></stop>
        <stop offset='1' stopColor='#fff'></stop>
      </linearGradient>
    </defs>
  </svg>
)

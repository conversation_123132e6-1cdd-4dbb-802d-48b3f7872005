import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import React from "react"
import { use<PERSON><PERSON>roller } from "react-hook-form"
import { toast } from "sonner"

import { cn } from "@/functions"
import { FormFieldProps } from "@/types/form"
import { UploadButton } from "@/utils/uploadthing"

import ImageUploader from "../gallery/image-uploader"
import { Button } from "../ui/button"
import { Calendar } from "../ui/calendar"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form"
import { Input } from "../ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover"
import { RadioGroup, RadioGroupItem } from "../ui/radio-group"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import { Textarea } from "../ui/textarea"
import ArrayInput from "./array-input"
import CustomCheckbox from "./checkbox"
import Selector from "./multiple-select"

export const FormFieldComponent: React.FC<FormFieldProps> = ({
  field,
  form,
}) => {
  const { name, label, type, placeholder, options, isRequired } = field
  const { field: controllerField, fieldState } = useController({
    name,
    control: form.control,
  })

  // console.log("options: ", options);
  const renderFieldComponent = () => {
    switch (type) {
      case "select":
        return (
          <Select
            value={controllerField.value || ""}
            onValueChange={(value) =>
              controllerField.onChange(value === "default" ? null : value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      case "textarea":
        return (
          <Textarea
            className='w-full'
            placeholder={placeholder}
            {...controllerField}
          />
        )
      case "gallery":
        return (
          <div key={name}>
            <ImageUploader
              uploadedImage={controllerField.value}
              setUploadedImage={(selectedImages) => {
                console.log("Selected images:", selectedImages)
                // controllerField.onChange(selectedImages);
                const prevImages = form.getValues(name)
                // my value should be strig not array as my selectedImages is string seprated by comma
                if (selectedImages) {
                  // form.setValue(name, selectedImages)
                  form.setValue(
                    name,
                    prevImages
                      ? // should be string not array
                        prevImages + "," + selectedImages
                      : selectedImages,
                  )
                }
              }}
              allowMultiple={true}
            />
          </div>
        )
      case "image":
        return (
          <UploadButton
            key={name}
            endpoint='imageUploader'
            onClientUploadComplete={(res) => {
              // Do something with the response
              // Do something with the response
              console.log("Files: ", res)
              // alert('Upload Completed')
              toast.success("Image uploaded successfully")
              const data: any = res
              console.log(data)
              if (data) {
                console.log("Data: ", data)
                // const prevImages = form.getValues(name);

                form.setValue(name, [data?.[0]?.url])
                form.setValue("imageKey", data?.[0]?.key)
                form.setValue("fileName", data?.[0]?.name)
                form.setValue("fileSize", data?.[0]?.size)
                form.setValue("fileType", data?.[0]?.type)

                // form.setValue(name, data?.[0]?.url);
              }
            }}
            onUploadError={(error: Error) => {
              // Do something with the error.
              alert(`ERROR! ${error.message}`)
            }}
          />
        )
      case "array":
        return (
          <ArrayInput
            value={controllerField.value || []}
            onChange={(value) => {
              controllerField.onChange(value)
            }}
            placeholder={placeholder}
          />
        )

      case "select-multiple":
        return (
          <Selector
            options={options || []}
            selected={controllerField.value || []} // Default to empty array
            onChange={(value) => {
              form.setValue(name, value)
            }}
            placeholder={placeholder || "Select items..."}
          />
        )
      case "select-single":
        return (
          <Selector
            options={options || []}
            selected={controllerField.value ? [controllerField.value] : []} // Single value as an array
            onChange={(value) => {
              if (Array.isArray(value)) {
                form.setValue(name, value[0] || null) // Use the first selected value
              }
            }}
            placeholder={placeholder || "Select an item..."}
          />
        )
      case "checkbox":
        return (
          <CustomCheckbox
            label={name}
            description={placeholder}
            checked={Boolean(controllerField.value)}
            onCheckedChange={(checked) => controllerField.onChange(checked)}
          />
          // <Checkbox
          //   checked={Boolean(controllerField.value)}
          //   onCheckedChange={(checked) => controllerField.onChange(checked)}
          // />
        )
      case "radio":
        return (
          <RadioGroup
            value={controllerField.value || ""}
            onValueChange={(value) => controllerField.onChange(value)}
          >
            {options?.map((option) => (
              <FormItem
                key={option.value}
                className='flex items-center space-x-3 space-y-0'
              >
                <FormControl>
                  <RadioGroupItem value={option.value} />
                </FormControl>
                <FormLabel className='font-normal'>{option.label}</FormLabel>
              </FormItem>
            ))}
          </RadioGroup>
        )
      case "date":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={`w-full justify-start text-left font-normal ${
                  !controllerField.value && "text-muted-foreground"
                }`}
              >
                <CalendarIcon className='mr-2 h-4 w-4' />
                {controllerField.value ? (
                  format(new Date(controllerField.value), "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className='w-auto p-0' align='start'>
              <Calendar
                mode='single'
                selected={
                  controllerField.value
                    ? new Date(controllerField.value)
                    : undefined
                }
                onSelect={(date) =>
                  // controllerField.onChange(date?.toISOString() || null)
                  controllerField.onChange(date)
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
        )
      default:
        return (
          <Input
            type={type}
            placeholder={placeholder}
            value={controllerField.value || ""}
            onChange={(e) => {
              const { value } = e.target
              if (type === "number") {
                controllerField.onChange(value ? parseFloat(value) : null)
              } else {
                controllerField.onChange(value)
              }
            }}
          />
        )
    }
  }

  return (
    <FormField
      control={form.control}
      name={name}
      render={() => (
        <FormItem
          className={cn(
            type === "textarea" || type === "image" || type === "array"
              ? "col-span-full"
              : "",
          )}
        >
          <FormLabel>
            {label}
            {isRequired && <span className='text-red-500'>*</span>}
          </FormLabel>
          <FormControl>{renderFieldComponent()}</FormControl>
          <FormMessage>{fieldState.error?.message}</FormMessage>
        </FormItem>
      )}
    />
  )
}

FormFieldComponent.displayName = "FormFieldComponent"

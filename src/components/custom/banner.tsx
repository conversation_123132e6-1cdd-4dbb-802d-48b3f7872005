/* eslint-disable @next/next/no-img-element */

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { About } from "@/types/about"

import { Button } from "../ui/button"
// import {
//   IconBrandFacebook,
//   IconBrandTwitter,
//   IconBrandLinkedin,
// } from "@tabler/icons-react";

interface BannerProps {
  about: About
}

// const SocialIcon = ({ platform }: { platform: string }) => {
//   switch (platform.toLowerCase()) {
//     case "facebook":
//       return <FacebookIcon className='h-5 w-5' />
//     case "twitter":
//       return <TwitterIcon className='h-5 w-5' />
//     case "linkedin":
//       return <LinkedinIcon className='h-5 w-5' />
//     default:
//       return null
//   }
// }

const Banner = ({ about }: BannerProps) => {
  return (
    <div className='-mt-10 px-5 pb-36'>
      <div className='flex w-full flex-wrap items-center justify-between gap-5 pt-5 md:gap-10'>
        <div className='relative flex flex-[1] -translate-y-6 items-center gap-5'>
          <Avatar className='size-24 border-2 border-white shadow-lg sm:size-44 sm:border-4'>
            <AvatarImage
              src={about.avatar}
              alt={`${about.first_name} ${about.last_name}`}
              className='object-cover'
            />
            <AvatarFallback>
              {about.first_name[0]}
              {about.last_name[0]}
            </AvatarFallback>
          </Avatar>
          <div className='mt-10 flex flex-col'>
            <h3 className='text-3xl font-medium'>{`${about.first_name} ${about.middle_name} ${about.last_name}`}</h3>
            <span className='text-muted-foreground'>{about.title}</span>
          </div>
        </div>

        {/* <Separator orientation="vertical" className="h-20" />

        <div className="w-full flex-[1.5] ">
          <div className="w-full flex ">
            <Button variant={"white"} className="block mb-3">
              Alternative images
            </Button>
          </div>

          <div className="flex gap-4 items-center overflow-auto w-full">
            {about.alternate_avatars.map((avatar, index) => (
              <Avatar
                key={index}
                className="size-20 border-2 border-white shadow-lg sm:size-28 sm:border-4"
              >
                <AvatarImage
                  src={avatar}
                  alt={`Alternative ${index + 1}`}
                  className="object-cover"
                />
                <AvatarFallback>
                  {about.first_name[0]}
                  {about.last_name[0]}
                </AvatarFallback>
              </Avatar>
            ))}
          </div>
        </div> */}
      </div>
      <Separator />
      <div className='mt-8 grid grid-cols-1 gap-10 md:grid-cols-2'>
        <div>
          {/* <h2 className="mb-3 font-medium">About Me</h2> */}
          <Button variant={"white"} className='mb-3 block'>
            About Me
          </Button>
          <span className='mb-10 text-sm text-foreground/80'>
            {about.sub_title}
          </span>
          <br />
          <br />
          <span className='text-sm text-muted-foreground'>{about.bio}</span>
          <br />
          <br />
          <blockquote className='text-sm italic text-muted-foreground'>
            &quot;{about.quote}&quot;
          </blockquote>
        </div>
        <div className='flex flex-col gap-5'>
          <div className='flex flex-wrap justify-between'>
            <div>
              <h2 className='mb-3 font-medium'>Location</h2>
              <span className='text-sm text-muted-foreground'>
                {`${about.address.street}, ${about.address.city}, ${about.address.state}, ${about.address.country} ${about.address.zip_code}`}
              </span>
            </div>
            <div>
              <h2 className='mb-3 font-medium'>Email</h2>
              <span className='text-sm text-muted-foreground'>
                {about.contact_email}
              </span>
            </div>
          </div>
          <Separator />
          <div className='flex flex-wrap items-start justify-between'>
            <div>
              <h2 className='mb-3 font-medium'>Phone</h2>
              <span className='text-sm text-muted-foreground'>
                {about.phone_number}
              </span>
            </div>
            <div>
              <h2 className='mb-3 font-medium'>Experience</h2>
              <span className='text-sm text-muted-foreground'>
                {about.years_of_experience} years
              </span>
            </div>
            <div>
              <h2 className='mb-3 font-medium'>Projects Done</h2>
              <span className='text-sm text-muted-foreground'>
                {about.total_projects}
              </span>
            </div>
            <div>
              <h2 className='mb-3 font-medium'>Clients</h2>
              <span className='text-sm text-muted-foreground'>
                {about.total_clients}
              </span>
            </div>
          </div>
          <div className='w-full'>
            <span className='font-semibold'>Social Links</span>
            <div className='mt-2 flex gap-4'>
              {about.social_links.map((link, index) => (
                <a
                  key={index}
                  href={link.url}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-muted-foreground hover:text-foreground'
                >
                  {/* <SocialIcon platform={link.platform} /> */}
                  <img
                    src={link?.icon ?? ""}
                    alt={link.platform}
                    className='object-contain md:h-12 md:w-12'
                  />
                </a>
              ))}
            </div>
          </div>
          {/* <div className="w-full">
            <span className="font-semibold">Alternative images</span>
            <div className="flex gap-4 mt-2">
              {about.alternate_avatars.map((avatar, index) => (
                <Avatar
                  key={index}
                  className="size-20 border-2 border-white shadow-lg sm:size-28 sm:border-4"
                >
                  <AvatarImage
                    src={avatar}
                    alt={`Alternative ${index + 1}`}
                    className="object-cover"
                  />
                  <AvatarFallback>
                    {about.first_name[0]}
                    {about.last_name[0]}
                  </AvatarFallback>
                </Avatar>
              ))}
            </div>
          </div> */}
        </div>
      </div>
    </div>
  )
}

export default Banner

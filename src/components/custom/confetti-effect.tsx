"use client"

import { useEffect, useState } from "react"
import Confetti from "react-confetti"

export function ConfettiEffect() {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const { innerWidth: width, innerHeight: height } = window
    setDimensions({ width, height })

    const timer = setTimeout(() => setIsVisible(false), 5000) // Hide confetti after 5 seconds

    const handleResize = () => {
      setDimensions({ width: window.innerWidth, height: window.innerHeight })
    }

    window.addEventListener("resize", handleResize)

    return () => {
      clearTimeout(timer)
      window.removeEventListener("resize", handleResize)
    }
  }, [])

  if (!isVisible) return null

  return (
    <Confetti
      width={dimensions.width}
      height={dimensions.height}
      recycle={false}
    />
  )
}

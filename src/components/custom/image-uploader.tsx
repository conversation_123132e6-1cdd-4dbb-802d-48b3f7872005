"use client"

import { ImagePlusIcon, TrashIcon } from "lucide-react"
import Image from "next/image"
import { useDropzone } from "react-dropzone"

import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface ImageUploaderProps {
  uploadedImage: string | null
  setUploadedImage: React.Dispatch<React.SetStateAction<string | null>>
  className?: string
}

// Regex to check if the URL is valid
const urlRegex = /^https?:\/\/[^\s$.?#].[^\s]*$/i

const ImageUploader = ({
  uploadedImage,
  setUploadedImage,
  className,
}: ImageUploaderProps) => {
  const { getRootProps, getInputProps } = useDropzone({
    accept: { "image/*": [] },
    onDrop: (acceptedFiles) => {
      const file = acceptedFiles[0]
      const reader = new FileReader()

      reader.onload = () => {
        setUploadedImage(reader.result as string)
      }

      reader.readAsDataURL(file)
    },
  })

  return (
    <>
      {uploadedImage && (
        <Button
          variant='destructive'
          size='icon'
          type='button'
          onClick={() => setUploadedImage(null)}
          className='absolute right-2 top-2 z-10 size-6 sm:size-10'
        >
          <TrashIcon size={16} />
        </Button>
      )}
      <div
        className={cn("relative mt-2 aspect-[16/6] overflow-hidden", className)}
      >
        <div
          {...getRootProps()}
          className='grid h-full place-items-center rounded-xl border border-dashed'
        >
          <input {...getInputProps()} />

          {uploadedImage ? (
            <div>
              <div className='absolute left-0 top-0 flex h-full w-full flex-col items-center justify-center rounded-xl bg-black/70'>
                <ImagePlusIcon />
                <span>Change photo</span>
              </div>
              <Image
                src={urlRegex.test(uploadedImage) ? uploadedImage : ""}
                alt=''
                width={300}
                height={150}
                loading='lazy'
                className='h-auto w-auto rounded-xl object-contain object-center'
              />
            </div>
          ) : (
            <div className='flex flex-col items-center justify-center gap-4'>
              <span>
                <ImagePlusIcon />
              </span>
              <p className='text-center text-muted-foreground'>
                <span className='font-bold'>Click to upload</span> <br />
                or drag & drop Image
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default ImageUploader

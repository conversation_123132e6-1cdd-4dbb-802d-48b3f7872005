"use client"

import { motion } from "framer-motion"
import { CreditCard, DollarSign } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

export default function BillingSection() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className='space-y-4'>
        <Card>
          <CardHeader>
            <CardTitle>Current Plan</CardTitle>
            <CardDescription>
              You are currently on the Pro plan.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='flex items-center space-x-2'>
              <DollarSign className='h-4 w-4 text-muted-foreground' />
              <span className='text-2xl font-bold'>$29/month</span>
            </div>
            <p className='mt-2 text-sm text-muted-foreground'>
              Next billing date: July 1, 2023
            </p>
          </CardContent>
          <CardFooter>
            <Button variant='outline'>Change Plan</Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Payment Method</CardTitle>
            <CardDescription>Manage your payment method.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='flex items-center space-x-2'>
              <CreditCard className='h-4 w-4 text-muted-foreground' />
              <span>Visa ending in 1234</span>
            </div>
            <p className='mt-2 text-sm text-muted-foreground'>
              Expires 12/2025
            </p>
          </CardContent>
          <CardFooter>
            <Button variant='outline'>Update Payment Method</Button>
          </CardFooter>
        </Card>
      </div>
    </motion.div>
  )
}

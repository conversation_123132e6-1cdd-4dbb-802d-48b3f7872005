"use client"
import Image, { ImageProps } from "next/image"
import { FC, useState } from "react"

export interface NcImageProps extends Omit<ImageProps, "alt"> {
  containerClassName?: string
  alt?: string
  src: string
  fallbackSrc?: string
}

const TpImage: FC<NcImageProps> = ({
  containerClassName = "",
  alt = "image",
  className = "object-cover w-full h-full",
  fallbackSrc = "",
  src,
  ...args
}) => {
  const [imgSrc, setImgSrc] = useState(src?.trim())

  return (
    <div className={containerClassName}>
      <Image
        className={className}
        src={imgSrc !== "" ? imgSrc : fallbackSrc}
        alt={alt}
        {...args}
        onError={() => setImgSrc(fallbackSrc)}
      />
    </div>
  )
}

export default TpImage

import Image from "next/image"
import Link from "next/link"

import { But<PERSON> } from "@/components/ui/button"

export default function AuthWrapper({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <div className='grid min-h-screen w-full gap-5 p-2 pb-12 md:grid-cols-2 md:gap-10 md:p-4 md:pb-4'>
      <div className='relative hidden overflow-hidden rounded-[20px] bg-[#3B06D2] p-4 md:block md:h-[calc(100vh_-_32px)]'>
        <Button variant={"link"} className='absolute left-4 top-4 z-20' asChild>
          <Link href='/'>
            <Image
              // src="/icons/logo.png"
              src={
                "https://utfs.io/f/qPlpyBmwd8UNH6DswT44WbcxIHPdTa18r3v7qelB5nkUwJSO"
              }
              width={145}
              height={34}
              alt='Logo'
              className='h-auto w-10 rounded-sm'
            />
          </Link>
        </Button>
        <Button
          variant={"white"}
          className='absolute right-4 top-4 z-20'
          asChild
        >
          <Link href='/'>Back</Link>
        </Button>

        <Image
          src='https://utfs.io/f/qPlpyBmwd8UNQmhiUN5oEoxcL67v0k4aHeTGCZpY53l1iSgW'
          width={240}
          height={240}
          alt='Logo Cover Step'
          className='absolute left-0 top-0.5 size-40 md:h-auto md:w-auto'
        />
        <Image
          src='https://utfs.io/f/qPlpyBmwd8UNjhPfnZKKw8vEHUYxf9mgWpOalC0SGu2cnd3J'
          width={145}
          height={34}
          alt='Logo Cover Cartoon'
          className='absolute bottom-0 left-0 right-0 h-52 w-full md:h-96'
        />
        <div className='absolute left-1/2 top-1/4 w-full max-w-md -translate-x-1/2 space-y-3 px-3 text-center text-white'>
          <h2 className='text-lg font-bold sm:text-2xl lg:text-[30px]/9'>
            Build Your Professional Identity with Ease.
          </h2>
          <p className='text-sm lg:text-xl/[30px]'>
            Create stunning portfolios effortlessly and showcase your work to
            the world. No coding skills required—just your ideas and creativity!
          </p>
        </div>
      </div>

      {/* auth forms */}
      <div className='flex w-full items-center justify-center'>{children}</div>
    </div>
  )
}

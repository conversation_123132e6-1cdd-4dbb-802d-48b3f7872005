import { CircularGitHubIcon } from "@/components/icons/icon-github"
import IconGoogle from "@/components/icons/icon-google"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface OAuthButtonProps {
  provider: "google" | "github"
  className?: string
}

interface ProviderDetails {
  icon: JSX.Element
  label: string
  url: string
  disabled?: boolean
}

const OAuthButton = ({ provider, className }: OAuthButtonProps) => {
  const getProviderDetails = (): ProviderDetails => {
    if (provider === "google") {
      return {
        icon: <IconGoogle className='mr-2 h-6 w-6' />,
        label: "Google",
        disabled: false, // Set to true if Google OAuth is unavailable
        url: `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/google`, // Update with your actual Google OAuth API endpoint
      }
    } else if (provider === "github") {
      return {
        icon: <CircularGitHubIcon className='mr-2 h-7 w-7' />,
        label: "GitHub",
        disabled: false, // Set to true if GitHub OAuth is unavailable
        url: `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/github`, // Update with your actual GitHub OAuth API endpoint
      }
    } else {
      throw new Error("Unsupported provider")
    }
  }

  const { icon, label, url, disabled } = getProviderDetails()

  const handleOAuthRedirect = () => {
    if (!disabled) {
      // Redirect to the OAuth URL
      window.location.href = url
    }
  }

  return (
    <Button
      onClick={handleOAuthRedirect}
      disabled={disabled}
      variant='outline-general'
      size='lg'
      className={cn("w-full", className)}
    >
      {icon}
      {label}
    </Button>
  )
}

export default OAuthButton

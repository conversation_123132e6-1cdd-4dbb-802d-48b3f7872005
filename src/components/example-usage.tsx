"use client"

import { useAIStructured, useAIText } from "@/hooks/use-ai"

export function ExampleComponent() {
  const { generateText, isLoading: isGeneratingText } = useAIText({
    onSuccess: (text) => {
      console.log("Generated text:", text)
    },
  })

  const { generateStructuredData, isLoading: isGeneratingStructured } =
    useAIStructured({
      onSuccess: (data) => {
        console.log("Generated structured data:", data)
      },
    })

  const handleTextGeneration = async () => {
    await generateText(
      "This is my input text",
      "Please summarize this text in a professional tone",
    )
  }

  const handleStructuredGeneration = async () => {
    await generateStructuredData("This is my resume text", "website-123")
  }

  return (
    <div>
      <button onClick={handleTextGeneration} disabled={isGeneratingText}>
        Generate Text
      </button>

      <button
        onClick={handleStructuredGeneration}
        disabled={isGeneratingStructured}
      >
        Generate Structured Data
      </button>
    </div>
  )
}

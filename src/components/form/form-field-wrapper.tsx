// components/form/FormFieldWrapper.tsx
import { ReactNode } from "react"
import { Control, FieldValues, Path } from "react-hook-form"

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { cn } from "@/lib/utils"

interface FormFieldWrapperProps<T extends FieldValues> {
  control: Control<T>
  name: Path<T>
  label: string
  className?: string
  required?: boolean
  children: (field: any) => ReactNode // Ensure children is a function that takes field as an argument
}

export const FormFieldWrapper = <T extends FieldValues>({
  control,
  name,
  label,
  className,
  required,
  children,
}: FormFieldWrapperProps<T>) => (
  <FormField
    control={control}
    name={name}
    render={({ field }) => (
      <FormItem className={cn(className)}>
        <FormLabel>
          {label}
          {required && <span className='text-red-500'>*</span>}
        </FormLabel>
        <FormControl>{children(field)}</FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
)

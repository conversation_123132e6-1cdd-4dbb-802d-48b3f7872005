// components/form/NumberInput.tsx
import { Control, FieldValues, Path } from "react-hook-form"

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface NumberInputProps<T extends FieldValues> {
  control: Control<T>
  name: Path<T>
  label: string
  placeholder?: string
  className?: string
  required?: boolean
  min?: number
  max?: number
  step?: number
  allowDecimals?: boolean
}

export const NumberInput = <T extends FieldValues>({
  control,
  name,
  label,
  placeholder,
  required,
  className,
  min,
  max,
  step = 1,
  allowDecimals = false,
}: NumberInputProps<T>) => {
  const formatNumber = (value: string): string => {
    // Remove all non-numeric characters except decimal point if allowed
    let cleaned = allowDecimals
      ? value.replace(/[^0-9.-]/g, "")
      : value.replace(/[^0-9-]/g, "")

    // Handle negative sign - only allow at the beginning
    if (cleaned.includes("-")) {
      const parts = cleaned.split("-")
      cleaned = parts.length > 1 ? "-" + parts.join("") : cleaned
      if (cleaned.indexOf("-") > 0) {
        cleaned = cleaned.replace(/-/g, "")
      }
    }

    // Handle decimal point - only allow one if decimals are allowed
    if (allowDecimals && cleaned.includes(".")) {
      const parts = cleaned.split(".")
      if (parts.length > 2) {
        cleaned = parts[0] + "." + parts.slice(1).join("")
      }
    }

    return cleaned
  }

  const validateNumber = (value: string): boolean => {
    if (!value || value === "" || value === "-") return true // Allow empty or just minus sign

    const num = parseFloat(value)
    if (isNaN(num)) return false

    if (min !== undefined && num < min) return false
    if (max !== undefined && num > max) return false

    return true
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow: backspace, delete, tab, escape, enter
    if ([8, 9, 27, 13, 46].includes(e.keyCode)) return

    // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
    if (e.ctrlKey && [65, 67, 86, 88, 90].includes(e.keyCode)) return

    // Allow: home, end, left, right, down, up
    if ([35, 36, 37, 39, 40, 38].includes(e.keyCode)) return

    // Allow decimal point if decimals are allowed and not already present
    if (allowDecimals && e.key === "." && !e.currentTarget.value.includes("."))
      return

    // Allow minus sign only at the beginning and if min is not set or allows negative
    if (
      e.key === "-" &&
      e.currentTarget.selectionStart === 0 &&
      !e.currentTarget.value.includes("-") &&
      (min === undefined || min < 0)
    )
      return

    // Ensure that it is a number and stop the keypress
    if (
      (e.shiftKey || e.keyCode < 48 || e.keyCode > 57) &&
      (e.keyCode < 96 || e.keyCode > 105)
    ) {
      e.preventDefault()
    }
  }

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn("relative", className)}>
          <FormLabel>
            {label}
            {required && <span className='text-red-500'>*</span>}
          </FormLabel>

          <FormControl>
            <Input
              type='text'
              placeholder={placeholder}
              {...field}
              value={field.value ?? ""}
              onChange={(e) => {
                const formatted = formatNumber(e.target.value)
                field.onChange(formatted)
              }}
              onKeyDown={handleKeyDown}
              onBlur={(e) => {
                const value = e.target.value
                if (value && value !== "-" && !validateNumber(value)) {
                  // Reset to previous valid value or empty
                  field.onChange("")
                }
              }}
            />
          </FormControl>
          <FormMessage />
          {(min !== undefined || max !== undefined) && (
            <p className='mt-1 text-xs text-muted-foreground'>
              {min !== undefined && max !== undefined
                ? `Range: ${min} - ${max}`
                : min !== undefined
                  ? `Minimum: ${min}`
                  : `Maximum: ${max}`}
            </p>
          )}
        </FormItem>
      )}
    />
  )
}

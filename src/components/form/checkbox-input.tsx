// components/form/Checkbox.tsx
import { Control, FieldValues, Path } from "react-hook-form"

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { cn } from "@/lib/utils"

import CustomCheckbox from "../custom/checkbox"

interface CheckboxProps<T extends FieldValues> {
  control: Control<T>
  name: Path<T>
  label: string
  description?: string
  className?: string
  required?: boolean
}

export const Checkbox = <T extends FieldValues>({
  control,
  name,
  label,
  description,
  className,
  required,
}: CheckboxProps<T>) => (
  <FormField
    control={control}
    name={name}
    render={({ field }) => (
      <FormItem className={cn(className)}>
        <FormLabel>
          {label}
          {required && <span className='text-red-500'>*</span>}
        </FormLabel>
        <FormControl>
          <CustomCheckbox
            label={label}
            checked={field.value}
            onCheckedChange={field.onChange}
            description={description}
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
)

// components/form/TextArea.tsx
import { Loader2Icon, Wand2Icon } from "lucide-react"
import { Control, FieldValues, Path } from "react-hook-form"

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"

import { Button } from "../ui/button"

interface TextAreaProps<T extends FieldValues> {
  control: Control<T>
  name: Path<T>
  label: string
  rows?: number
  cols?: number
  placeholder?: string
  className?: string
  required?: boolean
  enableAI?: boolean
  handleAIDescription?: () => void
  isGenerating?: boolean
  aiError?: string | null
}

export const TextArea = <T extends FieldValues>({
  control,
  name,
  label,
  placeholder,
  required,
  className,
  enableAI,
  handleAIDescription,
  isGenerating,
  rows,
  cols,
  aiError,
}: TextAreaProps<T>) => (
  <FormField
    control={control}
    name={name}
    render={({ field }) => (
      <FormItem
        className={cn(
          "relative",

          className,
        )}
      >
        <FormLabel>
          {label}
          {required && <span className='text-red-500'>*</span>}
        </FormLabel>
        <FormControl>
          <Textarea
            placeholder={placeholder}
            rows={rows}
            cols={cols}
            className={cn("resize-none", enableAI && "pb-4")}
            {...field}
          />
        </FormControl>
        {enableAI && (
          <Button
            type='button'
            variant={field.value ? "secondary" : "default"}
            size='sm'
            className={`absolute bottom-2 right-2 min-w-12 md:min-w-20 ${
              field.value
                ? "bg-gradient-to-tr from-purple-400 to-blue-500 text-white"
                : "bg-gradient-to-tr from-red-400 to-yellow-300 text-black"
            }`}
            onClick={handleAIDescription}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <Loader2Icon className='h-4 w-4 animate-spin' />
            ) : (
              <>
                <Wand2Icon className='mr-2 h-4 w-4' />
                {field.value ? "Rewrite with AI" : "Generate with AI"}
              </>
            )}
          </Button>
        )}
        {aiError && <p className='text-sm text-destructive'>{aiError}</p>}
        <FormMessage />
      </FormItem>
    )}
  />
)

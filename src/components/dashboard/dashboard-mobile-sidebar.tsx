"use client"

import { LogOutIcon, MenuIcon } from "lucide-react"
import Link from "next/link"
import { useParams, usePathname, useRouter } from "next/navigation"
import { toast } from "sonner"

import { logoutUser } from "@/actions/auth"
import { Button, buttonVariants } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { DASHBOARD_LINKS } from "@/constants/links"

import { Badge } from "../ui/badge"

const DashboardMobileSidebar = () => {
  // const { signOut } = useClerk();

  const params = useParams()
  const { website } = params

  const pathname = usePathname()

  const router = useRouter()

  const handleLogout = async (): Promise<any> => {
    try {
      const res = await logoutUser()
      if (res.success) {
        router.refresh()
      }
    } catch (error) {
      toast.error("Failed to logout")
      console.error(error)
      return error
    }
  }

  return (
    <div className='flex lg:hidden'>
      <Sheet>
        <SheetTrigger asChild>
          <Button size='icon' variant='ghost' className='flex lg:hidden'>
            <MenuIcon className='size-5' />
          </Button>
        </SheetTrigger>
        {/* <SheetContent className="w-screen max-w-full"> */}
        <SheetContent className='w-screen max-w-full'>
          <div className='mt-5 flex h-full w-full flex-col py-3'>
            {/* <Button
              variant="outline"
              className="w-full justify-start gap-2 px-2"
            >
              <SearchIcon className="size-4" />
              <span className="text-sm">Search...</span>
            </Button> */}
            <ul className='max-h-[calc(100vh-5rem)] w-full space-y-2 overflow-auto py-5'>
              {DASHBOARD_LINKS.map((link, index) => {
                const href = `/dashboard/${website}${link.href}`
                const isActive = pathname === href

                return (
                  <li key={index} className='w-full'>
                    <Link
                      href={href}
                      className={buttonVariants({
                        variant: "ghost",
                        className: isActive
                          ? "w-full !justify-start bg-muted text-primary"
                          : "w-full !justify-start text-foreground/70",
                        // "w-full !justify-start text-foreground/70"
                      })}
                    >
                      <link.icon
                        strokeWidth={2}
                        className='mr-1.5 size-[18px]'
                      />
                      {link.label}
                      {link.badge && (
                        <Badge className='ml-3 bg-gradient-to-tr from-yellow-400 to-yellow-500 text-xs font-bold text-yellow-900'>
                          {link.badge}
                        </Badge>
                      )}
                    </Link>
                  </li>
                )
              })}
            </ul>

            <div className='mt-auto flex w-full flex-col pb-4'>
              <Button
                size='sm'
                variant='ghost'
                className='w-full justify-start gap-2 px-4'
                onClick={handleLogout}
              >
                <LogOutIcon className='mr-1.5 size-4' />
                Logout
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}

export default DashboardMobileSidebar

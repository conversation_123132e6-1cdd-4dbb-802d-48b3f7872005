"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { Plus } from "lucide-react"
import { useParams } from "next/navigation"
import { useCallback, useState } from "react"
import { toast } from "sonner"

import {
  createPort<PERSON>lio,
  deletePort<PERSON><PERSON>,
  getPort<PERSON>lio,
  togglePortfolioStatus,
  updatedPortfolio,
} from "@/actions/portfolio"
import { Container } from "@/components"
import { SkeletonCard } from "@/components/cards/card-skeleton"
import { PortfolioCard } from "@/components/cards/portfolio-card"
import { AlertDialogCustom } from "@/components/custom/custom-alert"
import { EmptyState } from "@/components/custom/empty-state"
import { PortfolioForm } from "@/components/forms/portfolio-form"
import { Button } from "@/components/ui/button"
import { InputSearch } from "@/components/ui/input-search"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useDebounce } from "@/hooks"
import { Port<PERSON>lio, PortfolioFormData } from "@/types/portfolio"

import DashboardPageStickyWrapper from "../dashboard-page-sticky-wrapper"
// import { useDebounce } from "use-debounce"; // Add debounce for search input

export default function ProjectsPage() {
  const [search, setSearch] = useState("")
  const [filter, setFilter] = useState("all")
  const [formOpen, setFormOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedProject, setSelectedProject] = useState<Portfolio | null>(null)

  const { website } = useParams<{ website: string }>()

  // Debounce the search input
  const debouncedSearch = useDebounce(search, 300)

  // Fetch portfolio data
  const {
    data: portfolioData,
    isLoading,

    refetch,
  } = useQuery<Portfolio[] | []>({
    queryKey: ["portfolio", website],
    queryFn: () => getPortfolio(website),
    enabled: !!website,
  })

  // Handle the creation or update of a portfolio
  const { mutate } = useMutation({
    mutationFn: (data: PortfolioFormData) => {
      console.log("data", data)
      if (selectedProject) {
        return updatedPortfolio({ ...data, _id: selectedProject._id })
      } else {
        return createPortfolio(website, data)
      }
    },
    onSuccess: () => {
      if (selectedProject) {
        toast.success(` ${selectedProject.title} updated successfully.`)
      } else {
        toast.success("Portfolio created successfully.")
      }
      refetch()
      setFormOpen(false)
    },
  })

  // Handle project deletion
  const { mutate: deleteMutate, isPending: deleteMutatePending } = useMutation({
    mutationFn: (projectId: string) => deletePortfolio(projectId),
    onError: (error: Error) => {
      console.error(error)
      toast.error("Failed to delete the project. Please try again.")
    },
    onSuccess: () => {
      refetch()
      setDeleteDialogOpen(false)
      toast.success("Project deleted successfully.")
    },
  })

  // Optimistic toggle portfolio status (enabled/disabled)
  const { mutate: toggleMutate, isPending: toggleMutatePending } = useMutation({
    mutationFn: (portfolioId: string) => togglePortfolioStatus(portfolioId),
    onMutate: (portfolioId) => {
      console.log(portfolioId)
      // Optimistic update: Immediately toggle status
      // refetch(); // Uncomment if refetching is needed after the update
    },
    onError: (error, portfolioId, context) => {
      // Handle error and revert if needed
      console.log(error, portfolioId, context)
      refetch()
    },
    onSuccess: () => {
      toast.success("Portfolio status updated successfully.")
      refetch() // You can enable refetch if needed after success
    },
  })

  const filterAndSortProjects = (
    projects: Portfolio[] | undefined,
    search: string,
    filter: string,
  ) => {
    return projects
      ?.filter((portfolio) => {
        if (filter === "enabled") return portfolio.is_enabled
        if (filter === "disabled") return !portfolio.is_enabled
        return true
      })
      .filter((portfolio) =>
        portfolio.title.toLowerCase().includes(search.toLowerCase()),
      )
  }

  const filteredProjects = filterAndSortProjects(
    portfolioData,
    debouncedSearch,
    filter,
  )

  const handleSubmit = (data: PortfolioFormData) => {
    mutate(data)
  }

  // Handle project deletion
  const handleDeleteProject = () => {
    if (!selectedProject || !selectedProject?._id) {
      toast.info("No project selected to delete.")
      return
    }
    deleteMutate(selectedProject._id)
  }

  // Handle toggling project status (enabled/disabled)
  const handleToggleProject = useCallback(
    (portfolio: Portfolio) => {
      if (!portfolio._id) {
        toast.error("Project ID is required.")
        return
      }
      if (toggleMutatePending) {
        return
      }
      toast.info(`${portfolio.is_enabled ? "Hiding" : "Enabling"} Project...`)
      toggleMutate(portfolio._id)
    },
    [toggleMutate, toggleMutatePending],
  )

  return (
    <Container delay={0.3} className='mx-auto pb-10'>
      <DashboardPageStickyWrapper>
        <div className='mb-4 flex items-center justify-between'>
          <h1 className='text-3xl font-bold'>
            Projects ({portfolioData?.length})
          </h1>
          <Button onClick={() => setFormOpen(true)}>
            <Plus className='mr-2 h-4 w-4' />
            Add Project
          </Button>
        </div>

        <div className='mb-4 flex w-full items-center justify-start gap-4'>
          <InputSearch
            placeholder='Search projects...'
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className='w-full md:min-w-96'
          />
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className='w-[180px] text-xs'>
              <SelectValue placeholder='Filter by status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All</SelectItem>
              <SelectItem value='enabled'>Enabled</SelectItem>
              <SelectItem value='disabled'>Disabled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </DashboardPageStickyWrapper>

      <section className='px-4 py-3 md:px-6 md:pb-4 md:pt-3 2xl:px-8 2xl:py-6 2xl:pt-5'>
        {/* <Separator /> */}
        {isLoading ? (
          <div className='mt-4 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'>
            {Array.from({ length: 10 }).map((_, index) => (
              <SkeletonCard key={index} />
            ))}
          </div>
        ) : (filteredProjects ?? []).length > 0 ? (
          <div className='mt-4 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'>
            {filteredProjects?.map((portfolio) => (
              <PortfolioCard
                key={portfolio._id}
                portfolio={portfolio}
                onEdit={(portfolio: Portfolio) => {
                  console.log(portfolio)
                  setSelectedProject(portfolio)
                  setFormOpen(true)
                }}
                onDelete={(portfolio: Portfolio) => {
                  setSelectedProject(portfolio)
                  setDeleteDialogOpen(true)
                }}
                onToggle={handleToggleProject}
              />
            ))}
          </div>
        ) : (
          <EmptyState
            title='No projects found'
            description="Try adjusting your search or filter to find what you're looking for."
          />
        )}
      </section>

      <PortfolioForm
        open={formOpen}
        onOpenChange={setFormOpen}
        onSubmit={handleSubmit}
        defaultValues={selectedProject ?? undefined}
      />

      <AlertDialogCustom
        open={deleteDialogOpen}
        onOpenChange={(open) => {
          setDeleteDialogOpen(open)
          if (!open) setSelectedProject(null)
        }}
        title={`Delete ${selectedProject?.title}`}
        description={`Are you sure you want to delete "${selectedProject?.title}"? This action cannot be undone.`}
        confirmText={deleteMutatePending ? "Deleting..." : "Delete"}
        confirmButtonClassName='bg-destructive text-destructive-foreground hover:bg-destructive/90'
        isLoading={deleteMutatePending}
        onConfirm={handleDeleteProject}
      />
    </Container>
  )
}

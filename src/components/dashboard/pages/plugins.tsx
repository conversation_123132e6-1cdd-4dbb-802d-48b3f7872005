"use client"
import { SearchIcon } from "lucide-react"
import Image from "next/image"
import { useState } from "react"

import { PluginCard } from "@/components/cards/plugin-card"
import Heading from "@/components/headings/heading"
import { Input } from "@/components/ui/input"
import { categories, plugins } from "@/constants/plugins"
import { bgImages } from "@/constants/sections"

export default function MarketplacePage() {
  const [search, setSearch] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")

  const filteredPlugins = plugins.filter((plugin) => {
    const matchesSearch = search
      ? plugin.name.toLowerCase().includes(search.toLowerCase()) ||
        plugin.description.toLowerCase().includes(search.toLowerCase())
      : true
    const matchesCategory =
      selectedCategory === "All" ? true : plugin.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  return (
    <div className='min-h-screen bg-background'>
      {/* hero section  */}
      <div className='relative overflow-hidden px-2 py-8 md:px-4 md:py-12 lg:py-16'>
        <Image
          src={bgImages[1]}
          alt='hero'
          layout='fill'
          objectFit='cover'
          className='absolute inset-0 -z-0'
        />

        <div className='relative z-[1] mx-auto pl-6'>
          <Heading className='text-left text-primary-foreground'>
            Elevate your portfolio with ThePortfolyo plugins
          </Heading>
          <p className='max-w-2xl text-sm text-primary-foreground md:text-base lg:text-lg 2xl:text-xl'>
            Enhance functionality, boost engagement, and streamline operations
            with our comprehensive plugin marketplace.
          </p>
        </div>
      </div>

      {/* hero sections ends  */}

      <section className='mx-auto px-4 py-8'>
        <div className='flex flex-col gap-6'>
          <div className='sticky top-20 z-10 flex flex-col items-start gap-4 bg-background py-3 sm:flex-row'>
            <div className='relative w-full sm:max-w-xs'>
              <SearchIcon className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-foreground' />
              <Input
                type='search'
                placeholder='Search plugins...'
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className='pl-9'
              />
            </div>

            {/* category tabs */}
            <div className='flex flex-wrap gap-2'>
              {categories.map((category) => (
                <button
                  key={category.name}
                  onClick={() => setSelectedCategory(category.name)}
                  className={`inline-flex items-center rounded-lg px-3 py-1.5 text-sm ${
                    selectedCategory === category.name
                      ? "bg-primary text-white"
                      : "bg-muted hover:bg-muted/80"
                  } transition-colors`}
                >
                  <span className='font-medium'>{category.name}</span>
                  <span className='ml-2 text-sm text-muted-foreground'>
                    {category.count}
                  </span>
                </button>
              ))}
            </div>
            {/*  */}
          </div>
          {/* <PluginGrid plugins={plugins} /> */}

          <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
            {filteredPlugins.map((plugin) => (
              <PluginCard key={plugin.id} plugin={plugin} />
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}

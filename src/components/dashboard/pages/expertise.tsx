"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { Plus } from "lucide-react"
import { useParams } from "next/navigation"
import { useCallback, useState } from "react"
import { toast } from "sonner"

import {
  createServiceSkill,
  deleteServiceSkill,
  getSericeSkillsByWebsiteAndType,
  toggleServiceSkillStatus,
  updateServiceSkill,
} from "@/actions/service-skill"
import { Container } from "@/components"
import { SkeletonCard } from "@/components/cards/card-skeleton"
import { ServiceSkillCard } from "@/components/cards/service-skill-card"
import { AlertDialogCustom } from "@/components/custom/custom-alert"
import { EmptyState } from "@/components/custom/empty-state"
import { ServiceSkillForm } from "@/components/forms/service-skill-form"
import { Button } from "@/components/ui/button"
import { InputSearch } from "@/components/ui/input-search"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useDebounce } from "@/hooks"
import { ServiceSkill, ServiceSkillFormData } from "@/types/service-skill"

import DashboardPageStickyWrapper from "../dashboard-page-sticky-wrapper"

export default function ServiceSkillsPage() {
  const [search, setSearch] = useState("")
  const [filter, setFilter] = useState("all")
  const [formOpen, setFormOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedServiceSkill, setSelectedServiceSkill] =
    useState<ServiceSkill | null>(null)

  const { website, type } = useParams<{
    website: string
    type: "service" | "skill" | "language"
  }>()

  // Debounce the search input
  const debouncedSearch = useDebounce(search, 300)

  // Fetch service skills data
  const {
    data: serviceSkillsData,
    isLoading,

    refetch,
  } = useQuery<ServiceSkill[] | []>({
    queryKey: ["expertise", website, type],
    queryFn: () => getSericeSkillsByWebsiteAndType(website, type),
    enabled: !!website,
  })

  // Handle the creation or update of a service skill
  const { mutate } = useMutation({
    mutationFn: (data: ServiceSkillFormData) => {
      if (selectedServiceSkill) {
        return updateServiceSkill({ ...data, _id: selectedServiceSkill._id })
      } else {
        return createServiceSkill(website, data)
      }
    },
    onSuccess: () => {
      if (selectedServiceSkill) {
        toast.success(` ${selectedServiceSkill.title} updated successfully.`)
      } else {
        toast.success(`${type} created successfully.`)
      }
      refetch()
      setFormOpen(false)
    },
  })

  // Handle service skill deletion
  const { mutate: deleteMutate, isPending: deleteMutatePending } = useMutation({
    mutationFn: (serviceSkillId: string) => deleteServiceSkill(serviceSkillId),
    onError: () => {
      toast.error(`Failed to delete the ${type}. Please try again.`)
    },
    onSuccess: () => {
      refetch()
      setDeleteDialogOpen(false)
      toast.success(`${type} deleted successfully.`)
    },
  })

  // Optimistic toggle service skill status (enabled/disabled)
  const { mutate: toggleMutate } = useMutation({
    mutationFn: (serviceSkillId: string) =>
      toggleServiceSkillStatus(serviceSkillId),
    onMutate: () => {
      // Optimistic update: Immediately toggle status
      // refetch(); // Uncomment if refetching is needed after the update
    },
    onError: () => {
      // Handle error and revert if needed
      refetch()
    },
    onSuccess: () => {
      toast.success(`${type} status updated successfully.`)
      refetch() // You can enable refetch if needed after success
    },
  })

  const filterAndSortServiceSkills = (
    serviceSkills: ServiceSkill[] | undefined,
    search: string,
    filter: string,
  ) => {
    return serviceSkills
      ?.filter((serviceSkill) => {
        if (filter === "enabled") return serviceSkill.is_enabled
        if (filter === "disabled") return !serviceSkill.is_enabled
        return true
      })
      .filter((serviceSkill) =>
        serviceSkill.title.toLowerCase().includes(search.toLowerCase()),
      )
  }

  const filteredServiceSkills = filterAndSortServiceSkills(
    serviceSkillsData,
    debouncedSearch,
    filter,
  )

  const handleSubmit = (data: ServiceSkillFormData) => {
    mutate(data)
  }

  // Handle service skill deletion
  const handleDeleteServiceSkill = () => {
    if (!selectedServiceSkill || !selectedServiceSkill?._id) {
      toast.info(`No ${type} selected to delete.`)
      return
    }
    deleteMutate(selectedServiceSkill._id)
  }

  const handleAddServiceSkill = () => {
    setSelectedServiceSkill(null)
    setFormOpen(true)
  }

  // Handle toggling service skill status (enabled/disabled)
  const handleToggleServiceSkill = useCallback(
    (serviceSkill: ServiceSkill) => {
      if (!serviceSkill._id) {
        toast.error(`${type} ID is missing.`)
        return
      }
      toggleMutate(serviceSkill._id)
    },
    [toggleMutate, type],
  )

  return (
    <Container delay={0.3} className='mx-auto pb-10 pt-2'>
      <DashboardPageStickyWrapper>
        <div className='mb-4 flex items-center justify-between'>
          <h1 className='text-3xl font-bold capitalize'>
            {type}s ({serviceSkillsData?.length})
          </h1>
          <Button onClick={handleAddServiceSkill}>
            <Plus className='mr-2 h-4 w-4 capitalize' />
            Add {type}
          </Button>
        </div>

        <div className='mb-4 flex w-full items-center justify-start gap-4'>
          <InputSearch
            placeholder={`Search ${type}s`}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className='w-full md:min-w-96'
          />
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className='w-[180px] text-xs'>
              <SelectValue placeholder='Filter by status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All</SelectItem>
              <SelectItem value='enabled'>Enabled</SelectItem>
              <SelectItem value='disabled'>Disabled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </DashboardPageStickyWrapper>

      <section className='px-4 py-3 md:px-6 md:pb-4 md:pt-3 2xl:px-8 2xl:py-6 2xl:pt-5'>
        {isLoading ? (
          <div className='mt-4 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'>
            {Array.from({ length: 10 }).map((_, index) => (
              <SkeletonCard key={index} />
            ))}
          </div>
        ) : (filteredServiceSkills ?? []).length > 0 ? (
          <div className='mt-4 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'>
            {filteredServiceSkills?.map((serviceSkill) => (
              <ServiceSkillCard
                key={serviceSkill._id}
                serviceSkill={serviceSkill}
                onEdit={(serviceSkill: ServiceSkill) => {
                  setSelectedServiceSkill(serviceSkill)
                  setFormOpen(true)
                }}
                onDelete={(serviceSkill: ServiceSkill) => {
                  setSelectedServiceSkill(serviceSkill)
                  setDeleteDialogOpen(true)
                }}
                onToggle={handleToggleServiceSkill}
              />
            ))}
          </div>
        ) : (
          <EmptyState
            title={`No ${type}s found`}
            description="Try adjusting your search or filter to find what you're looking for. Or add a new one."
          />
        )}
      </section>

      <ServiceSkillForm
        type={type}
        open={formOpen}
        onOpenChange={setFormOpen}
        onSubmit={handleSubmit}
        defaultValues={selectedServiceSkill ?? undefined}
      />

      <AlertDialogCustom
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title='Are you sure?'
        description={`This action cannot be undone. This will permanently delete the ${type}.`}
        confirmText={deleteMutatePending ? "Deleting..." : "Delete"}
        confirmButtonClassName='bg-destructive text-destructive-foreground hover:bg-destructive/90'
        isLoading={deleteMutatePending}
        onConfirm={handleDeleteServiceSkill}
      />
    </Container>
  )
}

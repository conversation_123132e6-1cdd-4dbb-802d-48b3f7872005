"use client"
import { useQuery } from "@tanstack/react-query"
import { EditIcon } from "lucide-react"
import Image from "next/image"
import { useParams } from "next/navigation"
import { useEffect, useState } from "react"

import { getAboutbyWebsite } from "@/actions/about"
import { Container } from "@/components"
import Banner from "@/components/custom/banner"
import EditAboutForm from "@/components/forms/about-form"
import { Button } from "@/components/ui/button"
import { getAboutSampleData } from "@/data/about/about-samples"
import { About } from "@/types/about"

export default function AboutPageContent() {
  const params = useParams<{ website: string }>()
  const website = params?.website
  const [isEditing, setIsEditing] = useState(false)
  // const isEditing = searchParams.get("edit") === "true" // Check if "edit=true" is in the URL
  const [aboutData, setAboutData] = useState<About>({} as About)

  const { data: about, isLoading: aboutLoading } = useQuery({
    queryKey: ["about", website],
    queryFn: async () => {
      if (!website) throw new Error("Website ID is required")
      return await getAboutbyWebsite(website)
    },
    enabled: !!website,
  })

  useEffect(() => {
    if (about) {
      setAboutData(about)
    } else {
      setIsEditing(true)
    }
  }, [about])

  const handleToggleEdit = () => {
    setIsEditing((prev) => !prev)
  }

  const handleFillSampleData = () => {
    const sampleAboutData = getAboutSampleData()

    setAboutData(sampleAboutData)
  }

  return (
    <Container
      delay={0.3}
      reverse
      className='mx-auto min-h-screen bg-background'
    >
      <div className='relative w-full md:h-48 xl:h-56'>
        {isEditing && !about && (
          <div className='absolute left-4 top-4'>
            <Button variant='outline' onClick={handleFillSampleData}>
              Fill Sample Details
            </Button>
          </div>
        )}
        <div className='absolute right-4 top-4'>
          <Button
            variant='destructive'
            className='gap-1'
            onClick={handleToggleEdit}
          >
            <EditIcon size={20} />
            {isEditing ? "Close Edit" : "Edit Details"}
          </Button>
        </div>
        <Image
          src='https://assets.theportfolyo.com/WEBSITE-IMAGES+/1714055095360-theportfolyobnner.webp'
          alt='banner'
          width={1920}
          height={500}
          className='h-full w-full object-cover'
        />
      </div>

      <div className='relative p-4'>
        {isEditing ? (
          <EditAboutForm
            handleToggleEdit={handleToggleEdit}
            initialData={aboutData}
            website={website}
            fillSampleData={handleFillSampleData}
          />
        ) : (
          !aboutLoading && <Banner about={aboutData} />
        )}
      </div>
    </Container>
  )
}

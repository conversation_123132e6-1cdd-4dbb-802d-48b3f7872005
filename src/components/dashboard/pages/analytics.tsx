"use client"
import { useQuery } from "@tanstack/react-query"
import { useParams } from "next/navigation"

import { getAnalyticsByWebsite } from "@/actions/analytics"
import PageViewsChart from "@/components/analytics/page-views-chart"
import PerformanceOverview from "@/components/analytics/performance-overview"
import SessionDetails<PERSON>hart from "@/components/analytics/session-chart-view"
import VisitorsStats from "@/components/analytics/visitors-stats"
import DashboardPageStickyWrapper from "@/components/dashboard/dashboard-page-sticky-wrapper"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"

const AnalyticsDashboard = () => {
  const { website } = useParams<{ website: string }>()
  const websiteId = website

  // Fetch analytics data
  const { data: websiteAnalytics, isLoading: isLoadingWebsite } = useQuery({
    queryKey: ["analytics", "WebsiteAnalytics", websiteId],
    queryFn: () =>
      websiteId ? getAnalyticsByWebsite(websiteId, "WebsiteAnalytics") : null,
    enabled: !!websiteId,
    refetchOnWindowFocus: false,
  })

  const { data: pageViews } = useQuery({
    queryKey: ["analytics", "Pageview", websiteId],
    queryFn: () =>
      websiteId ? getAnalyticsByWebsite(websiteId, "Pageview") : null,
    enabled: !!websiteId,
    refetchOnWindowFocus: false,
  })

  const { data: sessions } = useQuery({
    queryKey: ["analytics", "Session", websiteId],
    queryFn: () =>
      websiteId ? getAnalyticsByWebsite(websiteId, "Session") : null,
    enabled: !!websiteId,
    refetchOnWindowFocus: false,
  })

  // const { data: events, isLoading: isLoadingEvents } = useQuery({
  //   queryKey: ["analytics", "Event", websiteId],
  //   queryFn: () =>
  //     websiteId ? getAnalyticsByWebsite(websiteId, "Event") : null,
  //   enabled: !!websiteId,
  //   refetchOnWindowFocus: false,
  // })

  return (
    <div className='relative w-full'>
      {/* {!currentWebsite?.is_pro && (
        <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            <h3 className="font-bold">Sample Analytics</h3>
          </div>
          <p className="mt-2">
            You&apos;re viewing sample analytics data. Upgrade to PRO to track
            your own website analytics.
          </p>
        </div>
      )} */}

      <div className='flex w-full flex-col'>
        <Tabs defaultValue='overview' className='w-full'>
          <DashboardPageStickyWrapper>
            <TabsList className='mx-auto mb-4 w-full max-w-md'>
              <TabsTrigger value='overview' className='flex-1'>
                Overview
              </TabsTrigger>
              <TabsTrigger value='audience' className='flex-1'>
                Audience
              </TabsTrigger>
              <TabsTrigger value='behavior' className='flex-1'>
                Behavior
              </TabsTrigger>
            </TabsList>
          </DashboardPageStickyWrapper>

          <TabsContent value='overview' className='w-full p-4 md:p-6 2xl:p-8'>
            <VisitorsStats
              isLoadingWebsite={isLoadingWebsite}
              websiteAnalytics={websiteAnalytics}
              pageViews={pageViews}
              sessions={sessions}
            />

            <div className='my-4 grid grid-cols-1 gap-6 lg:grid-cols-6'>
              <PageViewsChart pageViews={pageViews} />
              <PerformanceOverview websiteAnalytics={websiteAnalytics} />
            </div>

            <SessionDetailsChart sessions={sessions} />
            {/* <AudienceInsights websiteAnalytics={websiteAnalytics} /> */}
          </TabsContent>

          <TabsContent
            value='audience'
            className='space-y-4 p-4 md:p-6 2xl:p-8'
          >
            <VisitorsStats
              isLoadingWebsite={isLoadingWebsite}
              websiteAnalytics={websiteAnalytics}
              pageViews={pageViews}
              sessions={sessions}
            />

            {/* <AudienceInsights websiteAnalytics={websiteAnalytics} /> */}
            <PerformanceOverview websiteAnalytics={websiteAnalytics} />

            <SessionDetailsChart sessions={sessions} />
          </TabsContent>

          <TabsContent
            value='behavior'
            className='space-y-4 p-4 md:p-6 2xl:p-8'
          >
            <VisitorsStats
              isLoadingWebsite={isLoadingWebsite}
              websiteAnalytics={websiteAnalytics}
              pageViews={pageViews}
              sessions={sessions}
            />

            <div className='my-4 grid grid-cols-1 gap-6 p-4 md:p-6 lg:grid-cols-6 2xl:p-8'>
              <PageViewsChart pageViews={pageViews} />
              {/* <EventsOverview events={events} /> */}
              <PerformanceOverview websiteAnalytics={websiteAnalytics} />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default AnalyticsDashboard

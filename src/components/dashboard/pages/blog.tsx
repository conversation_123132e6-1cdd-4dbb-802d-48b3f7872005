"use client"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { Plus } from "lucide-react"
import Link from "next/link"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { useState } from "react"
import { toast } from "sonner"

import {
  getAllBlogsByWebsiteId,
  softDeleteBlog,
  toggleBlogStatus,
} from "@/actions/blog"
import { Container } from "@/components"
import { BlogCard } from "@/components/cards/blog-card"
import { AlertDialogCustom } from "@/components/custom/custom-alert"
import { EmptyState } from "@/components/custom/empty-state"
import { BlogCardSkeleton } from "@/components/skeletons/blog-card-skeleton"
import { Button } from "@/components/ui/button"
import { InputSearch } from "@/components/ui/input-search"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useUserStore } from "@/store/use-user-store"
import type { Blog } from "@/types/blog"

import DashboardPageStickyWrapper from "../dashboard-page-sticky-wrapper"

export default function BlogPage() {
  const [search, setSearch] = useState("")
  const [filter, setFilter] = useState("all")
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedBlog, setSelectedBlog] = useState<Blog | null>(null)
  const router = useRouter()

  const params = useParams<{ website: string }>()
  const { website } = params
  const { currentWebsite } = useUserStore()
  const queryClient = useQueryClient()

  const {
    data: blogPosts,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["blogs", website],
    queryFn: () => getAllBlogsByWebsiteId(website),
  })

  const deleteMutation = useMutation({
    mutationFn: softDeleteBlog,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["blogs", website] })
      setSelectedBlog(null)
      setDeleteDialogOpen(false)
      toast.success("Blog post deleted successfully", {
        description: "The blog post has been moved to trash",
      })
    },
    onError: (error: Error) => {
      toast.error("Failed to delete blog post", {
        description: error.message || "Please try again later",
      })
    },
  })

  const toggleStatusMutation = useMutation({
    mutationFn: (blogId: string) => toggleBlogStatus(blogId),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["blogs", website] })
      toast.success("Blog status updated", {
        description: `Blog is now ${data.is_enabled ? "enabled" : "disabled"}`,
      })
    },
    onError: (error: Error) => {
      toast.error("Failed to update blog status", {
        description: error.message || "Please try again later",
      })
    },
  })

  const filteredBlogPosts = blogPosts
    ? blogPosts
        ?.filter((blog) => {
          if (filter === "published") return blog.is_published
          if (filter === "draft") return !blog.is_published
          if (filter === "enabled") return blog.is_enabled
          if (filter === "disabled") return !blog.is_enabled
          return true
        })
        ?.filter(
          (blog) =>
            blog.title.toLowerCase().includes(search.toLowerCase()) ||
            blog.content.toLowerCase().includes(search.toLowerCase()),
        )
    : []

  const handleDeleteBlog = async () => {
    if (!selectedBlog) return
    deleteMutation.mutate(selectedBlog._id)
  }

  const handleToggleBlog = async (blog: Blog) => {
    toggleStatusMutation.mutate(blog._id)
  }

  return (
    <Container delay={0.3} className='mx-auto pb-10 pt-2'>
      {!currentWebsite?.is_pro && (
        <div className='bg-destructive/20 py-2 text-center text-white'>
          <p>Upgrade to PRO to view blogs on your website.</p>
        </div>
      )}

      <DashboardPageStickyWrapper>
        <div className='mb-4 flex items-center justify-between'>
          <h1 className='text-3xl font-bold'>Blog Posts</h1>
          <Button asChild>
            <Link href={`/dashboard/${website}/blogs/new`}>
              <Plus className='mr-2 h-4 w-4' />
              Add Blog Post
            </Link>
          </Button>
        </div>

        <div className='mb-4 flex w-full items-center justify-start gap-4'>
          <InputSearch
            placeholder='Search blog posts...'
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className='w-full md:min-w-96'
          />
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className='w-[180px] text-xs'>
              <SelectValue placeholder='Filter posts' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All</SelectItem>
              <SelectItem value='published'>Published</SelectItem>
              <SelectItem value='enabled'>Enabled</SelectItem>
              <SelectItem value='disabled'>Disabled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </DashboardPageStickyWrapper>

      {/* Blog Cards Section */}
      <div className='mt-4 grid grid-cols-1 gap-6 px-4 py-3 md:grid-cols-2 md:px-6 md:pb-4 md:pt-3 lg:grid-cols-3 2xl:grid-cols-4 2xl:px-8 2xl:py-6 2xl:pt-5'>
        {isLoading ? (
          Array.from({ length: 6 }).map((_, index) => (
            <BlogCardSkeleton key={index} />
          ))
        ) : error ? (
          <div className='col-span-full'>
            <EmptyState
              title='Error loading blogs'
              description='There was a problem loading the blog posts. Please try again.'
            />
          </div>
        ) : filteredBlogPosts.length === 0 ? (
          <div className='col-span-full'>
            <EmptyState
              title='No Blogs found'
              description="Try adjusting your search or filter to find what you're looking for."
            />
          </div>
        ) : (
          filteredBlogPosts.map((blog) => (
            <BlogCard
              key={blog._id}
              blog={blog}
              onEdit={(blog: Blog) =>
                router.push(`/dashboard/${website}/blogs/${blog._id}`)
              }
              onDelete={(blog: Blog) => {
                setSelectedBlog(blog)
                setDeleteDialogOpen(true)
              }}
              onToggle={handleToggleBlog}
              isDeleteLoading={deleteMutation.isPending}
              isToggleLoading={toggleStatusMutation.isPending}
            />
          ))
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialogCustom
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title='Are you sure?'
        description='This action cannot be undone. This will permanently delete the blog post.'
        confirmText={deleteMutation.isPending ? "Deleting..." : "Delete"}
        confirmButtonClassName='bg-destructive text-destructive-foreground hover:bg-destructive/90'
        isLoading={deleteMutation.isPending}
        onConfirm={handleDeleteBlog}
      />
    </Container>
  )
}

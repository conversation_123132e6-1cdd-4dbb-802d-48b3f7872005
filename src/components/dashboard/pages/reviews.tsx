"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { Plus } from "lucide-react"
import { useParams } from "next/navigation"
import { useCallback, useState } from "react"
import { toast } from "sonner"

import {
  createReview,
  deleteReview,
  getReviewsByWebsite,
  toggleReviewVisibility,
  updateReview,
} from "@/actions/review"
import { Container } from "@/components"
import { SkeletonCard } from "@/components/cards/card-skeleton"
import { ReviewCard } from "@/components/cards/review-card"
import { AlertDialogCustom } from "@/components/custom/custom-alert"
import { EmptyState } from "@/components/custom/empty-state"
import { ReviewForm } from "@/components/forms/review-form"
import { Button } from "@/components/ui/button"
import { InputSearch } from "@/components/ui/input-search"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useDebounce } from "@/hooks"

import DashboardPageStickyWrapper from "../dashboard-page-sticky-wrapper"

export default function ReviewsPage() {
  // State management
  const [search, setSearch] = useState("")
  const [filter, setFilter] = useState("all")
  const [formOpen, setFormOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedReviewItem, setSelectedReviewItem] = useState<any | null>(null)

  const { website } = useParams<{ website: string }>()

  // Debounced search term
  const debouncedSearch = useDebounce(search, 300)

  // Fetch review items data
  const {
    data: reviewItemsData,
    isLoading,

    refetch,
  } = useQuery<any[] | []>({
    queryKey: ["review", website],
    queryFn: () => getReviewsByWebsite(website),
    enabled: !!website,
  })

  // Create or update review item mutation
  const { mutate } = useMutation({
    mutationFn: (data: any) => {
      if (selectedReviewItem) {
        return updateReview({ ...data, _id: selectedReviewItem._id })
      } else {
        return createReview({ ...data, website })
      }
    },
    onSuccess: (data) => {
      if (data.success) {
        if (selectedReviewItem) {
          toast.success(`Review updated successfully.`)
        } else {
          toast.success("Review created successfully.")
        }
        refetch()
        setFormOpen(false)
      } else {
        toast.error("Something went wrong!")
      }
    },
  })

  // Soft delete review item
  const { mutate: softDeleteMutate, isPending: deleteMutatePending } =
    useMutation({
      mutationFn: (reviewItemId: string) => deleteReview(reviewItemId),
      onError: () => {
        toast.error("Failed to delete the review. Please try again.")
      },
      onSuccess: (data) => {
        if (data.success) {
          refetch()
          setDeleteDialogOpen(false)
          toast.success("Review deleted successfully.")
        } else {
          toast.error("Something went wrong!")
        }
      },
    })

  // Toggle review visibility
  const { mutate: toggleMutate } = useMutation({
    mutationFn: (reviewItemId: string) => toggleReviewVisibility(reviewItemId),
    onSuccess: (data) => {
      if (data.success) {
        toast.success("Review visibility updated successfully.")
        refetch()
      }
    },
  })

  const filterAndSortReviewItems = (
    reviewItems: any[] | undefined,
    search: string,
    filter: string,
  ) => {
    return reviewItems
      ?.filter((reviewItem) => {
        if (filter === "visible") return reviewItem.is_visible
        if (filter === "hidden") return !reviewItem.is_visible
        return true
      })
      .filter((reviewItem) =>
        reviewItem.client_name.toLowerCase().includes(search.toLowerCase()),
      )
  }

  const filteredReviewItems = filterAndSortReviewItems(
    reviewItemsData,
    debouncedSearch,
    filter,
  )

  const handleSubmit = (data: any) => {
    mutate(data)
  }

  // Handle review item soft deletion
  const handleSoftDeleteReviewItem = () => {
    if (!selectedReviewItem || !selectedReviewItem?._id) {
      toast.info("No review selected to delete.")
      return
    }
    softDeleteMutate(selectedReviewItem._id)
  }

  // Handle toggling review visibility
  const handleToggleReviewItem = useCallback(
    (reviewItem: any) => {
      toggleMutate(reviewItem._id)
    },
    [toggleMutate],
  )

  return (
    <Container delay={0.3} className='mx-auto pb-10 pt-2'>
      <DashboardPageStickyWrapper>
        <div className='mb-4 flex items-center justify-between'>
          <h1 className='text-3xl font-bold'>
            Reviews ({reviewItemsData?.length})
          </h1>
          <Button onClick={() => setFormOpen(true)}>
            <Plus className='mr-2 h-4 w-4' />
            Add Review
          </Button>
        </div>

        <div className='mb-4 flex w-full items-center justify-start gap-4'>
          <InputSearch
            placeholder='Search reviews...'
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className='w-full md:min-w-96'
          />
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className='w-[180px] text-xs'>
              <SelectValue placeholder='Filter by visibility' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All</SelectItem>
              <SelectItem value='visible'>Visible</SelectItem>
              <SelectItem value='hidden'>Hidden</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </DashboardPageStickyWrapper>

      <section className='px-4 py-3 md:px-6 md:pb-4 md:pt-3 2xl:px-8 2xl:py-6 2xl:pt-5'>
        {/* <Separator /> */}
        {isLoading ? (
          <div className='mt-4 grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4'>
            {Array.from({ length: 10 }).map((_, index) => (
              <SkeletonCard key={index} />
            ))}
          </div>
        ) : (filteredReviewItems ?? []).length > 0 ? (
          <div className='mt-4 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
            {filteredReviewItems?.map((reviewItem) => (
              <ReviewCard
                key={reviewItem._id}
                review={reviewItem}
                onEdit={(reviewItem: any) => {
                  setSelectedReviewItem(reviewItem)
                  setFormOpen(true)
                }}
                onDelete={(reviewItem: any) => {
                  setSelectedReviewItem(reviewItem)
                  setDeleteDialogOpen(true)
                }}
                onToggle={handleToggleReviewItem}
              />
            ))}
          </div>
        ) : (
          <EmptyState
            title='No reviews found'
            description="Try adjusting your search or filter to find what you're looking for."
          />
        )}
      </section>

      <ReviewForm
        open={formOpen}
        onOpenChange={setFormOpen}
        onSubmit={handleSubmit}
        defaultValues={selectedReviewItem ?? undefined}
      />

      <AlertDialogCustom
        open={deleteDialogOpen}
        onOpenChange={(open) => {
          setDeleteDialogOpen(open)
          if (!open) setSelectedReviewItem(null)
        }}
        title={`Delete ${selectedReviewItem?.title}`}
        description={`Are you sure you want to delete "${selectedReviewItem?.title}"? This action cannot be undone.`}
        confirmText={deleteMutatePending ? "Deleting..." : "Delete"}
        confirmButtonClassName='bg-destructive text-destructive-foreground hover:bg-destructive/90'
        isLoading={deleteMutatePending}
        onConfirm={handleSoftDeleteReviewItem}
      />
    </Container>
  )
}

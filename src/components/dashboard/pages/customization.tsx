"use client"

import { <PERSON><PERSON><PERSON>, Layout, <PERSON><PERSON>, Settings } from "lucide-react"
import Image from "next/image"
import { useState } from "react"

import { Container } from "@/components"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { colorPresets, fonts } from "@/constants/templae-meta"
import { cn } from "@/lib/utils"
import { type Template } from "@/types/template"

interface FaviconUploadProps {
  favicon: string
  onFaviconChange: (value: string) => void
}

const mockCustomization = {
  _id: "1",
  name: "Default Template",
  key: "default-template",
  description: "A customizable template for websites",
  preview_image: "/images/preview.png",
  preview_url: "/live-preview",
  default_meta: {
    sections: [
      { section_name: "hero", is_enabled: true },
      { section_name: "about", is_enabled: true },
      { section_name: "portfolios", is_enabled: true },
      { section_name: "blogs", is_enabled: false },
      { section_name: "analytics", is_enabled: false },
      { section_name: "contacts", is_enabled: true },
      { section_name: "timelines", is_enabled: true },
      { section_name: "reviews", is_enabled: true },
      { section_name: "services-skills", is_enabled: true },
    ],
    favicon: "",
    logo: "",
    keywords: "",
    fonts: {
      heading_font: {
        name: "",
        url: "",
      },
      body_font: {
        name: "",
        url: "",
      },
    },
    color_palette: {
      primary_color: "",
      secondary_color: "",
      accent_color: "",
    },
  },
  is_pro: false,
  tags: ["portfolio", "personal"],
  is_enabled: true,
  createdAt: new Date(),
  updatedAt: new Date(),
}

const tabs = [
  {
    name: "Header & Favicon",
    key: "header",
    icon: Layout,
  },
  { name: "Sections", key: "sections", icon: ImageIcon },
  { name: "Styles", key: "styles", icon: Palette },
  { name: "Advanced", key: "advanced", icon: Settings },
]

export default function CustomizeTemplatePage() {
  const [customization, setCustomization] =
    useState<Template>(mockCustomization)
  const [selectedTab, setSelectedTab] = useState<string>("header")
  const [selectedPreset, setSelectedPreset] = useState<string>("")

  const handlePresetChange = (presetId: string) => {
    const preset = colorPresets.find((p) => p.id === presetId)
    if (preset) {
      setSelectedPreset(presetId)
      setCustomization((prev) => ({
        ...prev,
        default_meta: {
          ...prev.default_meta,
          color_palette: preset.colors,
        },
      }))
    }
  }

  const renderTabContent = () => {
    switch (selectedTab) {
      case "header":
        return (
          <div className='space-y-8'>
            <div>
              <h2 className='mb-6 text-2xl font-semibold'>Header & Favicon</h2>
              <div className='space-y-8'>
                <div>
                  <Label className='text-base'>Website Logo</Label>
                  <p className='mb-4 text-sm text-muted-foreground'>
                    Upload your Website logo or enter its URL.
                  </p>
                  <Input
                    value={customization.default_meta.logo}
                    onChange={(e) =>
                      setCustomization((prev) => ({
                        ...prev,
                        default_meta: {
                          ...prev.default_meta,
                          logo: e.target.value,
                        },
                      }))
                    }
                    placeholder='Enter logo URL'
                  />
                </div>

                <FaviconUpload
                  favicon={customization.default_meta.favicon}
                  onFaviconChange={(value) =>
                    setCustomization((prev) => ({
                      ...prev,
                      default_meta: {
                        ...prev.default_meta,
                        favicon: value,
                      },
                    }))
                  }
                />
              </div>
            </div>
          </div>
        )
      case "sections":
        return (
          <div className='space-y-6'>
            <h2 className='text-2xl font-semibold'>Sections</h2>
            <Card>
              <CardContent className='p-6'>
                {customization.default_meta.sections.map((section, index) => (
                  <div
                    key={index}
                    className={cn(
                      "flex items-center justify-between border-b py-3 last:border-0",
                      !section.is_enabled ? "opacity-50" : "",
                    )}
                  >
                    <span className='capitalize'>
                      {section.section_name.replace("-", " ")}
                    </span>
                    <Switch
                      checked={section.is_enabled}
                      onCheckedChange={(checked) =>
                        setCustomization((prev) => {
                          const updatedSections = [
                            ...prev.default_meta.sections,
                          ]
                          updatedSections[index].is_enabled = checked
                          return {
                            ...prev,
                            default_meta: {
                              ...prev.default_meta,
                              sections: updatedSections,
                            },
                          }
                        })
                      }
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        )
      case "styles":
        return (
          <div className='space-y-8'>
            <div>
              <h2 className='mb-6 text-2xl font-semibold'>Fonts & Colors</h2>
              <div className='space-y-6'>
                <div>
                  <Label className='text-base'>Font Family</Label>
                  <p className='mb-4 text-sm text-muted-foreground'>
                    Choose from 1000+ font families to enhance the visual style
                    of your site.
                  </p>
                  <Select
                    onValueChange={(value) => {
                      const selectedFont = fonts.find(
                        (font) => font.name === value,
                      )
                      setCustomization((prev) => ({
                        ...prev,
                        default_meta: {
                          ...prev.default_meta,
                          fonts: {
                            ...prev.default_meta.fonts,
                            heading_font: selectedFont || { name: "", url: "" },
                          },
                        },
                      }))
                    }}
                  >
                    <SelectTrigger className='w-full'>
                      <SelectValue placeholder='Select font' />
                    </SelectTrigger>
                    <SelectContent>
                      {fonts.map((font) => (
                        <SelectItem key={font.name} value={font.name}>
                          {font.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className='space-y-6'>
                  <div>
                    <Label className='text-base'>Colors</Label>
                    <p className='mb-4 text-sm text-muted-foreground'>
                      Set your main colors according to your branding.
                    </p>
                    <div className='grid gap-4'>
                      <div className='grid gap-2'>
                        <Label htmlFor='primaryColor'>Primary Color</Label>
                        <div className='flex gap-2'>
                          <Input
                            id='primaryColor'
                            type='color'
                            value={
                              customization.default_meta.color_palette
                                .primary_color
                            }
                            onChange={(e) =>
                              setCustomization((prev) => ({
                                ...prev,
                                default_meta: {
                                  ...prev.default_meta,
                                  color_palette: {
                                    ...prev.default_meta.color_palette,
                                    primary_color: e.target.value,
                                  },
                                },
                              }))
                            }
                            className='h-10 w-[100px] p-1'
                          />
                          <Input
                            value={
                              customization.default_meta.color_palette
                                .primary_color
                            }
                            onChange={(e) =>
                              setCustomization((prev) => ({
                                ...prev,
                                default_meta: {
                                  ...prev.default_meta,
                                  color_palette: {
                                    ...prev.default_meta.color_palette,
                                    primary_color: e.target.value,
                                  },
                                },
                              }))
                            }
                            placeholder='#000000'
                          />
                        </div>
                      </div>
                      <div className='grid gap-2'>
                        <Label htmlFor='secondaryColor'>Secondary Color</Label>
                        <div className='flex gap-2'>
                          <Input
                            id='secondaryColor'
                            type='color'
                            value={
                              customization.default_meta.color_palette
                                .secondary_color
                            }
                            onChange={(e) =>
                              setCustomization((prev) => ({
                                ...prev,
                                default_meta: {
                                  ...prev.default_meta,
                                  color_palette: {
                                    ...prev.default_meta.color_palette,
                                    secondary_color: e.target.value,
                                  },
                                },
                              }))
                            }
                            className='h-10 w-[100px] p-1'
                          />
                          <Input
                            value={
                              customization.default_meta.color_palette
                                .secondary_color
                            }
                            onChange={(e) =>
                              setCustomization((prev) => ({
                                ...prev,
                                default_meta: {
                                  ...prev.default_meta,
                                  color_palette: {
                                    ...prev.default_meta.color_palette,
                                    secondary_color: e.target.value,
                                  },
                                },
                              }))
                            }
                            placeholder='#000000'
                          />
                        </div>
                      </div>
                      <div className='grid gap-2'>
                        <Label htmlFor='accentColor'>Accent Color</Label>
                        <div className='flex gap-2'>
                          <Input
                            id='accentColor'
                            type='color'
                            value={
                              customization.default_meta.color_palette
                                .accent_color
                            }
                            onChange={(e) =>
                              setCustomization((prev) => ({
                                ...prev,
                                default_meta: {
                                  ...prev.default_meta,
                                  color_palette: {
                                    ...prev.default_meta.color_palette,
                                    accent_color: e.target.value,
                                  },
                                },
                              }))
                            }
                            className='h-10 w-[100px] p-1'
                          />
                          <Input
                            value={
                              customization.default_meta.color_palette
                                .accent_color
                            }
                            onChange={(e) =>
                              setCustomization((prev) => ({
                                ...prev,
                                default_meta: {
                                  ...prev.default_meta,
                                  color_palette: {
                                    ...prev.default_meta.color_palette,
                                    accent_color: e.target.value,
                                  },
                                },
                              }))
                            }
                            placeholder='#000000'
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className='space-y-4'>
                    <div>
                      <Label className='text-base'>Color Palette Presets</Label>
                      <p className='mb-4 text-sm text-muted-foreground'>
                        Choose from our predefined color combinations.
                      </p>
                    </div>
                    <RadioGroup
                      value={selectedPreset}
                      onValueChange={handlePresetChange}
                      className='grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4'
                    >
                      {colorPresets.map((preset) => (
                        <div key={preset.id} className='relative'>
                          <RadioGroupItem
                            value={preset.id}
                            id={preset.id}
                            className='sr-only'
                          />
                          <Label
                            htmlFor={preset.id}
                            className='flex flex-col gap-2 rounded-lg border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary'
                          >
                            <div className='flex gap-2'>
                              {Object.values(preset.colors).map((color, i) => (
                                <div
                                  key={i}
                                  className='h-8 w-8 rounded-full border'
                                  style={{ backgroundColor: color }}
                                />
                              ))}
                            </div>
                            <span className='text-sm font-medium'>
                              {preset.name}
                            </span>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      case "advanced":
        return (
          <div className='space-y-6'>
            <h2 className='text-2xl font-semibold'>Advanced</h2>
            <div>
              <Label htmlFor='customCSS'>Custom CSS</Label>
              <textarea
                id='customCSS'
                className='mt-2 min-h-[300px] w-full rounded-md border bg-background p-4 text-foreground'
                value={customization.default_meta.keywords}
                onChange={(e) =>
                  setCustomization((prev) => ({
                    ...prev,
                    default_meta: {
                      ...prev.default_meta,
                      keywords: e.target.value,
                    },
                  }))
                }
                placeholder='Enter custom CSS here...'
              />
            </div>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <Container delay={0.3} reverse className='min-h-screen bg-background'>
      {/* Header */}
      <div className='sticky top-20 z-10 flex items-center justify-between border-b bg-background px-6 py-4'>
        <h1 className='text-xl font-semibold'>{customization.name}</h1>
        <div className='flex space-x-4'>
          <Button
            variant='outline'
            onClick={() => console.log("Preview", customization)}
          >
            Preview
          </Button>
          <Button onClick={() => console.log("Update", customization)}>
            Update
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className='flex flex-col md:flex-row'>
        {/* Sidebar */}
        <div className='w-full border-r bg-background md:min-h-[calc(100vh-65px)] md:w-64 md:pt-3'>
          <nav className='flex flex-row items-center justify-start gap-2 overflow-auto border-b p-3 pt-4 md:flex-col md:gap-3 md:border-b-0 md:p-4'>
            {tabs.map((tab) => (
              <Button
                variant={"outline"}
                key={tab.key}
                className={cn("w-full justify-start", {
                  "bg-primary text-primary-foreground hover:bg-primary":
                    selectedTab === tab.key,
                  "hover:bg-primary/45": selectedTab !== tab.key,
                })}
                onClick={() => setSelectedTab(tab.key)}
              >
                <tab.icon className='mr-2 h-4 w-4' />
                {tab.name}
              </Button>
            ))}

            {/* <button
              className={`flex items-center space-x-2 w-full p-2 rounded-md transition-colors ${
                selectedTab === "header"
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted"
              }`}
              onClick={() => setSelectedTab("header")}
            >
              <Layout className="h-4 w-4" />
              <span>Header & Favicon</span>
            </button>
            <button
              className={`flex items-center space-x-2 w-full p-2 rounded-md transition-colors ${
                selectedTab === "sections"
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted"
              }`}
              onClick={() => setSelectedTab("sections")}
            >
              <ImageIcon className="h-4 w-4" />
              <span>Sections</span>
            </button>
            <button
              className={`flex items-center space-x-2 w-full p-2 rounded-md transition-colors ${
                selectedTab === "styles"
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted"
              }`}
              onClick={() => setSelectedTab("styles")}
            >
              <Palette className="h-4 w-4" />
              <span>Fonts & Colors</span>
            </button>
            <button
              className={`flex items-center space-x-2 w-full p-2 rounded-md transition-colors ${
                selectedTab === "advanced"
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted"
              }`}
              onClick={() => setSelectedTab("advanced")}
            >
              <Settings className="h-4 w-4" />
              <span>Advanced</span>
            </button> */}
          </nav>
        </div>

        {/* Content */}
        <div className='max-w-3xl flex-1 p-6'>{renderTabContent()}</div>
      </div>
    </Container>
  )
}

function FaviconUpload({ favicon }: FaviconUploadProps) {
  return (
    <div className='space-y-4'>
      <div>
        <Label className='text-base'>Favicon</Label>
        <p className='text-sm text-muted-foreground'>
          Favicon should be a square and at least 48px*48px.
        </p>
      </div>

      <div className='rounded-lg border bg-muted/50'>
        <div className='flex items-center gap-2 border-b bg-muted p-2'>
          <div className='flex gap-1.5'>
            <div className='h-3 w-3 rounded-full bg-red-500' />
            <div className='h-3 w-3 rounded-full bg-yellow-500' />
            <div className='h-3 w-3 rounded-full bg-green-500' />
          </div>
        </div>
        <div className='p-4'>
          <div className='flex items-center gap-3'>
            {favicon ? (
              <Image
                src={favicon}
                alt='Favicon'
                width={48}
                height={48}
                className='rounded'
              />
            ) : (
              <div className='h-12 w-12 rounded bg-muted' />
            )}
            <span className='text-sm font-medium'>
              Your Site - Online Website
            </span>
          </div>
        </div>
      </div>

      <Button
        variant='outline'
        onClick={() => {
          // In a real implementation, this would open a file picker
          console.log("Open file picker")
        }}
      >
        Change image
      </Button>
    </div>
  )
}

"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { Plus } from "lucide-react"
import { useParams } from "next/navigation"
import { useCallback, useState } from "react"
import { toast } from "sonner"

import {
  createTimeline,
  deleteTimeline,
  getTimelinesByWebsiteAndType,
  toggleTimelineStatus,
  updateTimeline,
} from "@/actions/timeline"
import { Container } from "@/components"
import { SkeletonCard } from "@/components/cards/card-skeleton"
import { TimelineCard } from "@/components/cards/timeline-card"
import { AlertDialogCustom } from "@/components/custom/custom-alert"
import { EmptyState } from "@/components/custom/empty-state"
import { TimelineForm } from "@/components/forms/timeline-form"
import { Button } from "@/components/ui/button"
import { InputSearch } from "@/components/ui/input-search"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useDebounce } from "@/hooks"
import { Timeline, TimelineFormData } from "@/types/timeline"

import DashboardPageStickyWrapper from "../dashboard-page-sticky-wrapper"

export default function TimelinePage() {
  // State management
  const [search, setSearch] = useState("")
  const [filter, setFilter] = useState("all")
  const [formOpen, setFormOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedTimelineItem, setSelectedTimelineItem] =
    useState<Timeline | null>(null)

  // Get URL params
  const { website, type = "experience" } = useParams<{
    website: string
    type: "experience" | "education" | "achievement" | "milestone"
  }>()

  // Debounced search
  const debouncedSearch = useDebounce(search, 300)

  // Queries and Mutations
  const {
    data: timelineItemsData,
    isLoading,
    refetch,
  } = useQuery<Timeline[]>({
    queryKey: ["timeline", website, type],
    queryFn: () => getTimelinesByWebsiteAndType(website, type),
    enabled: !!website && !!type,
  })

  const createOrUpdateMutation = useMutation({
    mutationFn: (data: TimelineFormData) => {
      if (selectedTimelineItem) {
        return updateTimeline({ ...data, _id: selectedTimelineItem._id })
      }
      return createTimeline({ ...data })
    },
    onSuccess: () => {
      toast.success(
        selectedTimelineItem
          ? `${selectedTimelineItem.title} updated successfully`
          : `${type} item created successfully`,
      )
      refetch()
      setFormOpen(false)
      setSelectedTimelineItem(null)
    },
    onError: () => {
      toast.error(
        selectedTimelineItem
          ? `Failed to update ${type} item`
          : `Failed to create ${type} item`,
      )
    },
  })

  const deleteMutation = useMutation({
    mutationFn: deleteTimeline,
    onSuccess: () => {
      toast.success(`${type} item deleted successfully`)
      refetch()
      setDeleteDialogOpen(false)
      setSelectedTimelineItem(null)
    },
    onError: () => {
      toast.error(`Failed to delete ${type} item`)
    },
  })

  const toggleMutation = useMutation({
    mutationFn: toggleTimelineStatus,
    onSuccess: () => {
      toast.success(`${type} item status updated successfully`)
      refetch()
    },
    onError: () => {
      toast.error(`Failed to update ${type} item status`)
    },
  })

  // Handlers
  const handleSubmit = (data: TimelineFormData) => {
    createOrUpdateMutation.mutate(data)
  }

  const handleDeleteTimelineItem = () => {
    if (!selectedTimelineItem?._id) {
      toast.error(`Invalid ${type} item selected`)
      return
    }
    deleteMutation.mutate(selectedTimelineItem._id)
  }

  const handleAddTimelineItem = () => {
    setSelectedTimelineItem(null)
    setFormOpen(true)
  }

  const handleToggleTimelineItem = useCallback(
    (timelineItem: Timeline) => {
      if (!timelineItem._id) {
        toast.error(`${type} item ID is missing`)
        return
      }
      toggleMutation.mutate(timelineItem._id)
    },
    [toggleMutation, type],
  )

  // Filters
  const filteredTimelineItems = timelineItemsData
    ?.filter((item) => {
      if (filter === "enabled") return item.is_enabled
      if (filter === "disabled") return !item.is_enabled
      return true
    })
    .filter((item) =>
      item.title.toLowerCase().includes(debouncedSearch.toLowerCase()),
    )

  return (
    <Container delay={0.3} className='mx-auto pb-10 pt-2'>
      <DashboardPageStickyWrapper>
        <div className='mb-4 flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold capitalize'>
              {type} ({timelineItemsData?.length ?? 0})
            </h1>
            <p className='mt-1 text-sm text-muted-foreground'>
              Manage your {type.toLowerCase()} timeline items here
            </p>
          </div>
          <Button
            onClick={handleAddTimelineItem}
            disabled={createOrUpdateMutation.isPending}
          >
            <Plus className='mr-2 h-4 w-4' />
            Add {type} Item
          </Button>
        </div>

        <div className='mb-4 flex w-full flex-col gap-4 sm:flex-row sm:items-center sm:justify-start'>
          <InputSearch
            placeholder={`Search ${type} items...`}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className='w-full sm:max-w-md'
          />
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className='w-full sm:w-[180px]'>
              <SelectValue placeholder='Filter by status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All</SelectItem>
              <SelectItem value='enabled'>Enabled</SelectItem>
              <SelectItem value='disabled'>Disabled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </DashboardPageStickyWrapper>

      <section className='px-4 py-3 md:px-6 md:pb-4 md:pt-3 2xl:px-8 2xl:py-6 2xl:pt-5'>
        {isLoading ? (
          <div className='mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'>
            {Array.from({ length: 8 }).map((_, index) => (
              <SkeletonCard key={index} />
            ))}
          </div>
        ) : (filteredTimelineItems?.length ?? 0) > 0 ? (
          <div className='mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'>
            {filteredTimelineItems?.map((timelineItem) => (
              <TimelineCard
                key={timelineItem._id}
                timeline={timelineItem}
                onEdit={(item) => {
                  setSelectedTimelineItem(item)
                  setFormOpen(true)
                }}
                onDelete={(item) => {
                  setSelectedTimelineItem(item)
                  setDeleteDialogOpen(true)
                }}
                onToggle={handleToggleTimelineItem}
                isToggling={toggleMutation.isPending}
              />
            ))}
          </div>
        ) : (
          <EmptyState
            title={`No ${type} items found`}
            description={
              debouncedSearch || filter !== "all"
                ? "Try adjusting your search or filter to find what you're looking for."
                : `Get started by adding your first ${type.toLowerCase()} item.`
            }
            // action={
            //   <Button onClick={handleAddTimelineItem}>
            //     <Plus className='mr-2 h-4 w-4' />
            //     Add {type} Item
            //   </Button>
            // }
          />
        )}
      </section>

      <TimelineForm
        type={type}
        open={formOpen}
        onOpenChange={(open) => {
          setFormOpen(open)
          if (!open) setSelectedTimelineItem(null)
        }}
        onSubmit={handleSubmit}
        defaultValues={selectedTimelineItem ?? undefined}
        isPending={
          createOrUpdateMutation.isPending ||
          toggleMutation.isPending ||
          deleteMutation.isPending
        }
      />

      <AlertDialogCustom
        open={deleteDialogOpen}
        onOpenChange={(open) => {
          setDeleteDialogOpen(open)
          if (!open) setSelectedTimelineItem(null)
        }}
        title={`Delete ${selectedTimelineItem?.title}`}
        description={`Are you sure you want to delete "${selectedTimelineItem?.title}"? This action cannot be undone.`}
        confirmText={deleteMutation.isPending ? "Deleting..." : "Delete"}
        confirmButtonClassName='bg-destructive text-destructive-foreground hover:bg-destructive/90'
        isLoading={deleteMutation.isPending}
        onConfirm={handleDeleteTimelineItem}
      />
    </Container>
  )
}

"use client"
import { useQuery } from "@tanstack/react-query"
import { ChevronLeft } from "lucide-react"
import { useParams, useRouter } from "next/navigation"
import { toast } from "sonner"

import { applyTemplate, getTemplates } from "@/actions/template"
import { getWebsiteById } from "@/actions/website"
import { Container } from "@/components"
import { SkeletonCard } from "@/components/cards/card-skeleton"
import { EmptyState } from "@/components/custom/empty-state"
import { ThemeCard } from "@/components/themes/theme-marketplace/theme-card"
import { Button } from "@/components/ui/button"
import { Template } from "@/types/template" // Importing Template interface

import DashboardPageStickyWrapper from "../dashboard-page-sticky-wrapper"

export default function TemplatePage() {
  const router = useRouter()

  const params = useParams<{ website: string }>()

  // const { currentWebsite: website } = useUserStore();

  const {
    data: website,

    refetch: refetchWebsite,
  } = useQuery({
    queryKey: ["website", params.website],
    queryFn: async () => await getWebsiteById(params.website),
    enabled: params.website !== "",
  })

  // Fetch service skills data
  const { data: templateData, isLoading } = useQuery<Template[] | []>({
    queryKey: ["templates"],
    queryFn: () => getTemplates(),
  })

  const handleApplyTheme = async (themeId: string) => {
    const toastId = toast.loading("Applying theme...")

    try {
      if (!website) {
        // throw new Error("Website not found");
        toast.error("Website not found")
        return
      }

      const result = await applyTemplate(website?._id, themeId)
      if (result.success) {
        toast.success("Theme applied successfully", {
          id: toastId,
        })

        refetchWebsite()
      }
    } catch (error) {
      toast.error("Failed to apply theme", {
        id: toastId,
      })
    } finally {
      toast.dismiss(toastId)
    }
    // setActiveThemeId(themeId);
  }

  const handleCustomizeTheme = (themeId: string) => {
    router.push(`templates/customize?theme=${themeId}`)
  }

  return (
    <div className='min-h-screen'>
      <DashboardPageStickyWrapper>
        <div className='flex items-center gap-3'>
          <Button variant='ghost' size='icon'>
            <ChevronLeft className='h-5 w-5' />
          </Button>
          <div>
            <h1 className='text-lg font-semibold'>Themes</h1>
            <p className='text-sm text-muted-foreground'>
              Select and customize a theme for your website
            </p>
          </div>
        </div>
      </DashboardPageStickyWrapper>

      <Container
        delay={0.3}
        reverse
        className='px-4 py-3 md:px-6 md:pb-4 md:pt-3 2xl:px-8 2xl:py-6 2xl:pt-5'
      >
        {isLoading ? (
          <div className='mt-4 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'>
            {Array.from({ length: 10 }).map((_, index) => (
              <SkeletonCard key={index} />
            ))}
          </div>
        ) : (templateData ?? []).length > 0 ? (
          <div className='mt-4 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
            {templateData?.map((theme) => (
              <ThemeCard
                key={theme._id}
                theme={theme}
                onApply={handleApplyTheme}
                onCustomize={handleCustomizeTheme}
                isActive={theme._id === website?.template}
                website={website}
              />
            ))}
          </div>
        ) : (
          <EmptyState
            title={`No Templates found`}
            description="Try adjusting your search or filter to find what you're looking for. Or add a new one."
          />
        )}
      </Container>
    </div>
  )
}

"use client"

import { useMutation, useQ<PERSON>y, useQueryClient } from "@tanstack/react-query"
import { Plus } from "lucide-react"
import { useParams } from "next/navigation"
import { useState } from "react"
import { toast } from "sonner"

import { deleteImageFromGallery } from "@/actions/gallery"
import { fetchUserMedia } from "@/actions/users"
import { Container } from "@/components"
import { GalleryCard } from "@/components/cards/gallery-card"
import CustomImageUploader from "@/components/custom/custom-uploader"
import { EmptyState } from "@/components/custom/empty-state"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { InputSearch } from "@/components/ui/input-search"
import { Skeleton } from "@/components/ui/skeleton"
import { useDebounce } from "@/hooks/use-debounce"
import { ImageType } from "@/types/image"

import DashboardPageStickyWrapper from "../dashboard-page-sticky-wrapper"

// Skeleton component for loading images
const SkeletonCard = () => (
  <Card className='flex animate-pulse flex-col justify-between rounded-lg p-4 shadow-sm'>
    <Skeleton className='mb-4 h-48 rounded-md' />
    <Skeleton className='mb-2 h-4 max-w-[90%] rounded' />
    <Skeleton className='mb-2 h-4 max-w-[50%] rounded' />
  </Card>
)

export default function GalleryPage() {
  const queryClient = useQueryClient()
  const params = useParams<{ website: string }>()

  // State management
  const [search, setSearch] = useState("")
  const [formOpen, setFormOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedImage, setSelectedImage] = useState<ImageType | null>(null)
  const [page, setPage] = useState(1)

  const debouncedSearch = useDebounce(search, 300)

  // Fetch images query
  const { data, isLoading, isError } = useQuery({
    queryKey: ["images", params.website, page, debouncedSearch],
    queryFn: async () => {
      if (!params.website) throw new Error("Website ID is required")
      return await fetchUserMedia({
        websiteId: params.website,
        search: debouncedSearch,
        page,
      })
    },
    enabled: !!params.website,
  })

  // Delete image mutation
  const deleteMutation = useMutation({
    mutationFn: async ({
      imageId,
      websiteId,
    }: {
      imageId: string
      websiteId: string
    }) => deleteImageFromGallery(imageId, websiteId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["images", params.website] })
      toast.success("Image deleted successfully")
      setDeleteDialogOpen(false)
      setSelectedImage(null)
    },
    onError: (error: Error) => {
      toast.error("Failed to delete image", {
        description: error.message || "Please try again later",
      })
    },
  })

  const handleDeleteImage = () => {
    if (!selectedImage?._id || !params.website) return
    deleteMutation.mutate({
      imageId: selectedImage._id,
      websiteId: params.website,
    })
  }

  return (
    <Container delay={0.3} className='mx-auto pb-10 pt-2'>
      <DashboardPageStickyWrapper>
        <div className='mb-4 flex items-center justify-between'>
          <h1 className='text-3xl font-bold'>Gallery ({data?.total || 0}) </h1>
          <Button onClick={() => setFormOpen(true)} disabled={!params.website}>
            <Plus className='mr-2 h-4 w-4' />
            Add Image
          </Button>
        </div>

        <div className='mb-4 flex w-full flex-wrap items-center justify-between gap-4'>
          <InputSearch
            placeholder='Search images...'
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className='w-full md:min-w-96'
          />

          <div className='flex items-center justify-center gap-4'>
            <div className='flex items-center justify-center rounded-lg border bg-background px-3 py-[7px] text-xs font-medium text-muted-foreground shadow-sm'>
              Showing&nbsp;
              <span className='text-primary-foreground'>
                &nbsp;{data?.images.length || 0}
              </span>
              &nbsp;of&nbsp;
              <span className='text-primary-foreground'>
                {data?.total || 0}
              </span>
              &nbsp;Entries
            </div>

            <div className='flex items-center justify-center gap-1'>
              <Button
                size='sm'
                variant='white'
                disabled={page <= 1}
                onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
              >
                Previous
              </Button>
              <span className='mx-1 text-center text-xs md:text-sm'>
                Page {page}
              </span>
              <Button
                size='sm'
                variant='white'
                disabled={!data?.total || data.total <= page * 10}
                onClick={() => setPage((prev) => prev + 1)}
              >
                Next
              </Button>
            </div>
          </div>
        </div>
      </DashboardPageStickyWrapper>

      <div className='mt-4 grid grid-cols-1 gap-4 px-4 py-3 md:grid-cols-2 md:px-6 md:pb-4 md:pt-3 lg:grid-cols-3 2xl:grid-cols-4 2xl:px-8 2xl:py-6 2xl:pt-5'>
        {isLoading && (
          <>
            {Array.from({ length: 9 }).map((_, index) => (
              <SkeletonCard key={index} />
            ))}
          </>
        )}

        {isError && (
          <EmptyState
            title='Error loading images'
            description='There was a problem loading your images. Please try again.'
          />
        )}

        {!isLoading && !isError && data?.images.length === 0 && (
          <EmptyState
            title='No images found'
            description='Try adjusting your search or add some images to your gallery.'
          />
        )}

        {data?.images.map((image: ImageType) => (
          <GalleryCard
            key={image._id}
            galleryItem={image}
            onDelete={(image) => {
              setSelectedImage(image)
              setDeleteDialogOpen(true)
            }}
          />
        ))}
      </div>

      <Dialog
        open={formOpen}
        onOpenChange={(open) => {
          setFormOpen(open)
          if (!open) setSelectedImage(null)
        }}
      >
        <DialogContent className='max-h-[90vh] overflow-y-auto sm:max-w-[700px]'>
          <CustomImageUploader websiteId={params.website} />
        </DialogContent>
      </Dialog>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              image
              {selectedImage?.title ? ` "${selectedImage.title}"` : ""}.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteImage}
              disabled={deleteMutation.isPending}
              className='bg-destructive text-destructive-foreground hover:bg-destructive/90'
            >
              {deleteMutation.isPending ? "Deleting..." : "Delete Image"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Container>
  )
}

import { motion } from "framer-motion"
import { PlusCircle } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"

export function EmptyState({ onClick }: { onClick: () => void }) {
  return (
    <Card className='mx-auto max-w-xl p-8 text-center'>
      <CardContent>
        <motion.div
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{
            duration: 1,
            repeat: Number.POSITIVE_INFINITY,
            repeatType: "reverse",
          }}
        >
          <PlusCircle className='mx-auto h-12 w-12 text-primary' />
        </motion.div>
        <h2 className='mt-4 text-2xl font-semibold'>
          Create Your First Website
        </h2>
        <p className='mt-2 text-muted-foreground'>
          Build your professional online presence in minutes.
        </p>
      </CardContent>
      <CardFooter className='justify-center'>
        <Button size='lg' onClick={onClick}>
          Get Started
        </Button>
      </CardFooter>
    </Card>
  )
}

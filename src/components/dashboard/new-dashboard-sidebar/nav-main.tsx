"use client"

import { Ch<PERSON>ronR<PERSON>, LucideIcon } from "lucide-react"
import Link from "next/link"
import { useParams, usePathname } from "next/navigation"
import { useCallback, useMemo } from "react"

import { Badge } from "@/components/ui/badge"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import { useUserStore } from "@/store/use-user-store"
import { Website } from "@/types/website"

interface NavItem {
  title: string
  url: string
  icon?: LucideIcon
  badge?: string
  items?: {
    title: string
    url: string
    icon?: LucideIcon
    badge?: string
  }[]
}

interface NavItemWithMetadata extends NavItem {
  href: string
  isActive: boolean
  items?: NavItemWithMetadata[]
}

export function NavMain({ items }: { items: NavItem[] }) {
  const { website } = useParams<{ website: string }>()
  const pathname = usePathname()

  // Stabilize functions with useCallback
  const generateHref = useCallback(
    (url: string) => `/dashboard/${website}${url}`,
    [website],
  )

  const isActive = useCallback(
    (url: string) => pathname === generateHref(url),
    [pathname, generateHref],
  )

  // Memoize the items to avoid recalculating on every render
  const navItems = useMemo(
    () =>
      items.map((item) => ({
        ...item,
        href: generateHref(item.url),
        isActive: isActive(item.url),
        items: item.items?.map((subItem) => ({
          ...subItem,
          href: generateHref(subItem.url),
          isActive: isActive(subItem.url),
        })),
      })),
    [items, generateHref, isActive],
  )

  return (
    <SidebarGroup className='px-2'>
      <SidebarGroupLabel className='px-2 text-sm font-semibold'>
        Dashboard
      </SidebarGroupLabel>
      <SidebarMenu className='space-y-1'>
        {navItems.map((item) => (
          <NavItemComponent key={item.title} item={item} />
        ))}
      </SidebarMenu>
    </SidebarGroup>
  )
}

interface NavItemComponentProps {
  item: NavItemWithMetadata
}

function NavItemComponent({ item }: NavItemComponentProps) {
  const { currentWebsite } = useUserStore()

  return (
    <SidebarMenuItem>
      {item.items && item.items.length > 0 ? (
        <Collapsible
          defaultOpen={
            item.isActive || item.items.some((subItem) => subItem.isActive)
          }
          className='group/collapsible'
        >
          <CollapsibleTrigger asChild>
            <SidebarMenuButton
              tooltip={item.title}
              className='w-full justify-between'
              data-active={item.isActive}
            >
              <NavItemContent item={item} currentWebsite={currentWebsite} />
              <ChevronRight className='h-4 w-4 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <SidebarMenuSub className='mt-1 space-y-1 py-2'>
              {item.items.map((subItem) => (
                <SidebarMenuSubItem key={subItem.title}>
                  <SidebarMenuSubButton
                    asChild
                    className='py-3 pl-8 pr-2'
                    data-active={subItem.isActive}
                  >
                    <Link href={subItem.href}>
                      <span>{subItem.title}</span>
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        </Collapsible>
      ) : (
        <SidebarMenuButton
          asChild
          tooltip={item.title}
          className='w-full justify-between'
          data-active={item.isActive}
        >
          <Link href={item.href}>
            <NavItemContent item={item} currentWebsite={currentWebsite} />
          </Link>
        </SidebarMenuButton>
      )}
    </SidebarMenuItem>
  )
}

interface NavItemContentProps {
  item: NavItemWithMetadata
  currentWebsite: Website | null
}

function NavItemContent({ item, currentWebsite }: NavItemContentProps) {
  return (
    <div className='flex items-center py-2'>
      {item.icon && <item.icon className='mr-4 h-4 w-4 shrink-0' />}
      <span>{item.title}</span>
      {item.badge && (
        <>
          {/* Show "Pro" badge only if the website is NOT Pro */}
          {item.badge.toLowerCase() === "pro" && !currentWebsite?.is_pro ? (
            <Badge className='ml-3 bg-gradient-to-tr from-yellow-400 to-yellow-500 text-xs font-bold text-yellow-900'>
              {item.badge}
            </Badge>
          ) : (
            /* Show other badges with purple gradient */
            item.badge.toLowerCase() !== "pro" && (
              <Badge className='ml-3 bg-gradient-to-tr from-purple-400 to-purple-600 text-xs font-bold text-white'>
                {item.badge}
              </Badge>
            )
          )}
        </>
      )}
    </div>
  )
}

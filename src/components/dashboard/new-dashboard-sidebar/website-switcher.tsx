"use client"

import { ChevronsUpDown, Plus } from "lucide-react"
import Image from "next/image"
import { usePara<PERSON>, useRouter } from "next/navigation"
import * as React from "react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"
import { Website } from "@/types/website"

interface TeamSwitcherProps {
  websites: Website[] | []
  websiteLoading: boolean
}

export default function WebsiteSwitcher({
  websites,
  websiteLoading,
}: TeamSwitcherProps) {
  const { isMobile } = useSidebar()
  const params = useParams()
  const router = useRouter()
  // const activeWebsite = websites?.find(
  //   (website: Website) => website?._id === params.websiteId
  // );
  //use usecallback to avoid rerendering
  const activeWebsite = React.useCallback(() => {
    return websites?.find((website: Website) => website?._id === params.website)
  }, [websites, params.website])()

  const handleActiveWebsite = (website: Website) => {
    router.push(`/dashboard/${website._id}`)
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        {websiteLoading ? (
          <div className='flex w-full items-center gap-1 p-2'>
            <Skeleton className='min-h-9 min-w-9 rounded-full' />
            <div className='flex flex-1 flex-col'>
              <Skeleton className='h-5 w-full' />
              <Skeleton className='mt-1 h-3 w-[60%]' />
            </div>
            {/* <Skeleton className="w-full h-9" /> */}
            <Skeleton className='min-h-9 min-w-4' />
          </div>
        ) : (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                size='lg'
                className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'
              >
                <div className='flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground'>
                  {/* <activeTeam.logo className="size-4" /> */}
                  <Image
                    src={
                      "https://utfs.io/f/qPlpyBmwd8UNH6DswT44WbcxIHPdTa18r3v7qelB5nkUwJSO"
                    }
                    alt='ThePortfolyo'
                    width={150}
                    height={40}
                    className='h-8 w-auto rounded-sm'
                  />
                </div>
                <div className='grid flex-1 text-left text-sm leading-tight md:text-base'>
                  <span className='truncate font-semibold'>
                    {activeWebsite?.name}
                  </span>
                  {activeWebsite?.is_pro ? (
                    <span className='text-xs text-accent-foreground text-yellow-400'>
                      Pro
                    </span>
                  ) : (
                    <span className='text-xs text-accent-foreground'>Free</span>
                  )}
                </div>
                <ChevronsUpDown className='ml-auto' />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className='w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg'
              align='start'
              side={isMobile ? "bottom" : "right"}
              sideOffset={4}
            >
              <DropdownMenuLabel className='text-xs text-muted-foreground'>
                Websites
              </DropdownMenuLabel>
              {websites?.map((website) => (
                <DropdownMenuItem
                  key={website.name}
                  onClick={() => handleActiveWebsite(website)}
                  className='gap-2 p-2'
                >
                  <div className='flex size-6 items-center justify-center rounded-sm border'>
                    {/* <team.logo className="size-4 shrink-0" /> */}
                  </div>
                  {website.name}
                  {/* <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut> */}
                  <DropdownMenuShortcut
                    className={cn(
                      website.is_pro
                        ? "text-yellow-300"
                        : "text-muted-foreground",
                    )}
                  >
                    {" "}
                    {website?.is_pro ? "Pro" : "Free"}
                  </DropdownMenuShortcut>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => {
                  router.push("/dashboard?createWebsite=true")
                }}
                className='gap-2 p-2'
              >
                <div className='flex size-6 items-center justify-center rounded-md border bg-background'>
                  <Plus className='size-4' />
                </div>
                <div className='font-medium text-muted-foreground'>
                  Add Website
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </SidebarMenuItem>
    </SidebarMenu>
  )
}

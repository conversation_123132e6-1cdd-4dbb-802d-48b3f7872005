"use client"

import { useQuery } from "@tanstack/react-query"
import * as React from "react"

import { getWebsites } from "@/actions/website"
import { NavMain } from "@/components/dashboard/new-dashboard-sidebar/nav-main"
import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import { Skeleton } from "@/components/ui/skeleton"
import { DASHBOARD_LINKS_NEW } from "@/constants/links"
import { useUserStore } from "@/store/use-user-store"
import { Website } from "@/types/website"

import { NavUser } from "./nav-user"
import WebsiteSwitcher from "./website-switcher"

function DashboardSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  // const [activeTeam, setActiveTeam] = React.useState(teams[0]);
  // const params = useParams();
  const { user, userLoading } = useUserStore()

  // const {
  //   data: user,
  //   isLoading: userLoading,
  //   isError: userError,
  // } = useQuery({
  //   queryKey: ["user"],
  //   queryFn: getLoggedInUser,
  // });

  const hasWebsites = !!user?.websites?.length

  const { data: websites, isLoading: websitesLoading } = useQuery<Website[]>({
    queryKey: ["websites"],
    queryFn: getWebsites,
    enabled: hasWebsites,
  })

  return (
    <Sidebar collapsible='icon' {...props}>
      <SidebarHeader>
        <WebsiteSwitcher
          websiteLoading={websitesLoading ?? true}
          websites={websites ?? []}
        />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={DASHBOARD_LINKS_NEW} />
        {/* <NavProjects projects={data.projects} /> */}
      </SidebarContent>
      <SidebarFooter>
        {userLoading ? (
          <Skeleton className='h-7 w-24 md:h-8 md:w-48' />
        ) : (
          user && <NavUser user={user} />
        )}
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

export default DashboardSidebar

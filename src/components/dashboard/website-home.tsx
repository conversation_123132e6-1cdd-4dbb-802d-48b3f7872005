"use client"

import Image from "next/image"
import { useParams, useRouter } from "next/navigation"

import { SectionCard } from "@/components/cards/section-card"
import { sections } from "@/constants/sections"
import { useUserStore } from "@/store/use-user-store"

export default function WebsiteHome() {
  const params = useParams<{ website: string }>()
  const router = useRouter()
  const { currentWebsite: website, websiteLoading } = useUserStore()

  if (!websiteLoading && !website) {
    router.push("/dashboard")
    return null
  }

  return (
    <section className='relative z-[1] px-4 py-3 pb-7 md:px-6 md:py-4 md:pb-10 2xl:px-8 2xl:py-6 2xl:pb-16'>
      {/* background gradients */}
      <div className='absolute right-0 top-[-266px] -z-10 hidden h-[620px] w-[379px] rounded-full bg-primary/60 opacity-20 blur-[10rem] dark:opacity-50 lg:flex'></div>

      <div className='absolute left-0 top-[-60px] -z-10 hidden h-[525px] w-[433px] rounded-full bg-primaryLight/40 opacity-20 blur-[15rem] dark:opacity-50 lg:flex'></div>

      <div className='fixed inset-0 -left-20 h-screen'>
        <Image
          src='/images/background-effect1.svg'
          alt='background'
          layout='fill'
          objectFit='cover'
          objectPosition='center'
          quality={100}
          className='z-[-1] opacity-20 dark:opacity-50'
        />
      </div>

      <div className='mx-auto grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-4 lg:grid-cols-3 2xl:grid-cols-4'>
        {sections.map((section, index) => (
          <SectionCard
            key={section.title}
            {...section}
            index={index}
            isPro={website?.is_pro || false}
            websiteId={params.website}
          />
        ))}
      </div>
    </section>
  )
}

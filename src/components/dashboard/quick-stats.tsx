import { Activity, Calendar, Globe } from "lucide-react"

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import type { Website } from "@/types/website"

export function QuickStats({ websites }: { websites: Website[] }) {
  const activeWebsites = websites?.filter((w: Website) => w.is_active).length
  const proWebsites = websites?.filter((w: Website) => w.is_pro).length

  return (
    <div className='mb-8 grid grid-cols-1 gap-6 md:grid-cols-3'>
      <StatCard title='Total Websites' value={websites?.length} icon={Globe} />
      <StatCard
        title='Active Websites'
        value={activeWebsites}
        icon={Activity}
      />
      <StatCard title='Pro Websites' value={proWebsites} icon={Calendar} />
    </div>
  )
}

interface StatCardProps {
  title: string
  value: number
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>
}

function StatCard({ title, value, icon: Icon }: StatCardProps) {
  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-base font-medium'>{title}</CardTitle>
        <Icon className='h-4 w-4 text-muted-foreground' />
      </CardHeader>
      <CardContent>
        <div className='text-2xl font-bold'>{value}</div>
      </CardContent>
    </Card>
  )
}

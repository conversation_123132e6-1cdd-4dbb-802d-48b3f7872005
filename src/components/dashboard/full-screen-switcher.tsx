"use client"

import { motion } from "framer-motion"
import { Maximize, Minimize } from "lucide-react"
import { useState } from "react"

import { Button } from "@/components/ui/button"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

export default function FullScreenSwitcher() {
  const [isFullScreen, setIsFullScreen] = useState(false)

  const toggleFullScreen = () => {
    if (!isFullScreen) {
      // Enter full screen
      document.documentElement.requestFullscreen?.()
      setIsFullScreen(true)
    } else {
      // Exit full screen
      document.exitFullscreen?.()
      setIsFullScreen(false)
    }
  }

  return (
    <TooltipProvider>
      <motion.div
        className='hidden h-full items-center md:flex'
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
      >
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size={"iconlg"}
              variant={"tertiary"}
              // variant={"ghost"}
              onClick={toggleFullScreen}
              className='p-3'
            >
              {isFullScreen ? (
                <Minimize className='h-4 w-4' />
              ) : (
                <Maximize className='h-4 w-4' />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {isFullScreen ? "Exit Full Screen" : "Enter Full Screen"}
          </TooltipContent>
        </Tooltip>
      </motion.div>
    </TooltipProvider>
  )
}

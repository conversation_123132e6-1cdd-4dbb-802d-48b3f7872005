"use client"

import { useQuery } from "@tanstack/react-query"
import { Crown, ExternalLink, HelpCircleIcon, ZapIcon } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import { getWebsiteById, publishWebsite } from "@/actions/website"
import { useUserStore } from "@/store/use-user-store"

import { TooltipButton } from "../custom/tooltip-button"
import Container from "../global/container"
import { ThemeSwitcher } from "../theme/theme-switcher"
import { SidebarTrigger } from "../ui/sidebar"
import FullScreenSwitcher from "./full-screen-switcher"

// const allowedRoles = ["admin", "app-admin"] as const

const DashboardNavbar = () => {
  const params = useParams<{ website: string }>()
  const router = useRouter()
  const { user, setCurrentWebsite } = useUserStore()

  const {
    data: website,
    isLoading: websiteLoading,
    // isError: websiteError,
  } = useQuery({
    queryKey: ["website", params.website],
    queryFn: async () => {
      const data = await getWebsiteById(params.website)
      setCurrentWebsite(data)
      return data
    },
    enabled: params.website !== "",
  })

  const allowedRoles = ["admin", "app-admin"]

  if (
    !user ||
    (!allowedRoles.includes(user.role) &&
      !user.websites.includes(params.website ?? website._id))
  ) {
    router.push("/dashboard")
  }

  const handlePublish = async () => {
    if (!website) return
    const toastId = toast.loading("Publishing...")

    try {
      await publishWebsite(website._id)
      toast.success("Website published successfully", {
        id: toastId,
      })
      // Handle successful publish (e.g., show a success message)
    } catch (error) {
      console.error("Failed to publish website", error)
      toast.error("Failed to publish website", {
        id: toastId,
      })
    } finally {
      toast.dismiss(toastId)
    }
  }

  return (
    <header
      id='dashboard-navbar'
      className='sticky inset-x-0 top-0 z-50 h-16 w-full border-b border-border/50 bg-background px-4 backdrop-blur-md'
    >
      <Container className='flex size-full items-center justify-between'>
        <div className='flex w-max items-center justify-start gap-2 md:gap-4'>
          <TooltipButton
            tooltipContent='Go to homepage'
            variant='tertiary'
            size='iconlg'
            asChild
          >
            <Link href='/'>
              <Image
                src='https://utfs.io/f/qPlpyBmwd8UNH6DswT44WbcxIHPdTa18r3v7qelB5nkUwJSO'
                alt='ThePortfolyo Logo'
                width={50}
                height={50}
                className='h-8 w-auto rounded-sm'
              />
            </Link>
          </TooltipButton>
          <TooltipButton
            tooltipContent='Toggle sidebar'
            variant='tertiary'
            size='iconlg'
            className='hidden md:flex'
          >
            <SidebarTrigger />
          </TooltipButton>
          <TooltipButton
            tooltipContent='Toggle full screen'
            variant='tertiary'
            size='iconlg'
          >
            <FullScreenSwitcher />
          </TooltipButton>
          <TooltipButton
            tooltipContent='Open website in new tab'
            disabled={websiteLoading || !website?.name}
            size='iconlg'
            variant='secondary'
            asChild
          >
            <Link
              href={website ? `https://${website?.name}.theportfolyo.com` : "#"}
              target='_blank'
            >
              <ExternalLink className='h-4 w-4' />
            </Link>
          </TooltipButton>
        </div>

        <div>
          <TooltipButton
            tooltipContent='Only click on publish when you have updated anything'
            disabled={websiteLoading || website?.is_published}
            size='sm'
            variant='default'
            onClick={handlePublish}
            // className="animate-bounce"
          >
            Publish
          </TooltipButton>
        </div>

        <div className='flex items-center gap-x-2'>
          {!website?.is_pro ? (
            <TooltipButton
              tooltipContent='Upgrade to PRO'
              size='sm'
              variant='ghost'
              asChild
            >
              <Link href={`/upgrade/${website?._id}`}>
                <ZapIcon className='mr-1.5 size-4 fill-orange-500 text-orange-500' />
                Upgrade
              </Link>
            </TooltipButton>
          ) : (
            <TooltipButton
              tooltipContent='You are a PRO user'
              size='sm'
              variant='tertiary'
              className='text-yellow-600 dark:border-yellow-500 dark:text-yellow-400'
            >
              <Crown className='mr-1.5 size-4 fill-yellow-500 text-yellow-500' />
              PRO
            </TooltipButton>
          )}
          {/* theme switch */}
          <ThemeSwitcher />

          <TooltipButton
            tooltipContent='Open help center'
            size='icon'
            variant='ghost'
            className='hidden lg:flex'
            asChild
          >
            <Link href='/help-center' target='_blank'>
              <HelpCircleIcon className='size-5' />
            </Link>
          </TooltipButton>

          {/* <NavUser user={user} /> */}

          <TooltipButton
            tooltipContent='Go to dashboard'
            size='sm'
            variant='white'
            asChild
          >
            <Link href='/dashboard'>Dashboard</Link>
          </TooltipButton>

          <TooltipButton
            tooltipContent='Toggle sidebar'
            variant='tertiary'
            size='iconlg'
            className='flex md:hidden'
          >
            <SidebarTrigger />
          </TooltipButton>
        </div>
      </Container>
    </header>
  )
}

export default DashboardNavbar

import { cn } from "@/lib/utils"

export function CircularGitHubIcon({ className }: { className: string }) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 24 24'
      fill='currentColor'
      className={cn("h-6 w-6", className)}
    >
      <path d='M12 2C6.477 2 2 6.484 2 12.014c0 4.418 2.865 8.165 6.839 9.491.5.092.682-.217.682-.483 0-.237-.009-.868-.014-1.703-2.782.605-3.37-1.343-3.37-1.343-.454-1.155-1.11-1.462-1.11-1.462-.907-.62.069-.607.069-.607 1.004.07 1.533 1.034 1.533 1.034.892 1.529 2.341 1.088 2.91.832.092-.648.35-1.089.635-1.34-2.22-.254-4.555-1.113-4.555-4.95 0-1.093.39-1.987 1.03-2.686-.103-.253-.447-1.27.098-2.646 0 0 .841-.27 2.75 1.026A9.563 9.563 0 0 1 12 6.845a9.56 9.56 0 0 1 2.506.336c1.909-1.296 2.75-1.026 2.75-1.026.546 1.375.202 2.393.1 2.646.64.7 1.03 1.593 1.03 2.686 0 3.847-2.338 4.692-4.565 4.943.36.31.679.923.679 1.86 0 1.344-.012 2.428-.012 2.757 0 .268.18.58.688.482C19.135 20.178 22 16.43 22 12.014 22 6.484 17.523 2 12 2z' />
    </svg>
  )
}

export default CircularGitHubIcon

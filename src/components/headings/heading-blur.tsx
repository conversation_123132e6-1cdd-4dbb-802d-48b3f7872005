import React from "react"

import { BlurText } from "../ui/blur-text"

type HeadingBlurProps = {
  word: string
  className?: string
}

const HeadingBlur: React.FC<HeadingBlurProps> = ({ word, className }) => {
  return (
    <BlurText
      word={word}
      className={`racking-[-0.0125em] mt-6 bg-clip-text py-2 font-heading text-4xl font-medium text-black dark:text-white sm:text-5xl md:py-0 lg:text-6xl lg:!leading-snug xl:text-7xl ${className}`}
    />
  )
}

export default HeadingBlur

import React, { ReactNode } from "react"

import { cn } from "@/functions"

interface HeadingProps {
  children: ReactNode
  className?: string
}

const Heading: React.FC<HeadingProps> = ({ children, className }) => {
  return (
    <h2
      className={cn(
        "bg-gradient-to-tr from-foreground/80 via-foreground to-foreground/80 bg-clip-text text-center font-heading text-2xl font-medium !leading-snug text-transparent dark:from-zinc-400/50 dark:via-white dark:to-white/60 md:text-4xl lg:text-5xl",
        className,
      )}
    >
      {children}
    </h2>
  )
}

export default Heading

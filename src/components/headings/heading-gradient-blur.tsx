import React from "react"

import { BlurText } from "../ui/blur-text"

type HeadingGradientBlurProps = {
  word: string
  className?: string
}

const HeadingGradientBlur: React.FC<HeadingGradientBlurProps> = ({
  word,
  className,
}) => {
  return (
    <BlurText
      word={word}
      className={`racking-[-0.0125em] mt-6 bg-gradient-to-br from-foreground to-foreground/90 bg-clip-text py-2 font-heading text-3xl font-medium !leading-tight text-transparent dark:from-foreground dark:to-foreground/60 sm:text-5xl md:py-0 lg:text-6xl xl:text-7xl ${className}`}
    />
  )
}

export default HeadingGradientBlur

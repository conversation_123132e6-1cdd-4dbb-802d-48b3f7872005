"use client"

import {
  CalendarRangeIcon,
  ContactIcon,
  GroupIcon,
  HashIcon,
  HelpCircle,
  Newspaper,
  UserCircleIcon,
  UsersIcon,
  VideoIcon,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import React from "react"

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"

interface MenuItemProps {
  title: string
  href: string
  icon: React.ReactNode
  description: string
}

const MenuItem: React.FC<MenuItemProps> = ({
  title,
  href,
  icon,
  description,
}) => (
  <li>
    <NavigationMenuLink asChild>
      <Link
        href={href}
        className='group grid select-none grid-cols-[.15fr_1fr] space-y-1 rounded-lg p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/50 hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground'
      >
        <div className='mt-1 flex h-8 w-8 items-center justify-center rounded-md border border-border/80 p-1'>
          {icon}
        </div>
        <div className='ml-3 text-start'>
          <span className='text-sm font-normal leading-none group-hover:text-foreground'>
            {title}
          </span>
          <p className='mt-0.5 line-clamp-2 text-sm text-muted-foreground'>
            {description}
          </p>
        </div>
      </Link>
    </NavigationMenuLink>
  </li>
)

const Menu = () => {
  const company = [
    {
      title: "Careers",
      href: "/careers",
      icon: <UsersIcon className='h-5 w-5' />,
      description: "Explore all the available job opportunities.",
    },
    {
      title: "About",
      href: "/about-us",
      icon: <UserCircleIcon className='h-5 w-5' />,
      description: "Learn more about our company and team.",
    },
    {
      title: "Contact",
      href: "/contact-us",
      icon: <ContactIcon className='h-5 w-5' />,
      description: "Get in touch with us for any queries.",
    },
    {
      title: "Feedback",
      href: "/feedback",
      icon: <ContactIcon className='h-5 w-5' />,
      description: "Give us your feedback or report a bug.",
    },
  ]

  const resources = [
    {
      title: "Blog",
      href: "/blog",
      icon: <Newspaper className='h-5 w-5' />,
      description: "Get useful tips on how to build your portfolio.",
    },
    {
      title: "Community",
      href: "/community",
      icon: <GroupIcon className='h-5 w-5' />,
      description:
        "Become a part of our community and get help from other users.",
    },
    {
      title: "Videos",
      href: "/videos",
      icon: <VideoIcon className='h-5 w-5' />,
      description: "Acquire knowledge from our video tutorials.",
    },
    {
      title: "Help Center",
      href: "/help-center",
      icon: <HelpCircle className='h-5 w-5' />,
      description: "Get help with any issues you may have.",
    },
  ]

  return (
    <NavigationMenu>
      <NavigationMenuList>
        {/* Products */}
        <NavigationMenuItem>
          <NavigationMenuTrigger className='text-foreground hover:text-foreground'>
            Products
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className='grid gap-3 rounded-3xl p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr] xl:w-[550px]'>
              <li className='h-max w-full'>
                <NavigationMenuLink asChild>
                  <Link
                    href='/'
                    className='flex h-max w-max select-none flex-col justify-center overflow-hidden rounded-lg p-0 no-underline outline-none focus:shadow-md'
                  >
                    <Image
                      src='https://utfs.io/f/qPlpyBmwd8UNH6DswT44WbcxIHPdTa18r3v7qelB5nkUwJSO'
                      alt='ThePortfolyo'
                      width={200}
                      height={200}
                      className='z-0 rounded-lg object-contain'
                    />
                  </Link>
                </NavigationMenuLink>
              </li>

              <div className='w-full'>
                <MenuItem
                  title='Portfolio Themes'
                  href='/portfolio-themes'
                  icon={<CalendarRangeIcon className='h-5 w-5' />}
                  description='Explore all the available Portfolio Themes'
                />
                <MenuItem
                  title='Our Plugins'
                  href='/our-plugins'
                  icon={<HashIcon className='h-5 w-5' />}
                  description='Explore all the available Plugins'
                />
                <MenuItem
                  title='Tools'
                  href='/tools'
                  icon={<UsersIcon className='h-5 w-5' />}
                  description='Explore all the available Tools'
                />
              </div>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>

        {/* Company */}
        <NavigationMenuItem>
          <NavigationMenuTrigger className='text-foreground hover:text-foreground'>
            Company
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className='grid w-[400px] gap-3 p-4 md:w-[400px] md:grid-cols-2 lg:w-[500px] xl:w-[500px]'>
              {company.map((item, index) => (
                <MenuItem key={index} {...item} />
              ))}
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>

        {/* Resources */}
        <NavigationMenuItem>
          <NavigationMenuTrigger className='text-foreground hover:text-foreground'>
            Resources
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className='grid w-[400px] gap-3 p-4 md:w-[400px] md:grid-cols-2 lg:w-[500px] xl:w-[500px]'>
              {resources.map((item, index) => (
                <MenuItem key={index} {...item} />
              ))}
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>

        {/* Pricing */}
        <NavigationMenuItem>
          <Link href='/pricing' legacyBehavior passHref>
            <NavigationMenuLink className='h-10 w-max rounded-md px-4 py-2 text-sm font-normal text-foreground hover:bg-none hover:text-foreground'>
              Pricing
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  )
}

export default Menu

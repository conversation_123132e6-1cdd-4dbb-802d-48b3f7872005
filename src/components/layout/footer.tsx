"use client"

import { InstagramIcon, LinkedinIcon } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { Button } from "../ui/button"
import { TextHoverEffect } from "../ui/text-hover-effect"

type FooterLink = {
  name: string
  link: string
}
const FooterLinks = [
  // { title: "Company", links: ["Home", "FAQ's", "Components"] },
  {
    title: "Company",
    links: [
      {
        name: "About",
        link: "/about-us",
      },
      {
        name: "Contact",
        link: "/contact-us",
      },
      {
        name: "Feedback",
        link: "/feedback",
      },
      {
        name: "Careers",
        link: "/careers",
      },
    ],
  },
  {
    title: "Resources",
    links: [
      { name: "Blog", link: "/blog" },
      { name: "FAQ's", link: "/faq" },
      { name: "Components", link: "/components" },
    ],
  },
  {
    title: "Products",
    links: [
      { name: "Portfolio Themes", link: "/portfolio-themes" },
      { name: "Our Plugins", link: "/our-plugins" },
      { name: "Tools", link: "/tools" },
    ],
  },
  {
    title: "Help",
    links: [
      { name: "Terms & Conditions", link: "/terms-and-conditions" },
      { name: "Privacy Policy", link: "/privacy-policy" },
      {
        name: "Cancellation & Refunds",
        link: "/cancellation-and-refunds",
      },
    ],
  },
]

const FooterLink = ({ name, link }: { name: string; link: string }) => (
  <li>
    <Link
      href={link}
      className='text-sm text-white/80 transition-colors duration-300 hover:text-white hover:underline dark:hover:text-white md:text-base'
    >
      {name}
    </Link>
  </li>
)

const Footer = () => {
  const currentYear = new Date().getFullYear()

  return (
    <footer className='relative mt-20 border-t border-secondary bg-black sm:pt-16 lg:pt-24'>
      {/* Background effect */}
      <div className='absolute inset-0 h-full w-screen bg-black dark:block'>
        <Image
          src='https://utfs.io/f/qPlpyBmwd8UNX1Z4UT9NhvnCGoBPYFEWVq14kTrXi0MmAgtS'
          className='h-full w-full'
          width={1000}
          height={300}
          alt='background'
        />
      </div>

      <div className='absolute inset-0 h-full w-full bg-gradient-to-r from-black/35 to-black/50 px-5 py-24' />

      <div className='relative z-0 mx-auto max-w-7xl px-5 sm:px-6 lg:px-8'>
        <div className='grid grid-cols-2 gap-x-8 gap-y-12 md:grid-cols-4 lg:grid-cols-12 xl:gap-x-12'>
          {/* Logo and Description */}
          <div className='col-span-2 md:col-span-4 xl:pr-8'>
            <Image
              className='rounded-lg py-3'
              width={200}
              height={150}
              src='https://utfs.io/f/qPlpyBmwd8UNtXK3S906EUoAQbpwj1vLGnkYxNIMWuZyq4sF'
              alt='ThePortfolyo Logo'
            />
            <p className='mt-2 text-pretty text-base leading-relaxed text-white/80'>
              Effortlessly create stunning portfolio websites with just a few
              clicks. Whether you&apos;re a student, professional, or creative,
              ThePortfolyo makes showcasing your work as easy as posting on
              social media—no coding required!
            </p>
            <Button
              className='mt-5 gap-1 rounded-lg font-bold'
              size='lg'
              asChild
            >
              <Link href='/contact-us'>
                <svg
                  className='h-6 w-6'
                  xmlns='http://www.w3.org/2000/svg'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
                  />
                </svg>
                Contact
              </Link>
            </Button>
          </div>

          {/* Links Sections */}
          {FooterLinks.map(({ title, links }, idx) => (
            <div key={idx} className='lg:col-span-2'>
              <p className='font-heading text-lg font-semibold text-primary-foreground underline md:text-xl'>
                {title}
              </p>
              <ul className='mt-6 space-y-5'>
                {links.map((link, i) =>
                  typeof link === "string" ? (
                    <FooterLink key={i} name={link} link={`/${link}`} />
                  ) : (
                    <FooterLink key={i} name={link.name} link={link.link} />
                  ),
                )}
              </ul>
            </div>
          ))}
        </div>

        {/* <hr className='mb-10 mt-16 block bg-neutral-800 dark:bg-primary/55' /> */}

        {/* Footer Bottom */}
        <div className='mt-16 border-t border-primary/40 pb-16 pt-10 sm:flex sm:items-center sm:justify-between md:pb-0'>
          <p className='flex flex-col gap-2 text-center text-sm text-white/80 sm:flex-row md:text-start'>
            © Copyright {currentYear}, All Rights Reserved by{" "}
            <Link href='/' className='text-primary-foreground underline'>
              ThePortfolyo
            </Link>
          </p>
          <ul className='mt-5 flex items-center space-x-3 sm:mt-0'>
            {[
              {
                title: "Instagram",
                icon: <InstagramIcon className='w-4' />,
                url: "https://www.instagram.com/theportfolyo.official/",
              },
              {
                title: "LinkedIn",
                icon: <LinkedinIcon className='w-4' />,
                url: "https://www.linkedin.com/company/theportfolyo/",
              },
            ].map((social, i) => (
              <li key={i}>
                <a
                  href={social.url}
                  title={social.title}
                  className='flex h-7 w-7 items-center justify-center rounded-full border border-primary bg-primary text-white'
                >
                  {social.icon}
                </a>
              </li>
            ))}
          </ul>
        </div>
        <div className='relative z-0 hidden h-[20rem] w-full md:flex lg:h-[20rem]'>
          <TextHoverEffect text='THEPORTFOLYO' />
        </div>
      </div>
    </footer>
  )
}

export default Footer

"use client"

import { motion } from "framer-motion"
import { ArrowRightIcon, LayoutDashboardIcon, XIcon } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useEffect, useState } from "react"

import { cn } from "@/functions"
import { useUserStore } from "@/store/use-user-store"

import Icons from "../global/icons"
import Wrapper from "../global/wrapper"
import { ThemeSwitcher } from "../theme/theme-switcher"
import { Button } from "../ui/button"
import { Skeleton } from "../ui/skeleton"
import MobileMenu from "./mobile-menu"
import Menu from "./navbar-menu"
import { NavUser } from "./navbar-user"

export const excludesUrls = ["/templates", "/dashboard", "/auth"]
export const includesUrls = [
  "/",
  "/pricing",
  "/contact-us",
  "/about-us",
  "/blog",
]

const Navbar = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const { user, userLoading, fetchUser } = useUserStore()

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = ""
    }

    return () => {
      document.body.style.overflow = ""
    }
  }, [isOpen])

  //setup user and all
  useEffect(() => {
    fetchUser()
  }, [fetchUser])

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={cn("relative h-full w-full")}
    >
      {/* <div className='pointer-events-none fixed inset-x-0 z-[99] h-[88px] backdrop-blur-sm [mask:linear-gradient(to_bottom,#000_20%,transparent_calc(100%-20%))] dark:bg-[rgba(10,10,10,0.8)]'></div> */}

      <header
        className={cn(
          "th fixed inset-x-0 top-4 z-[100] mx-auto max-w-7xl transform px-2 md:px-12",
          isOpen ? "h-[calc(100%-24px)]" : "h-12",
        )}
      >
        <Wrapper className='px- flex items-center justify-start rounded-xl border border-[rgba(124,124,124,0.2)] backdrop-blur-lg md:px-2 lg:rounded-2xl'>
          <div className='sticky inset-x-0 mb-auto mt-[7px] flex w-full items-center justify-between lg:mt-auto'>
            <div className='flex flex-1 items-center pl-1 lg:flex-none'>
              <Link
                href='/'
                className='flex items-center gap-2 text-lg font-semibold text-foreground'
              >
                <Image
                  src={
                    "https://utfs.io/f/qPlpyBmwd8UNH6DswT44WbcxIHPdTa18r3v7qelB5nkUwJSO"
                  }
                  alt='ThePortfolyo'
                  width={50}
                  height={40}
                  className='h-8 w-auto rounded-sm'
                />
                <span className='text-lg font-semibold'>
                  {process.env.NEXT_PUBLIC_APP_NAME}
                </span>
              </Link>
            </div>

            <div className='ml-4 hidden items-center lg:flex'>
              <Menu />
            </div>

            <div className='flex items-center gap-2 lg:gap-3'>
              <ThemeSwitcher />

              {userLoading ? (
                <>
                  <Skeleton className='rounded-full md:h-8 md:w-8' />
                  <Skeleton className='h-7 w-24 md:h-8 md:w-28' />
                </>
              ) : user ? (
                <>
                  <NavUser />
                  <Button
                    size='sm'
                    variant='default'
                    asChild
                    className='hidden sm:flex'
                  >
                    <Link href='/dashboard'>
                      <LayoutDashboardIcon className='mr-1 h-4 w-4' />
                      Dashboard
                    </Link>
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    size='sm'
                    variant='white'
                    asChild
                    className='hover:translate-y-0 hover:scale-100'
                  >
                    <Link href='/auth/login'>Login</Link>
                  </Button>
                  <Button
                    size='sm'
                    variant='default'
                    asChild
                    className='hidden sm:flex'
                  >
                    <Link href='/auth/register'>
                      Start Free
                      <ArrowRightIcon className='ml-1 hidden w-3 lg:block' />
                    </Link>
                  </Button>
                </>
              )}

              <Button
                size='icon'
                variant='ghost'
                onClick={() => setIsOpen((prev) => !prev)}
                className='h-8 w-8 p-2 lg:hidden'
              >
                {isOpen ? (
                  <XIcon className='h-4 w-4 duration-300' />
                ) : (
                  <Icons.menu className='h-3.5 w-3.5 duration-300' />
                )}
              </Button>
            </div>
          </div>
          <MobileMenu isOpen={isOpen} setIsOpen={setIsOpen} />
        </Wrapper>
      </header>
    </motion.div>
  )
}

export default Navbar

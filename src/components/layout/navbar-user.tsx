"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CreditCard, LogOut, <PERSON>rk<PERSON> } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

import { logoutUser } from "@/actions/auth"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useUserStore } from "@/store/use-user-store"

export const NavUser = () => {
  const router = useRouter()
  const { user } = useUserStore()
  const handleLogout = async (): Promise<any> => {
    try {
      const res = await logoutUser()
      if (res.success) {
        router.refresh()
      }
    } catch (error) {
      toast.error("Failed to logout")
      console.error(error)
      return error
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className='h-8 w-8 rounded-full'>
          <AvatarImage
            src={user?.profile_picture}
            alt='profile'
            className='object-cover'
          />
          <AvatarFallback>
            {user?.full_name ? user?.full_name.charAt(0).toUpperCase() : "?"}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className='w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg'
        // side={isMobile ? "bottom" : "right"}
        align='end'
        sideOffset={4}
      >
        <DropdownMenuLabel className='p-0 font-normal'>
          <div className='flex items-center gap-2 px-1 py-1.5 text-left text-sm'>
            <Avatar className='h-8 w-8 rounded-full'>
              <AvatarImage src={user?.profile_picture} alt={user?.full_name} />
              <AvatarFallback>
                {/* Display the first letter of the full_name */}
                {user?.full_name
                  ? user?.full_name.charAt(0).toUpperCase()
                  : "?"}
              </AvatarFallback>
            </Avatar>
            <div className='grid flex-1 text-left text-sm leading-tight'>
              <span className='truncate font-semibold'>{user?.full_name}</span>
              <span className='truncate text-xs'>{user?.email}</span>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href='/dashboard'>
              <Sparkles className='mr-2 h-4 w-4' />
              Dashboard
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem>
            <BadgeCheck className='mr-2 h-4 w-4' />
            Account
          </DropdownMenuItem>
          <DropdownMenuItem>
            <CreditCard className='mr-2 h-4 w-4' />
            Billing
          </DropdownMenuItem>
          {/* <DropdownMenuItem>
            <Bell className="w-4 h-4 mr-2" />
            Notifications
          </DropdownMenuItem> */}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className='mr-2 h-4 w-4' />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

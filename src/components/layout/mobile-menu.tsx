"use client"

import { motion } from "framer-motion"
import {
  CalendarClock,
  Captions,
  ContactIcon,
  CopyCheck,
  Gem,
  GroupIcon,
  HelpCircle,
  LineChart,
  Newspaper,
  UserCircleIcon,
  UserCog,
  UsersIcon,
  VideoIcon,
} from "lucide-react"
import Link from "next/link"
import React from "react"

import {
  <PERSON>ccordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { cn } from "@/functions"
import { useClickOutside } from "@/hooks"

interface Props {
  isOpen: boolean
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>
}

const MobileMenu = ({ isOpen, setIsOpen }: Props) => {
  const ref = useClickOutside(() => setIsOpen(false))

  const variants = {
    open: { opacity: 1, y: 20 },
    closed: { opacity: 0, y: 0 },
  }

  return (
    <div
      ref={ref}
      className={cn(
        "absolute inset-x-0 top-12 z-20 flex size-full flex-1 bg-inherit p-4",
        isOpen ? "flex" : "hidden",
      )}
    >
      <motion.div
        initial='closed'
        animate={isOpen ? "open" : "closed"}
        variants={variants}
        transition={{
          type: "spring",
          bounce: 0.15,
          duration: 0.5,
        }}
        className='flex size-full flex-col justify-start'
      >
        <ul className='flex w-full flex-1 flex-col items-start space-y-3'>
          <li
            onClick={() => setIsOpen(false)}
            className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'
          >
            <Link href='/' className='flex w-full items-center text-start'>
              <UserCog className='mr-2 h-4 w-4' />
              Home
            </Link>
          </li>
          <Accordion type='single' collapsible className='w-full'>
            <AccordionItem value='products' className='border-transparent'>
              <AccordionTrigger className='px-4 py-2 text-lg font-normal hover:text-muted-foreground'>
                <span className='flex items-center'>
                  <CopyCheck className='mr-2 h-4 w-4' />
                  Products
                </span>
              </AccordionTrigger>
              <AccordionContent
                onClick={() => setIsOpen(false)}
                className='mt-1 flex flex-col items-start gap-1'
              >
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/portfolio-themes'
                    className='flex w-full items-center text-start'
                  >
                    <Captions className='mr-2 h-4 w-4' />
                    Portfolio Themes{" "}
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/our-plugins'
                    className='flex w-full items-center text-start'
                  >
                    <CalendarClock className='mr-2 h-4 w-4' />
                    Our Plugins{" "}
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/tools'
                    className='flex w-full items-center text-start'
                  >
                    <LineChart className='mr-2 h-4 w-4' />
                    Tools {/* <span></span> */}
                  </Link>
                </li>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value='company' className='border-transparent'>
              <AccordionTrigger className='px-4 py-2 text-lg font-normal hover:text-muted-foreground'>
                <span className='flex items-center'>
                  <CopyCheck className='mr-2 h-4 w-4' />
                  Company
                </span>
              </AccordionTrigger>
              <AccordionContent
                onClick={() => setIsOpen(false)}
                className='mt-1 flex flex-col items-start gap-1'
              >
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/careers'
                    className='flex w-full items-center text-start'
                  >
                    <UserCircleIcon className='mr-2 h-4 w-4' />
                    Careers
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/about-us'
                    className='flex w-full items-center text-start'
                  >
                    <UsersIcon className='mr-2 h-4 w-4' />
                    About us
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/contact-us'
                    className='flex w-full items-center text-start'
                  >
                    <ContactIcon className='mr-2 h-4 w-4' />
                    Contact us
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/feedback'
                    className='flex w-full items-center text-start'
                  >
                    <ContactIcon className='mr-2 h-4 w-4' />
                    Feedback
                  </Link>
                </li>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value='resources' className='border-transparent'>
              <AccordionTrigger className='px-4 py-2 text-lg font-normal hover:text-muted-foreground'>
                <span className='flex items-center'>
                  <CopyCheck className='mr-2 h-4 w-4' />
                  Resources
                </span>
              </AccordionTrigger>
              <AccordionContent
                onClick={() => setIsOpen(false)}
                className='mt-1 flex flex-col items-start gap-1'
              >
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/blog'
                    className='flex w-full items-center text-start'
                  >
                    <Newspaper className='mr-2 h-4 w-4' />
                    Blog
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/community'
                    className='flex w-full items-center text-start'
                  >
                    <GroupIcon className='mr-2 h-4 w-4' />
                    Community
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/videos'
                    className='flex w-full items-center text-start'
                  >
                    <VideoIcon className='mr-2 h-4 w-4' />
                    Videos
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/help-center'
                    className='flex w-full items-center text-start'
                  >
                    <HelpCircle className='mr-2 h-4 w-4' />
                    Help Center
                  </Link>
                </li>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          <li
            onClick={() => setIsOpen(false)}
            className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'
          >
            <Link
              href='/pricing'
              className='flex w-full items-center text-start'
            >
              <Gem className='mr-2 h-4 w-4' />
              Pricing
            </Link>
          </li>
          {/* <li
            onClick={() => setIsOpen(false)}
            className="w-full px-4 py-2 text-lg hover:text-muted-foreground font-normal transition transform rounded-md cursor-pointer text-foreground text-start active:scale-95 hover:bg-muted/20 active:opacity-80"
          >
            <Link href="/" className="flex items-center w-full text-start">
              <Waypoints className="w-4 h-4 mr-2" />
              Integrations
            </Link>
          </li>
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="item-1" className="border-transparent">
              <AccordionTrigger className="px-4 py-2 text-lg hover:text-muted-foreground font-normal">
                <span className="flex items-center">
                  <Layers3 className="w-4 h-4 mr-2" />
                  Resources
                </span>
              </AccordionTrigger>
              <AccordionContent
                onClick={() => setIsOpen(false)}
                className="flex flex-col items-start gap-1 mt-1"
              >
                <li className="w-full px-4 py-2 text-lg font-normal transition transform rounded-md cursor-pointer text-foreground/80 hover:text-muted-foreground text-start active:scale-95 hover:bg-muted/20 active:opacity-80">
                  <Link
                    href="/"
                    className="flex items-center w-full text-start"
                  >
                    <Newspaper className="w-4 h-4 mr-2" />
                    Blog
                  </Link>
                </li>
                <li className="w-full px-4 py-2 text-lg font-normal transition transform rounded-md cursor-pointer text-foreground/80 hover:text-muted-foreground text-start active:scale-95 hover:bg-muted/20 active:opacity-80">
                  <Link
                    href="/"
                    className="flex items-center w-full text-start"
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Case Studies
                  </Link>
                </li>
                <li className="w-full px-4 py-2 text-lg font-normal transition transform rounded-md cursor-pointer text-foreground/80 hover:text-muted-foreground text-start active:scale-95 hover:bg-muted/20 active:opacity-80">
                  <Link
                    href="/"
                    className="flex items-center w-full text-start"
                  >
                    <Box className="w-4 h-4 mr-2" />
                    Tools
                  </Link>
                </li>
                <li className="w-full px-4 py-2 text-lg font-normal transition transform rounded-md cursor-pointer text-foreground/80 hover:text-muted-foreground text-start active:scale-95 hover:bg-muted/20 active:opacity-80">
                  <Link
                    href="/"
                    className="flex items-center w-full text-start"
                  >
                    <CircleHelp className="w-4 h-4 mr-2" />
                    Support
                  </Link>
                </li>
              </AccordionContent>
            </AccordionItem>
          </Accordion> */}
        </ul>
      </motion.div>
    </div>
  )
}

export default MobileMenu

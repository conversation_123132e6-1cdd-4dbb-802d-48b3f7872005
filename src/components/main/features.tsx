"use client"

import Image from "next/image"

import Container from "../global/container"
import Images from "../global/images"
import Heading from "../headings/heading"
import { But<PERSON> } from "../ui/button"
import MagicCard from "../ui/magic-card"
import { Ripple } from "../ui/ripple"
import { SectionBadge } from "../ui/section-bade"

const imageToPortfolio = [
  "https://utfs.io/f/qPlpyBmwd8UNaXVDyo92upBJPkoQv2xCXthLKRl0N4EsjZgq",
  "https://utfs.io/f/qPlpyBmwd8UNMc6EBattp0rg2ilXD8WcAzGUyCLhBwJdoKqu",
  "https://utfs.io/f/qPlpyBmwd8UNrRw6i2lKES1oY4fskA9elWgbC0GJDVPNyM3T",
  "https://utfs.io/f/qPlpyBmwd8UN3WuJYLdlZbR5HzhUOVGdr8eQSpimKEJjtF9T",
  "https://utfs.io/f/qPlpyBmwd8UN0jNAbPHjiT1qY2szFub9aEHfI6BdlnMVcQGe",
  "https://utfs.io/f/qPlpyBmwd8UNOthqLS7iTJ6GWORrCSof7aYNx9kZcIPpb5qh",
  "https://utfs.io/f/qPlpyBmwd8UNFfF096U3BwG9nZoYAtPLIpzgyhube2jJvEHl",
]

// Reusable FeatureCard component
const FeatureCard = ({
  imageSrc,
  altText,
  title,
  description,
  className = "",
}: {
  imageSrc: string
  altText: string
  title: string
  description: string
  className?: string
}) => {
  return (
    <MagicCard
      particles={true}
      className={`flex w-full flex-col items-start bg-card ${className}`}
    >
      <div className='bento-card w-full flex-row gap-6'>
        <div className='relative h-52 w-full md:h-64'>
          <Image
            src={imageSrc}
            alt={altText}
            width={600}
            height={400}
            className='inset-0 -z-0 h-full w-full rounded-md object-cover'
          />
        </div>
        <div className='mt-auto flex flex-col'>
          <h4 className='heading font-heading text-xl font-medium'>{title}</h4>
          <p className='mt-2 text-sm text-muted-foreground md:text-base'>
            {description}
          </p>
        </div>
      </div>
    </MagicCard>
  )
}

const Features = () => {
  return (
    <div className='flex w-full flex-col items-center justify-center rounded-2xl py-8 md:py-16 lg:py-24'>
      <Container>
        <div className='mx-auto flex max-w-2xl flex-col items-center text-center'>
          <SectionBadge title='Features' />
          <Heading className='mt-6'>
            Loved by Everyone <br /> Trusted by Developers
          </Heading>
          <p className='mt-6 text-center text-base text-accent-foreground/80 md:text-lg'>
            ThePortfolyo helps everyone to create beautiful portfolio websites
            with ease. It provides you the Dashboard to manage the content of
            your website.
          </p>
        </div>
      </Container>
      <div className='mt-16 w-full'>
        <div className='flex w-full flex-col items-center gap-3 lg:gap-5'>
          <Container>
            <div className='grid w-full grid-cols-1 gap-3 lg:grid-cols-[1fr_.8fr] lg:gap-5'>
              <FeatureCard
                imageSrc={
                  imageToPortfolio[
                    Math.floor(Math.random() * imageToPortfolio.length)
                  ]
                }
                altText='Resume to Portfolio'
                title='Resume to Portfolio'
                description='Convert your resume into a stunning portfolio website in minutes.'
              />
              <FeatureCard
                imageSrc='https://utfs.io/f/qPlpyBmwd8UN3ic6wGdlZbR5HzhUOVGdr8eQSpimKEJjtF9T'
                altText='One-Click Theme Switch'
                title='One-Click Theme Switch'
                description='Change the look and feel of your portfolio with a single click.'
              />
            </div>
          </Container>
          <Container>
            <div className='grid w-full grid-cols-1 gap-3 lg:grid-cols-3 lg:gap-5'>
              <FeatureCard
                imageSrc='https://utfs.io/f/qPlpyBmwd8UNQmLN5dboEoxcL67v0k4aHeTGCZpY53l1iSgW'
                altText='As Easy as Posting on Social Media'
                title='As Easy as Posting on Social Media'
                description='Just Fill in the details and your portfolio is ready to go live.'
              />
              <div className='grid-rows grid gap-3 lg:gap-5'>
                <MagicCard
                  particles={true}
                  className='row-span-1 row-start-[0.5] flex h-32 w-full flex-col items-start bg-card'
                >
                  <div className='bento-card relative w-full items-center justify-center'>
                    <div className='absolute left-1/2 top-1/2 w-full -translate-x-1/2 -translate-y-1/2'>
                      <p className='text-justify text-base text-muted-foreground [mask-image:radial-gradient(50%_50%_at_50%_50%,#BAB3FF_0%,rgba(186,179,255,0)_90.69%)]'>
                        ThePortfolyo empowers users with simple tools to create
                        professional portfolios that stand out, with no coding
                        required.
                      </p>
                    </div>
                    <div className='relative h-16 w-full'>
                      <Button className='absolute left-1/2 top-1/2 w-max -translate-x-1/2 -translate-y-1/2 bg-primary/50'>
                        Build Your Portfolio Today With ThePortfolyo
                      </Button>
                      <div className='absolute left-1/2 top-1/2 z-10 h-20 w-20 -translate-x-1/2 -translate-y-1/2 rounded-full bg-primary/10 blur-2xl'></div>
                    </div>
                  </div>
                </MagicCard>
                <MagicCard
                  particles={true}
                  className='row-start-2 flex !h-max w-full flex-col items-start bg-card'
                >
                  <div className='bento-card relative w-full gap-6'>
                    <div className='relative h-52 w-full md:h-56'>
                      <Images.rings className='absolute inset-0 h-full w-full' />
                      <Images.rings className='absolute left-1/2 top-1/2 h-56 w-56 -translate-x-1/2 -translate-y-1/2' />
                      <Ripple />
                      <Image
                        src='https://utfs.io/f/qPlpyBmwd8UNH6DswT44WbcxIHPdTa18r3v7qelB5nkUwJSO'
                        alt='ThePortfolyo'
                        width={150}
                        height={40}
                        className='absolute left-1/2 top-1/2 h-24 w-24 -translate-x-1/2 -translate-y-1/2 rounded-lg opacity-70'
                      />
                      <Images.circlePallete className='h-full w-full opacity-30' />
                    </div>
                    <div className='absolute left-1/2 top-1/2 -z-10 h-28 w-28 -translate-x-1/2 -translate-y-1/2 rounded-full bg-primary/10 blur-3xl'></div>
                  </div>
                </MagicCard>
              </div>
              <FeatureCard
                imageSrc='https://utfs.io/f/qPlpyBmwd8UN3Tsxc0dlZbR5HzhUOVGdr8eQSpimKEJjtF9T'
                altText='No Coding Required'
                title='No Coding Required'
                description='Just Enter your details through the dashboard and your portfolio is ready.'
              />
            </div>
          </Container>
          <Container>
            <div className='grid w-full grid-cols-1 gap-3 lg:grid-cols-[.8fr_1fr] lg:gap-5'>
              <FeatureCard
                imageSrc='https://utfs.io/f/qPlpyBmwd8UNKeiZpDc4VLey9SRwdklqcszTQj6ZuO2xgEvo'
                altText='Just Fill the Forms'
                title='Just Fill the Forms'
                description='Just Fill in the details , Similar to filling a form.'
              />
              <FeatureCard
                imageSrc='https://utfs.io/f/qPlpyBmwd8UNGDa9GmnvefJTtaMWc2NY5QLwjkARXpFI40rh'
                // imageSrc="https://utfs.io/f/qPlpyBmwd8UNkmqNQx66TlzUaOYt0xVNI5MShKo9A3j2LPgp"
                altText='Hosting Included'
                title='Hosting Included'
                description='No need to worry about hosting, we take care of it.'
              />
            </div>
          </Container>
        </div>
      </div>
    </div>
  )
}

export default Features

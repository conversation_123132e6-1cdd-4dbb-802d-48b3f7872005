"use client"

import { useMutation } from "@tanstack/react-query"
import { AlertCircle, CheckCircle, ChevronRight, Tag } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useMemo, useState } from "react"
import { toast } from "sonner"

import { applyCoupon } from "@/actions/checkout"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import type { Coupon } from "@/types/checkout"
import type { Plan as PlanType } from "@/types/plans"
import {
  calculateBasePrice,
  calculateCouponDiscount,
  calculateDiscountedPrice,
  calculateFinalPrice,
  formatMoney,
} from "@/utils/calculateAmount"

interface CheckoutDialogProps {
  isOpen: boolean
  onClose: () => void
  selectedPlan: PlanType
  currentPlan?: PlanType
  billingCycle: "monthly" | "yearly"
  onUpgrade?: (
    plan: PlanType,
    billingCycle: "monthly" | "yearly",
    coupon: Coupon | null,
    finalPrice: number,
  ) => Promise<void>
}

const CheckoutDialog = ({
  isOpen,
  onClose,
  selectedPlan,
  currentPlan,
  billingCycle,
  onUpgrade,
}: CheckoutDialogProps) => {
  const [couponCode, setCouponCode] = useState("")
  const [coupon, setCoupon] = useState<Coupon | null>(null)

  const router = useRouter()

  // Memoized price calculations
  const basePrice = useMemo(
    () => calculateBasePrice(selectedPlan, billingCycle),
    [selectedPlan, billingCycle],
  )

  const discountedPrice = useMemo(
    () => calculateDiscountedPrice(selectedPlan, billingCycle),
    [selectedPlan, billingCycle],
  )

  const couponDiscount = useMemo(
    () => calculateCouponDiscount(discountedPrice, coupon),
    [discountedPrice, coupon],
  )

  const finalPrice = useMemo(
    () => calculateFinalPrice(selectedPlan, billingCycle, coupon),
    [selectedPlan, billingCycle, coupon],
  )

  //reset everything when dialog is closed
  useEffect(() => {
    if (!isOpen) {
      setCouponCode("")
      setCoupon(null)
    }
  }, [isOpen])

  // Mutation for upgrading
  const upgradeMutation = useMutation({
    mutationFn: async () => {
      if (!selectedPlan || !onUpgrade) return
      await onUpgrade(
        selectedPlan,
        billingCycle,
        coupon ? coupon : null,
        finalPrice,
      )
    },
    onSuccess: () => {
      onClose()
      toast.success("Upgrade successful!")
      router.push("/dashboard")
    },
    onError: (error: any) => {
      toast.error("Upgrade failed. Please try again.")
      console.error(error)
    },
  })

  // Mutation for applying coupon
  const applyCouponMutation = useMutation({
    mutationFn: async () => {
      if (!couponCode) {
        toast.error("Please enter a coupon code.")
        return
      }

      try {
        const res = await applyCoupon(couponCode)
        if (!res.success) {
          throw new Error(res.message ?? "Invalid or expired coupon.")
        }

        const appliedCoupon: Coupon = res.data
        if (!appliedCoupon.is_active) {
          toast.error("Coupon is not active.")
          return
        }
        if (
          appliedCoupon.max_usage &&
          appliedCoupon.used_count >= appliedCoupon.max_usage
        ) {
          toast.error("Coupon has reached its maximum usage limit.")
          return
        }
        setCoupon(appliedCoupon)
        toast.success(`Coupon "${appliedCoupon.code}" applied successfully!`)
      } catch (error) {
        toast.error("Invalid or expired coupon. Please try another one.")
      }
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle className='text-2xl font-bold'>
            Confirm Upgrade
          </DialogTitle>
          <DialogDescription className='text-base'>
            You&apos;re upgrading to the &apos;{selectedPlan.name}&apos; plan
            with {selectedPlan.type === "lifetime" ? "Lifetime" : billingCycle}{" "}
            billing.
          </DialogDescription>
        </DialogHeader>
        <div className='space-y-6 py-4'>
          <div className='flex items-center justify-between rounded-lg bg-neutral-900 p-4'>
            <div className='space-y-1'>
              <p className='text-sm text-muted-foreground'>Current Plan</p>
              <p className='font-semibold'>{currentPlan?.name || "Free"}</p>
            </div>
            <ChevronRight className='h-5 w-5 text-muted-foreground' />
            <div className='space-y-1'>
              <p className='text-sm text-muted-foreground'>New Plan</p>
              <p className='font-semibold'>{selectedPlan.name}</p>
            </div>
          </div>
          <Separator />
          <div className='space-y-2'>
            <div className='flex items-center justify-between'>
              <span className='text-muted-foreground'>Base Price:</span>
              <span className='font-semibold'>
                {formatMoney(basePrice)}
                {/*/ {selectedPlan.type === "lifetime" ? "only" : billingCycle} */}
              </span>
            </div>
            {discountedPrice !== basePrice && (
              <div className='flex items-center justify-between text-green-500'>
                <span className='flex items-center'>
                  <Tag className='mr-1 h-4 w-4' />
                  Plan Discount:
                </span>
                <span>-{formatMoney(basePrice - discountedPrice)}</span>
              </div>
            )}
            {coupon && (
              <div className='flex items-center justify-between text-green-500'>
                <span className='flex items-center'>
                  <Tag className='mr-1 h-4 w-4' />
                  Coupon Discount:{" "}
                  <Badge variant={"success"} className='ml-1'>
                    {coupon.discount_type === "fixed" && "$"}
                    {coupon.discount_value}{" "}
                    {coupon.discount_type === "percentage" && "%"}
                  </Badge>
                </span>
                <span>-{formatMoney(couponDiscount)}</span>
              </div>
            )}
          </div>
          <Separator />
          <div className='flex items-center justify-between text-lg font-bold'>
            <span>Total Price:</span>
            <span>
              {formatMoney(finalPrice)}/
              {selectedPlan.type === "lifetime" ? "only" : billingCycle}
            </span>
          </div>
          <div className='space-y-2'>
            <p className='text-sm font-medium'>Have a coupon?</p>
            <div className='flex items-center gap-2'>
              <Input
                type='text'
                placeholder='Enter coupon code'
                value={couponCode}
                onChange={(e) => setCouponCode(e.target.value)}
                className='flex-grow'
                disabled={!!coupon}
              />
              <Button
                onClick={() => applyCouponMutation.mutate()}
                disabled={
                  !couponCode || !!coupon || applyCouponMutation.isPending
                }
                variant={coupon ? "secondary" : "default"}
              >
                {applyCouponMutation.isPending
                  ? "Applying..."
                  : coupon
                    ? "Applied"
                    : "Apply"}
              </Button>
            </div>
            {coupon && (
              <Badge
                variant='outline'
                className='flex items-center gap-1 px-2 py-1'
              >
                <CheckCircle className='h-3 w-3 text-green-500' />
                Coupon {coupon.code} applied
              </Badge>
            )}
          </div>
        </div>
        <DialogFooter className='sm:justify-between'>
          <Button
            variant='ghost'
            onClick={onClose}
            disabled={upgradeMutation.isPending}
            className='mb-2 w-full sm:mb-0 sm:w-auto'
          >
            Cancel
          </Button>
          <Button
            onClick={() => upgradeMutation.mutate()}
            disabled={upgradeMutation.isPending}
            className='w-full sm:w-auto'
          >
            {upgradeMutation.isPending ? "Upgrading..." : "Confirm Upgrade"}
          </Button>
        </DialogFooter>
        <p className='mt-4 flex items-center justify-center text-center text-xs text-muted-foreground'>
          <AlertCircle className='mr-1 h-3 w-3' />
          You can cancel or change your plan at any time in account settings.
        </p>
      </DialogContent>
    </Dialog>
  )
}

export default CheckoutDialog

"use client"
import { ArrowRightIcon, LayoutDashboardIcon } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { useUserStore } from "@/store/use-user-store"

import Container from "../global/container"
import HeadingBlur from "../headings/heading-blur"
import { Button } from "../ui/button"

const Hero = ({ showImage = false }) => {
  const { user } = useUserStore()
  return (
    <div className='relative z-40 mx-auto flex w-full flex-col items-center px-4 py-24 text-center md:pt-40'>
      {/* gradient background effect */}
      <div className='absolute left-[calc(55%-379px/2)] top-[-266px] -z-10 hidden h-[620px] w-[379px] rounded-full bg-primary/60 opacity-50 blur-[10rem] lg:flex'></div>
      <div className='absolute left-[calc(50%-433px/2)] top-[-60px] -z-10 hidden h-[525px] w-[433px] rounded-full bg-primaryLight/40 opacity-50 blur-[15rem] lg:flex'></div>

      {/* spotlight effect */}
      {/* <Spotlight
        className='-top-40 left-0 md:-top-20 md:left-60'
        fill='rgba(255, 255, 255, 0.5)'
      /> */}

      {/* TOP IMAGE */}

      {/* background image */}
      {showImage && (
        <div className='absolute inset-0 h-screen'>
          <Image
            src={
              "https://gallery.theportfolio.in/background-images/1743147184230-new-background-image.webp"
            }
            alt='background'
            layout='fill'
            objectFit='cover'
            objectPosition='center'
            quality={100}
            className='z-[-20] hidden opacity-70 dark:block'
          />
          <Image
            src={
              "https://gallery.theportfolio.in/background-images/1743147639568-pink-backgroun.webp"
            }
            alt='background'
            layout='fill'
            objectFit='cover'
            objectPosition='center'
            quality={100}
            className='z-[-20] block dark:hidden'
          />
        </div>
      )}

      <Container delay={0.0}>
        <div className='mx-auto flex w-max cursor-pointer select-none items-center gap-2.5 rounded-full border border-foreground/10 py-1 pl-2 pr-1 backdrop-blur-lg hover:border-foreground/15'>
          <div className='relative flex h-3.5 w-3.5 items-center justify-center rounded-full bg-primary/40'>
            <div className='flex h-2.5 w-2.5 animate-ping items-center justify-center rounded-full bg-primary/60'>
              <div className='flex h-2.5 w-2.5 animate-ping items-center justify-center rounded-full bg-primary/60'></div>
            </div>
            <div className='absolute left-1/2 top-1/2 flex h-1.5 w-1.5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary'></div>
          </div>
          <h1 className='animate-text-gradient inline-flex animate-background-shine items-center justify-center gap-2 bg-gradient-to-r from-secondary to-primary bg-[200%_auto] bg-clip-text text-sm text-transparent dark:from-[#b2a8fd] dark:via-[#8678f9] dark:to-[#c7d2fe]'>
            Build Portfolio in Minutes
            <span className='flex items-center justify-center rounded-full bg-gradient-to-b from-foreground/20 to-foreground/10 px-1.5 py-0.5 text-xs text-secondary-foreground'>
              What&apos;s new
              <ArrowRightIcon className='ml-1 h-3.5 w-3.5 text-foreground/50' />
            </span>
          </h1>
        </div>
      </Container>

      <HeadingBlur word={"Build your portfolio \n with ease"} />
      <Container delay={0.1}>
        <h2 className='mx-auto mt-4 max-w-lg text-pretty text-sm text-foreground dark:text-accent-foreground sm:text-base lg:text-lg'>
          Create a stunning portfolio website effortlessly! It’s as simple as
          posting on social media—just fill in your details, and your portfolio
          is ready. <b>No coding required!</b>
        </h2>
      </Container>
      <Container delay={0.2}>
        <div className='mt-8 flex items-center justify-center md:gap-x-6'>
          <Button asChild variant='white' className='hidden md:flex'>
            <Link href='/portfolio-themes'> Portfolio Themes </Link>
          </Button>
          {!user ? (
            <Button asChild variant='default'>
              <Link href='/auth/login'> Start For Free </Link>
            </Button>
          ) : (
            <Button asChild variant='default'>
              <Link href='/dashboard'>
                {" "}
                <LayoutDashboardIcon className='mr-1 h-4 w-4' />
                Dashboard
              </Link>
            </Button>
          )}
        </div>
      </Container>
      <Container delay={0.3}>
        <div className='relative mx-auto mt-12 max-w-5xl rounded-xl border border-neutral-300 bg-primary-foreground p-2 backdrop-blur-lg dark:border-neutral-700 dark:bg-neutral-800/50 md:p-4 lg:rounded-[32px]'>
          <div className='gradient absolute inset-0 left-1/2 top-1/4 -z-10 h-1/4 w-3/4 -translate-x-1/2 -translate-y-1/2 blur-[10rem]'></div>

          <div className='rounded-lg border border-neutral-700 bg-black p-2 lg:rounded-[24px]'>
            <Image
              // src="/images/dashboard.png"
              src={
                // "https://utfs.io/f/qPlpyBmwd8UNmEVwHkZR9nPwrYz4VB7gN6TJC5OlkibEQDKa"
                "https://agency-assets.theportfolyo.com/theportfolyo-main/hard-coded/dashboard-tp.webp"
              }
              alt='dashboard'
              width={1920}
              height={1080}
              className='rounded-lg lg:rounded-[20px]'
            />
          </div>
        </div>
      </Container>
    </div>
  )
}

export default Hero

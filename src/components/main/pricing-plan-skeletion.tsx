import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"

// we can add length to make the skeltion reusble

export const PricingPlansSkeleton = () => {
  return (
    <div className='mx-auto w-full max-w-4xl'>
      <div className='mb-8 flex justify-center'>
        <Skeleton className='h-10 w-48' />
      </div>
      <div
        className={cn("grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2")}
      >
        {[...Array(2)].map((_, index) => (
          <div key={index} className='space-y-4 rounded-lg border p-6'>
            <Skeleton className='h-8 w-24' />
            <Skeleton className='h-4 w-full' />
            <Skeleton className='h-4 w-3/4' />
            <div className='space-y-2'>
              {[...Array(8)].map((_, i) => (
                <Skeleton key={i} className='h-4 w-full' />
              ))}
            </div>
            <Skeleton className='h-10 w-full' />
          </div>
        ))}
      </div>
    </div>
  )
}

import { Star } from "lucide-react"

import { RE<PERSON>EWS } from "@/constants"
import { REVIEW } from "@/constants/reviews"

import Container from "../global/container"
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import Marquee from "../ui/marquee"
import { SectionBadge } from "../ui/section-bade"

const firstRow = REVIEWS.slice(0, REVIEWS.length / 2)
const secondRow = REVIEWS.slice(REVIEWS.length / 2)

const Reviews = () => {
  return (
    <div className='flex w-full flex-col items-center justify-center py-12 md:py-16 lg:py-24'>
      <Container>
        <div className='mx-auto flex max-w-xl flex-col items-center text-center'>
          <SectionBadge title='Our Customers' />
          <h2 className='mt-6 font-heading text-2xl font-medium !leading-snug md:text-4xl lg:text-5xl'>
            What our users are saying
          </h2>
          <p className='mt-6 text-center text-base text-accent-foreground/80 md:text-lg'>
            ThePortfolyo has empowered countless developers and designers to
            elevate their projects. Here are some of their success stories
          </p>
        </div>
      </Container>
      <Container>
        <div className='relative mt-16 w-full overflow-hidden'>
          <div className='relative flex flex-col items-center justify-center overflow-hidden'>
            <Marquee pauseOnHover className='[--duration:30s]'>
              {firstRow.map((review) => (
                <ReviewCard key={review.name} {...review} />
              ))}
            </Marquee>
            <Marquee pauseOnHover reverse className='[--duration:30s]'>
              {secondRow.map((review) => (
                <ReviewCard key={review.name} {...review} />
              ))}
            </Marquee>
            <div className='pointer-events-none absolute inset-y-0 left-0 w-1/4 bg-gradient-to-r from-background'></div>
            <div className='pointer-events-none absolute inset-y-0 right-0 w-1/4 bg-gradient-to-l from-background'></div>
            <div className='absolute left-1/4 top-1/4 -z-10 hidden h-28 w-28 rounded-full bg-primary/80 blur-[6rem] lg:block'></div>
            <div className='absolute right-1/4 top-1/4 -z-10 hidden h-28 w-28 rounded-full bg-primary/80 blur-[6rem] lg:block'></div>
          </div>
        </div>
      </Container>
    </div>
  )
}

const ReviewCard = ({ image, name, title, rating, review }: REVIEW) => {
  return (
    <figure className='relative w-64 cursor-pointer overflow-hidden rounded-xl border border-foreground/5 bg-neutral-50/[.05] p-4 transition-all duration-300 ease-in-out hover:bg-foreground/10 md:min-w-80'>
      <div className='flex flex-row items-start justify-between gap-2'>
        {/* <Image
          className="rounded-full"
          width="32"
          height="32"
          alt=""
          src={image}
        /> */}
        <div className='flex flex-row items-center gap-2'>
          <Avatar className='h-8 w-8 rounded-full'>
            <AvatarImage src={image} alt={name} className='object-cover' />
            <AvatarFallback>
              {name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <div className='flex flex-col'>
            <figcaption className='text-sm font-medium text-foreground'>
              {name}
            </figcaption>
            <p className='text-xs font-medium text-foreground/40'>{title}</p>
          </div>
        </div>

        <div className='flex items-center gap-1 text-yellow-500'>
          {Array.from({ length: rating }).map((_, i) => (
            <Star key={i} className='h-4 w-4' />
          ))}
        </div>
      </div>
      <blockquote className='mt-2 text-sm'>{review}</blockquote>
    </figure>
  )
}

export default Reviews

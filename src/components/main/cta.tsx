import Link from "next/link"

import Container from "../global/container"
import { Button } from "../ui/button"
import { Particles } from "../ui/particles"
import RetroGrid from "../ui/retro-grid"

const CTA = () => {
  return (
    <div className='relative flex w-full flex-col items-center justify-center py-12 md:py-16 lg:py-24'>
      <Container>
        <div className='relative mx-auto flex h-[500px] w-full flex-col items-center justify-center overflow-hidden rounded-3xl border border-foreground/10 px-4 text-center md:px-0'>
          <div className='absolute bottom-0 left-1/2 h-12 w-full -translate-x-1/2 bg-violet-500 blur-[10rem]'></div>
          <div className='z-20 flex w-full flex-col items-center justify-center'>
            <h2 className='heading mt-6 font-heading text-4xl font-semibold !leading-tight md:text-6xl'>
              {/* Elevate your <br className="hidden md:block" /> experience with us */}
              Get Started with <br className='hidden md:block' /> ThePortfolyo
              Today
            </h2>
            <p className='mx-auto mt-6 max-w-xl text-center text-base text-accent-foreground/80 md:text-lg'>
              Discover the easiest way to create stunning, professional
              portfolio websites tailored to your needs, without any coding
              required.
            </p>
            <div className='mt-6 flex w-full flex-col items-center justify-center gap-6 md:flex-row'>
              <Button asChild size='lg' className='w-full md:w-max'>
                <Link href=''>Get Started</Link>
              </Button>
              <Button
                asChild
                size='lg'
                variant='secondary'
                className='w-full md:w-max'
              >
                <Link href=''>Learn More</Link>
              </Button>
            </div>
          </div>
          <RetroGrid />
          <Particles
            refresh
            ease={80}
            color='#d4d4d8'
            quantity={100}
            className='absolute inset-0 size-full'
          />
        </div>
      </Container>
    </div>
  )
}

export default CTA

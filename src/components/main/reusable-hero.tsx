import { cn } from "@/lib/utils"

import Container from "../global/container"
import HeadingGradientBlur from "../headings/heading-gradient-blur"
import { Spotlight } from "../ui/spotlight"

type Props = {
  className?: string
  title: string
  description: string
}

const CommonHero = ({ title, description, className }: Props) => {
  return (
    <div
      className={cn(
        "relative mx-auto flex w-full flex-col items-center px-4 pt-20 text-center md:pt-24",
        className,
      )}
    >
      {/* gradient background effect */}
      <div className='absolute left-0 top-[-266px] -z-10 hidden h-[620px] w-[379px] rounded-full bg-primary/10 opacity-50 blur-[10rem] dark:left-[calc(55%-379px/2)] dark:bg-primary/60 lg:flex'></div>

      <div className='absolute right-0 top-[-60px] -z-10 hidden h-[525px] w-[433px] rounded-full bg-primary/20 opacity-50 blur-[15rem] dark:left-[calc(50%-433px/2)] dark:bg-primaryLight/40 lg:flex'></div>

      {/* spotlight effect */}
      <Spotlight
        className='-top-40 left-0 md:-top-20 md:left-60'
        fill='rgba(255, 255, 255, 0.5)'
      />

      <HeadingGradientBlur word={title} />
      <Container delay={0.1}>
        <p className='mx-auto mt-4 max-w-2xl text-sm text-foreground dark:text-accent-foreground/60 sm:text-base lg:text-lg'>
          {description}
        </p>
      </Container>
    </div>
  )
}
export default CommonHero

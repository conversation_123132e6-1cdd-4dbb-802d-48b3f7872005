"use client"

import { useState } from "react"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { Coupon } from "@/types/checkout"
import type { Plan as PlanType } from "@/types/plans"

import CheckoutDialog from "./checkout-dialog"
import PlanCard from "./plan-card"

interface PricingPlansProps {
  plans: PlanType[]
  currentPlan?: string
  isUpgradePage?: boolean
  onUpgrade?: (
    plan: PlanType,
    billingCycle: "monthly" | "yearly",
    coupon: Coupon | null,
    finalPrice: number,
  ) => Promise<void>
}

export const PricingPlans = ({
  plans,
  currentPlan,
  isUpgradePage = false,
  onUpgrade,
}: PricingPlansProps) => {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">(
    "monthly",
  )
  const [selectedPlan, setSelectedPlan] = useState<PlanType | null>(null)
  const [isCheckoutDialogO<PERSON>, setIsCheckoutDialog<PERSON>pen] = useState(false)

  const filteredPlans = isUpgradePage
    ? plans.filter((plan) => plan.type !== "free")
    : plans

  return (
    <>
      <Tabs
        value={billingCycle}
        onValueChange={(value) =>
          setBillingCycle(value as "monthly" | "yearly")
        }
        className='flex w-full flex-col items-center'
      >
        <TabsList>
          <TabsTrigger value='monthly'>Monthly</TabsTrigger>
          <TabsTrigger value='yearly'>Yearly</TabsTrigger>
        </TabsList>
        <TabsContent value={billingCycle} className='w-full'>
          <div
            className={cn(
              "mt-14 grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",
              isUpgradePage && "mx-auto max-w-5xl lg:grid-cols-2",
            )}
          >
            {filteredPlans.map((plan) => (
              <PlanCard
                key={plan._id}
                plan={plan}
                planType={billingCycle}
                isCurrentPlan={currentPlan === plan._id}
                isSelected={selectedPlan?._id === plan._id}
                onSelect={() => {
                  setSelectedPlan(plan)
                  if (isUpgradePage) {
                    setIsCheckoutDialogOpen(true)
                  }
                }}
                isUpgradePage={isUpgradePage}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
      {isUpgradePage && selectedPlan && (
        <CheckoutDialog
          isOpen={isCheckoutDialogOpen}
          onClose={() => setIsCheckoutDialogOpen(false)}
          selectedPlan={selectedPlan}
          currentPlan={plans.find((p) => p._id === currentPlan)}
          billingCycle={billingCycle}
          onUpgrade={onUpgrade}
        />
      )}
    </>
  )
}

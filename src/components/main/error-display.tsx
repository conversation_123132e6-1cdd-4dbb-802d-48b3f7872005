import { AlertCircle } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"

interface ErrorDisplayProps {
  message: string
  onRetry?: () => void
}

export const ErrorDisplay = ({ message, onRetry }: ErrorDisplayProps) => {
  return (
    <div className='flex min-h-[400px] flex-col items-center justify-center text-center'>
      <AlertCircle className='mb-4 h-16 w-16 text-destructive' />
      <h2 className='mb-2 text-2xl font-bold'>Oops! Something went wrong</h2>
      <p className='mb-6 text-muted-foreground'>{message}</p>
      {onRetry && (
        <Button onClick={onRetry} variant='outline'>
          Try Again
        </Button>
      )}
    </div>
  )
}

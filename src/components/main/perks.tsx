import { LucideIcon } from "lucide-react"

import { PERKS } from "@/constants"
import { cn } from "@/functions"

import Container from "../global/container"
import { SectionBadge } from "../ui/section-bade"

const Perks = () => {
  return (
    <div className='flex w-full flex-col items-center justify-center py-12 md:py-16 lg:py-24'>
      <Container>
        <div className='mx-auto flex max-w-2xl flex-col items-center text-center'>
          <SectionBadge title='Perks' />
          <h2 className='mt-6 font-heading text-2xl font-medium !leading-snug md:text-4xl lg:text-5xl'>
            Discover the Benefits of ThePortfolyo
          </h2>
          <p className='mt-6 text-center text-base text-accent-foreground/80 md:text-lg'>
            {/* Unlock a World of Resources to Accelerate Your Development Projects */}
            ThePortfolyo is a digital marketplace offering high-quality,
            customizable resources to help developers and designers streamline
            their workflows and enhance their projects.
          </p>
        </div>
      </Container>
      <Container>
        <div className='mt-16 w-full'>
          <div className='relative grid w-full grid-cols-1 md:grid-cols-2 lg:grid-cols-3'>
            {PERKS.map((perk, index) => (
              <Perk key={index} index={index} {...perk} />
            ))}
          </div>
        </div>
      </Container>
    </div>
  )
}

const Perk = ({
  title,
  description,
  icon: Icon,
  index,
}: {
  title: string
  description: string
  icon: LucideIcon
  index: number
}) => {
  return (
    <div
      className={cn(
        "group/feature relative flex transform-gpu flex-col border-secondary py-10 dark:border-primary/15 lg:border-r",
        (index === 0 || index === 3) && "lg:border-l",
        index < 3 && "lg:border-b",
      )}
    >
      {index < 3 && (
        <div className='from-neutral-80 pointer-events-none absolute inset-0 h-full w-full bg-gradient-to-t from-primary/30 to-transparent transition duration-200 group-hover/feature:opacity-100 dark:from-violet-950/25 md:opacity-50' />
      )}

      {index >= 3 && (
        <div className='from-neutral-80 pointer-events-none absolute inset-0 h-full w-full bg-gradient-to-t from-primary/30 to-transparent transition duration-200 group-hover/feature:opacity-100 dark:from-violet-950/25 md:bg-gradient-to-b md:opacity-50' />
      )}

      <div className='flex w-full transform-gpu flex-col transition-all duration-300 group-hover/feature:-translate-y-1'>
        <div className='relative z-10 mb-4 px-10'>
          <Icon
            strokeWidth={1.3}
            className='h-10 w-10 origin-left transform-gpu text-neutral-500 transition-all duration-300 ease-in-out group-hover/feature:scale-75 group-hover/feature:text-foreground'
          />
        </div>
        <div className='relative z-10 mb-2 px-10 font-heading text-lg font-medium'>
          <div className='absolute -inset-y-0 left-0 h-6 w-1 origin-center rounded-br-full rounded-tr-full bg-secondary transition-all duration-500 group-hover/feature:h-8 group-hover/feature:bg-violet-600 dark:bg-neutral-700' />
          <span className='group-hover/feature:-translate-y- group-hover/feature:text- heading inline-block transition duration-500'>
            {title}
          </span>
        </div>
        <p className='relative z-10 max-w-xs px-10 text-sm text-muted-foreground'>
          {description}
        </p>
      </div>
    </div>
  )
}

export default Perks

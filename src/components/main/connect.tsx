import Container from "../global/container"
import Images from "../global/images"
import Heading from "../headings/heading"
import { Particles } from "../ui/particles"
import { SectionBadge } from "../ui/section-bade"

const Connect = () => {
  return (
    <div className='flex w-full flex-col items-center justify-center py-8 md:py-12'>
      <Container>
        <div className='mx-auto flex max-w-2xl flex-col items-center text-center'>
          <SectionBadge title='Connect Tools' />
          {/* <h2 className="text-2xl md:text-4xl lg:text-5xl font-heading font-medium !leading-snug mt-6">
                        Seamless Integration with your favorite tools
                    </h2> */}
          <Heading className='mt-6 !leading-snug'>
            {/* Power Your Workflow with Effortless Integrations */}
            Ready-to-Use Resources for Every Tech Stack
          </Heading>
          <p className='mt-6 text-center text-base text-accent-foreground/80 md:text-lg'>
            Access templates and starter kits built for a variety of
            technologies, including web, mobile, and design tools. From React to
            Figma, we provide everything you need to jumpstart your projects
            effortlessly.
          </p>
        </div>
      </Container>
      <Container>
        <div className='relative mt-12 w-full'>
          <Images.connect className='h-auto w-full' />
          <Particles
            className='absolute inset-0'
            quantity={150}
            ease={80}
            color='#e4e4e7'
            refresh
          />
        </div>
      </Container>
    </div>
  )
}

export default Connect

"use client"

import { AnimatePresence, motion } from "framer-motion"
import { CheckCircleIcon, MinusCircleIcon } from "lucide-react"
import Link from "next/link"

import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { Plan as PlanType } from "@/types/plans"

import Container from "../global/container"
import { Button } from "../ui/button"
import NumberTicker from "../ui/number-ticker"
import { SectionBadge } from "../ui/section-bade"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip"

const Pricing = ({ plans }: { plans: PlanType[] }) => {
  return (
    <div className='relative flex w-full flex-col items-center justify-center py-12 md:py-16 lg:py-24'>
      <Container>
        <div className='mx-auto flex max-w-xl flex-col items-center text-center'>
          <SectionBadge title='Choose your plan' />
          <h2 className='mt-6 font-heading text-2xl font-medium md:text-4xl lg:text-5xl'>
            Simple and transparent pricing
          </h2>
          <p className='my-6 text-base text-accent-foreground/80 md:text-lg'>
            Choose the plan that suits your needs. No hidden fees, no surprises.
          </p>
        </div>
      </Container>
      <Container>
        <Tabs
          defaultValue='monthly'
          className='flex w-full flex-col items-center'
        >
          <TabsList>
            <TabsTrigger value='monthly'>Monthly</TabsTrigger>
            <TabsTrigger value='yearly'>Yearly</TabsTrigger>
          </TabsList>
          {["monthly", "yearly"].map((tab) => (
            <TabsContent value={tab} key={tab}>
              <div className='mt-14 grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
                {plans?.map((plan) => (
                  <Plan
                    key={plan._id}
                    plan={plan}
                    planType={tab as "monthly" | "yearly"}
                  />
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </Container>
    </div>
  )
}

const Plan = ({
  plan,
  planType,
}: {
  plan: PlanType
  planType: "monthly" | "yearly"
}) => {
  const {
    name,
    description,
    pricing,

    type,
    metadata,

    discount,
  } = plan

  const percentage = discount?.percentage ?? 0
  const displayedPrice =
    planType === "monthly" ? pricing.monthly : pricing.yearly

  const priceAfterDiscount = Math.floor(
    displayedPrice - (displayedPrice * percentage) / 100,
  )
  // const buttonText = type === "pro" ? "Choose Pro" : "Get Started";

  return (
    <div
      key={plan._id}
      className='relative flex w-full flex-col rounded-2xl saturate-150'
    >
      <div
        className={cn(
          "relative flex size-full flex-col rounded-2xl border p-3 [background-image:linear-gradient(345deg,rgba(255,255,255,0.01)_0%,rgba(255,255,255,0.03)_100%)]",
          type === "pro" ? "border-primary/80" : "border-border/60",
        )}
      >
        {type === "pro" && (
          <div className='absolute -top-3 left-1/2 inline-flex h-7 min-w-min max-w-fit -translate-x-1/2 select-none items-center whitespace-nowrap rounded-full bg-gradient-to-r from-primary to-violet-500 px-1'>
            <span className='flex-1 animate-background-shine bg-gradient-to-r from-foreground to-foreground/80 bg-[length:250%_100%] bg-clip-text px-2 text-sm font-medium text-transparent'>
              Most Popular
            </span>
          </div>
        )}
        <div className='flex w-full flex-col p-3'>
          <h2 className='text-xl font-medium'>{name}</h2>
          <p className='mt-2 break-words text-sm text-muted-foreground'>
            {description}
          </p>
        </div>
        <hr
          className='h-px w-full shrink-0 border-none bg-border'
          role='separator'
        />

        <div className='relative flex h-full w-full flex-1 flex-col gap-4 break-words p-3 text-left align-top'>
          {/* pric */}

          <div className='flex items-end gap-2'>
            <div className='flex w-40 min-w-max items-end gap-1'>
              <span className='text-3xl font-bold md:text-4xl'>
                $
                {priceAfterDiscount === 0 ? (
                  0
                ) : (
                  <NumberTicker value={priceAfterDiscount} />
                )}
              </span>
              {type !== "free" && (
                <span className='text-xl font-bold text-muted-foreground line-through md:text-2xl'>
                  ${displayedPrice}
                </span>
              )}
              {/* In here 120 * 0.8 = 96 and /12 to get monthly price */}
              {/* <span className="text-lg text-muted-foreground">
                per {planType === "monthly" ? "month" : "year"}
              </span> */}
              {type === "pro" && (
                <span className='text-lg text-muted-foreground'>
                  /{planType === "monthly" ? "month" : "year"}
                  {/* /month */}
                </span>
              )}
              {type === "lifetime" && (
                <span className='text-lg text-muted-foreground'>only</span>
              )}
            </div>
            <AnimatePresence>
              {type !== "free" && (
                <motion.span
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0 }}
                  transition={{ duration: 0.2 }}
                  aria-hidden='true'
                  className='mb-1 rounded bg-primary px-2 py-0.5 text-xs font-medium text-foreground'
                >
                  -{percentage}%
                </motion.span>
              )}
            </AnimatePresence>
          </div>

          <ul className='flex flex-col gap-2'>
            {/* {features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2">
                <CheckIcon
                  aria-hidden="true"
                  className="w-5 h-5 text-primary"
                />
                <p className="text-sm md:text-base text-muted-foreground">
                  {feature}
                </p>
              </li>
            ))} */}

            {metadata?.features.map((feature: any, index: number) => (
              <div key={index} className='flex items-center gap-2'>
                {feature.included ? (
                  <CheckCircleIcon className='h-4 w-4 text-green-500' />
                ) : (
                  <MinusCircleIcon className='h-4 w-4 text-red-500' />
                )}
                <TooltipProvider>
                  <Tooltip delayDuration={0}>
                    <TooltipTrigger asChild>
                      <p
                        className={cn(
                          feature.tooltip &&
                            "cursor-pointer border-b !border-dashed border-border",
                        )}
                      >
                        {feature.text}
                      </p>
                    </TooltipTrigger>
                    {feature.tooltip && (
                      <TooltipContent>
                        <p>{feature.tooltip}</p>
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              </div>
            ))}
          </ul>
        </div>

        <div className='mt- flex h-auto w-full items-center p-3'>
          {type === "free" ? (
            <Button
              asChild
              variant={"white"}
              className='w-full shadow-none hover:translate-y-0 hover:scale-100'
            >
              <Link href={"/"}>{metadata?.btn.text}</Link>
            </Button>
          ) : (
            <Button
              asChild
              className='w-full shadow-none hover:translate-y-0 hover:scale-100'
            >
              <Link href={"/dashboard"}>{metadata?.btn.text}</Link>
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export default Pricing

"use client"

import { motion, useAnimation, useInView } from "framer-motion"
import { Check } from "lucide-react"
import Image from "next/image"
import { useEffect, useRef } from "react"

import Container from "../global/container"
import { SectionBadge } from "../ui/section-bade"

const features = [
  { title: "Personalized Dashboard/CMS", column: 1 },
  { title: "Easy Analytics Setup", column: 2 },
  { title: "User-Friendly Interface", column: 1 },
  { title: "SEO Optimised", column: 2 },
  { title: "Customizable Templates", column: 1 },
  { title: "Update Portfolio via Mobile", column: 2 },
  { title: "No Coding Required", column: 1 },
  { title: "Responsive Across the Devices", column: 2 },
  { title: "Career Empowerment", column: 1 },
  { title: "Secure Hosting", column: 2 },
  { title: "Fast Loading Pages", column: 1 },
  { title: "Designer-Crafted Portfolios", column: 2 },
  { title: "SSL Included", column: 1 },
  { title: "Montly Free Template Drop", column: 2 },
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut",
    },
  },
}

const imageVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: 50,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut",
    },
  },
}

export default function FeaturesSection() {
  const controls = useAnimation()
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  useEffect(() => {
    if (isInView) {
      controls.start("visible")
    }
  }, [controls, isInView])

  return (
    <section
      ref={ref}
      // className="relative min-h-screen w-full overflow-hidden "
      className='flex w-full flex-col items-center justify-center py-12 md:py-16 lg:py-24'
    >
      <Container>
        <div className='mx-auto flex max-w-2xl flex-col items-center text-center'>
          <SectionBadge title='More Features' />
          <h2 className='mt-6 font-heading text-2xl font-medium !leading-snug md:text-4xl lg:text-5xl'>
            More Fetures to Love
          </h2>
          <p className='mt-6 text-center text-base text-accent-foreground/80 md:text-lg'>
            ThePortfolyo provides you features that will help you create a
            stunning portfolio website.
          </p>
        </div>
      </Container>

      {/* Animated gradient backgrounds */}
      {/* <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="absolute inset-0 overflow-hidden"
      >
        <div className="absolute -left-[25%] top-0 h-[500px] w-[500px] animate-pulse rounded-full bg-purple-500/20 blur-[128px]" />
        <div className="absolute -right-[25%] bottom-0 h-[500px] w-[500px] animate-pulse rounded-full bg-cyan-500/20 blur-[128px]" />
      </motion.div> */}

      <Container className='relative mx-auto mt-16 px-4'>
        <div className='grid gap-0 md:grid-cols-2 md:gap-8 lg:grid-cols-3'>
          {/* Left column with image */}
          <motion.div
            variants={imageVariants}
            initial='hidden'
            animate={controls}
            className='relative mx-auto'
          >
            <Image
              // src="https://assets.theportfolyo.com/WEBSITE-IMAGES+/1708787557621-8.webp"
              src='https://utfs.io/f/qPlpyBmwd8UNjm3GENKKw8vEHUYxf9mgWpOalC0SGu2cnd3J'
              // src="https://assets.theportfolyo.com/WEBSITE-IMAGES+/1708787557621-8.webp"
              alt='Portfolio features illustration'
              width={600}
              height={600}
              className='rounded-lg shadow-2xl'
              priority
            />
            {/* <motion.div
              variants={badgeVariants}
              initial="hidden"
              animate={controls}
              className="mt-4 rounded-lg bg-gradient-to-r from-purple-500/30 to-cyan-500/30 px-4 py-2 backdrop-blur-sm text-center"
            >
              <span className="text-sm font-semibold text-white">
                GET PORTFOLIO NOW
              </span>
            </motion.div> */}
          </motion.div>

          {/* Right column with features */}
          {/* <div className="grid grid-cols-1 gap-8 md:grid-cols-2"> */}
          <div className='col-span-2 mt-10 grid grid-cols-1 gap-4 md:mt-0 md:grid-cols-2 md:gap-8'>
            {[1, 2].map((column) => (
              <motion.div
                key={column}
                variants={containerVariants}
                initial='hidden'
                animate={controls}
                className='space-y-2 md:space-y-4'
              >
                {features
                  .filter((feature) => feature.column === column)
                  .map((feature, index) => (
                    <motion.div
                      key={index}
                      variants={itemVariants}
                      // whileHover={{ scale: 1.05 }}
                      className='from-neutral-80 group flex items-center gap-3 rounded-lg bg-white/5 bg-gradient-to-b from-primary/10 p-2 pl-3 transition-colors duration-200 hover:bg-white/10 dark:from-primary/10'
                    >
                      <motion.div
                        initial={{ scale: 1 }}
                        // whileHover={{ scale: 1.2 }}
                        className='flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-r from-emerald-200 to-emerald-100 dark:from-emerald-500/20 dark:to-emerald-500/30'
                      >
                        <Check className='h-4 w-4 text-emerald-600 dark:text-emerald-500' />
                      </motion.div>
                      <span className='text-sm text-foreground dark:text-muted-foreground md:text-base'>
                        {feature.title}
                      </span>
                    </motion.div>
                  ))}
              </motion.div>
            ))}
          </div>
        </div>
      </Container>
    </section>
  )
}

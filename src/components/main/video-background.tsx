import Container from "../global/container"

const VideoBackground = () => {
  return (
    <>
      {/* video bg */}
      <Container
        delay={0.2}
        className='pointer-events-none fixed inset-0 z-0 max-h-screen bg-black opacity-50 md:block'
      >
        {/* dark overlay */}
        <div className='absolute inset-0 z-[1] bg-black opacity-60' />

        {/* CRT Overlay */}
        <div className="absolute inset-0 z-10 bg-[url('https://payloadcms.com/images/crt.gif')] bg-[length:256px] bg-center bg-repeat mix-blend-multiply" />

        {/* Video Background */}
        <div className='relative z-0 h-full'>
          <video
            autoPlay
            loop
            muted
            playsInline
            className='h-full w-full object-cover'
            src='https://l4wlsi8vxy8hre4v.public.blob.vercel-storage.com/video/glass-animation-5-f0gPcjmKFIV3ot5MGOdNy2r4QHBoXt.mp4'
          />
        </div>
      </Container>
    </>
  )
}

export default VideoBackground

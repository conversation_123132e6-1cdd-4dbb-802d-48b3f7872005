"use client"
import { motion } from "framer-motion"
import { Eye, Heart, ShoppingCart } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { createArrayFromString } from "@/utils/generic"

interface ProductCardProps {
  data: any
}

export function MainCard({ data }: ProductCardProps) {
  // const mainDiscount = 0.2;
  // const discountedPrice = Math.floor(data?.price * mainDiscount);
  // const discountedPrice = data?.price && data?.price * mainDiscount;

  return (
    <Card
      className='group relative mx-auto max-w-7xl cursor-pointer overflow-hidden rounded-md border-0 border-neutral-200/50 border-neutral-700 bg-gray-800/50 p-0 lg:rounded-xl'
      // className="relative mx-auto max-w-7xl rounded-xl lg:rounded-xl border-0 border-card bg-card p-0 overflow-hidden group cursor-pointer"
    >
      {/* backdrop-blur-lg */}

      <Link
        // href={`/templates/${data.previewUrl
        //   ?.toLowerCase()
        //   .replace(/\s+/g, "-")}`}
        href={`/${data.type}/${data.slug}`}
      >
        <div className='relative rounded-t-md bg-black lg:rounded-t-xl'>
          <Image
            // src="/images/dashboard.png"
            src={createArrayFromString(data?.images)[0]}
            alt='dashboard'
            width={600}
            height={600}
            // className="object-cover aspect-video"
            className='object-contain'
          />

          {/* Hover Overlay */}
          <motion.div className='absolute inset-0 flex items-center justify-center gap-4 bg-black/50 opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100'>
            {/* Preview Button */}
            <Button
              // variant="secondary"
              variant='outline'
              size='sm'
              className='flex h-12 w-12 items-center justify-center rounded-full transition-all duration-300 hover:border-white'
            >
              <Eye
                // size={24}

                className='text-neutral-300 transition-all hover:text-white'
              />
            </Button>
            {/* Cart Button */}
            <Button
              // variant="destructive"
              variant='outline'
              size='sm'
              className='flex h-12 w-12 items-center justify-center rounded-full transition-all duration-300 hover:border-white'
            >
              <ShoppingCart
                // size={24}
                className='text-neutral-300 transition-all hover:text-white'
              />
            </Button>
          </motion.div>

          {/* Like Button */}
          <motion.div className='absolute right-2 top-2 opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100'>
            <Button
              variant='ghost'
              // variant={"outline"}
              size='sm'
              className='flex h-10 w-10 items-center justify-center rounded-full transition-all duration-300 hover:border-white'
            >
              <Heart
                size={24}
                className='text-neutral-300 transition-all hover:text-white'
              />
            </Button>
          </motion.div>
        </div>
        <CardContent className='px-4 py-2.5'>
          <div className='flex items-center justify-between gap-2'>
            <h3 className='line-clamp-1 font-heading text-sm font-medium'>
              {data.fullName}
            </h3>
            {/* <div className="flex items-center justify-start gap-2">
              <span className="text-base font-bold text-emerald-600 dark:text-emerald-400">
                ${discountedPrice}
              </span>
              <div className="flex items-center gap-2">
                <span className="text-sm line-through text-gray-500">
                  ${data.price}
                </span>
              </div>
            </div> */}
          </div>

          <div className='flex items-center justify-start gap-1'>
            <span className='text-sm font-medium text-gray-600 dark:text-gray-400'>
              ${data.price}
            </span>
            <div className='flex items-center gap-2'>
              <span className='text-xs text-gray-500 line-through'>
                ${data.price}
              </span>
            </div>
          </div>
        </CardContent>
      </Link>
    </Card>
  )
}

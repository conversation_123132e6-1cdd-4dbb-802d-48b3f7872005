import React from "react"

import { cn } from "@/functions"

interface Props {
  isOpen: boolean
  onClick: () => void
}

const MenuIcon = ({ isOpen, onClick }: Props) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "relative z-40 flex h-8 w-8 flex-col items-center justify-center gap-1.5 rounded-md p-2 hover:bg-muted lg:hidden",
        "focus:outline-none",
      )}
    >
      <span
        className={cn(
          "block h-[1.5px] w-4 origin-center transform rounded-full bg-current transition-transform duration-300 ease-in-out",
          isOpen ? "rotate-45" : "rotate-0",
        )}
      ></span>
      <span
        className={cn(
          "block h-[1.5px] w-4 origin-center transform rounded-full bg-current transition-transform duration-300 ease-in-out",
          isOpen ? "-rotate-45" : "rotate-0",
        )}
      ></span>
    </button>
  )
}

export default MenuIcon

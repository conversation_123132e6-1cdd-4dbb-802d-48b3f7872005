import { ChevronLeft, ChevronRight } from "lucide-react"
import * as React from "react"

import { ButtonProps, buttonVariants } from "@/components/ui/button"
import { cn } from "@/functions"

const Pagination = ({ className, ...props }: React.ComponentProps<"nav">) => (
  <nav
    role='navigation'
    aria-label='pagination'
    className={cn("mx-auto flex w-full justify-end", className)}
    {...props}
  />
)
Pagination.displayName = "Pagination"

const PaginationContent = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    className={cn(
      "flex flex-row items-center overflow-hidden rounded-lg bg-background shadow-sm",
      className,
    )}
    {...props}
  />
))
PaginationContent.displayName = "PaginationContent"

const PaginationItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn("", className)} {...props} />
))
PaginationItem.displayName = "PaginationItem"

type PaginationLinkProps = {
  isActive?: boolean
} & Pick<ButtonProps, "size"> &
  React.ComponentProps<"a">

const PaginationLink = ({
  className,
  isActive,

  ...props
}: PaginationLinkProps) => (
  <a
    aria-current={isActive ? "page" : undefined}
    className={cn(
      "grid size-[30px] place-content-center !rounded-none text-xs text-muted-foreground !shadow-none !ring-0 hover:!border-x hover:border-secondary",
      buttonVariants({
        variant: isActive ? "outline-general" : "outline-general",
      }),
      className,
      isActive &&
        "border border-x bg-background text-primary-foreground hover:bg-secondary hover:text-primary-foreground",
    )}
    {...props}
  />
)
PaginationLink.displayName = "PaginationLink"

const PaginationPrevious = ({
  className,
  disabled,
  ...props
}: React.ComponentProps<typeof PaginationLink> & { disabled?: boolean }) => (
  <PaginationLink
    aria-label='Go to previous page'
    size='default'
    className={cn(
      "gap-1 rounded-l-lg border border-r bg-background/40 pl-2.5 hover:!border-l-0",
      className,
      disabled && "pointer-events-none cursor-not-allowed opacity-50",
    )}
    {...props}
  >
    <ChevronLeft className='h-4 w-4 text-primary-foreground' />
  </PaginationLink>
)
PaginationPrevious.displayName = "PaginationPrevious"

const PaginationNext = ({
  className,
  disabled,
  ...props
}: React.ComponentProps<typeof PaginationLink> & { disabled?: boolean }) => (
  <PaginationLink
    aria-label='Go to next page'
    size='default'
    className={cn(
      "gap-1 rounded-r-lg border border-l bg-background/40 pr-2.5 hover:!border-r-0",
      className,
      disabled && "pointer-events-none cursor-not-allowed opacity-50",
    )}
    {...props}
  >
    <ChevronRight className='h-4 w-4 text-primary-foreground' />
  </PaginationLink>
)
PaginationNext.displayName = "PaginationNext"

export {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
}

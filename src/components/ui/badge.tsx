import { cva, type VariantProps } from "class-variance-authority"
import * as React from "react"

import { cn } from "@/functions"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-[rgba(25,25,25,5)] text-primary-foreground",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        success:
          "bg-green-300 dark:bg-green-100 text-green-600 border-green-600",
        pending:
          "bg-orange-300 dark:bg-orange-200 text-orange-600 border-orange-600",
        danger: "bg-red-300 dark:bg-red-200 text-red-600 border-red-600",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  circle?: boolean
}

function Badge({ circle, className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props}>
      {circle && (
        <div className='mr-1.5 h-1.5 w-1.5 animate-spin rounded-full bg-gradient-to-t from-primary to-violet-400' />
      )}
      {props.children}
    </div>
  )
}

export { Badge, badgeVariants }

import React from "react"

import { cn } from "@/functions"

interface Props {
  size?: "sm" | "md" | "lg"
  className?: string
}

const LoadingIcon = ({ size = "sm", className }: Props) => {
  return (
    <div
      className={cn(
        "animate-loading rounded-full border-2 border-muted-foreground border-t-foreground",
        size === "sm" && "h-4 w-4",
        size === "md" && "h-6 w-6",
        size === "lg" && "h-8 w-8",
        className,
      )}
    />
  )
}

export default LoadingIcon

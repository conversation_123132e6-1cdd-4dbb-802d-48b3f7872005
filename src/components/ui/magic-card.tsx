"use client"

import React from "react"

import { cn } from "@/functions"

import { Particles } from "../ui/particles"

export interface Props extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  particles?: boolean
  count?: number
  className?: string
}

export default function MagicCard({
  children,
  particles = false,
  count = 20,
  className,
}: Props) {
  const onMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const { currentTarget } = e
    const rect = currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    currentTarget.style.setProperty("--pos-x", `${x}px`)
    currentTarget.style.setProperty("--pos-y", `${y}px`)
  }

  return (
    <div
      className={cn(
        "group relative overflow-hidden rounded-xl bg-card text-card-foreground lg:rounded-2xl",
        className,
      )}
      onMouseMove={onMouseMove}
    >
      <div className='relative z-10 h-full w-full'>
        {particles && (
          <Particles
            className='absolute inset-0 z-0 h-full w-full'
            quantity={count}
            ease={80}
            color='var(--muted-foreground)'
            refresh
          />
        )}
        {children}
      </div>
      {/* spotlight */}
      <div
        style={{
          background: `radial-gradient(circle at var(--pos-x) var(--pos-y), rgba(139,92,246,0.15), transparent)`,
        }}
        className='to-primary-light absolute inset-0 z-0 rounded-xl bg-gradient-to-br from-primary opacity-30 blur-lg transition-opacity group-hover:opacity-50 lg:rounded-2xl'
      ></div>
      <div className='absolute inset-0 z-0 rounded-xl border border-border opacity-50 transition-opacity group-hover:opacity-70 lg:rounded-2xl'></div>
    </div>
  )
}

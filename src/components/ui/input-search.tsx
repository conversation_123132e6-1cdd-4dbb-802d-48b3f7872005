import { Search } from "lucide-react"
import * as React from "react"

import { cn } from "@/functions"

import { Input } from "./input"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const InputSearch = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <div className='relative min-w-[204px]'>
        <Input
          type={type}
          className={cn("pl-10", className)}
          ref={ref}
          {...props}
        />
        <Search className='absolute left-3 top-3 h-4 w-4 shrink-0' />
      </div>
    )
  },
)
InputSearch.displayName = "Input"

export { InputSearch }

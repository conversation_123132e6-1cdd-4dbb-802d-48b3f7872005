"use client"

import { ThemeProvider as NextThemesProvider } from "next-themes"
import * as React from "react"

import { TooltipProvider } from "@/components/ui/tooltip"

export default function ThemeProvider({
  children,
  ...props
}: React.ComponentProps<typeof NextThemesProvider>) {
  return (
    <NextThemesProvider
      attribute='class'
      defaultTheme='dark'
      // enableSystem
      disableTransitionOnChange
      {...props}
    >
      <TooltipProvider>{children}</TooltipProvider>
    </NextThemesProvider>
  )
}

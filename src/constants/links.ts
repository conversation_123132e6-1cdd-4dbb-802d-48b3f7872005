import {
  BadgeIcon,
  ChartPieIcon,
  ComponentIcon,
  CreditCardIcon,
  IdCardIcon,
  LayoutIcon,
  LineChartIcon,
  LucideAppWindow,
  LucideIcon,
  MegaphoneIcon,
  MessageSquareTextIcon,
  PackageIcon,
  SettingsIcon,
  TableIcon,
  UserIcon,
  UsersIcon,
} from "lucide-react"

type Link = {
  title?: string
  href: string
  label: string
  icon: LucideIcon
  badge?: string
}
export interface SideLink extends Link {
  sub?: Link[]
}

export const DASHBOARD_LINKS_NEW = [
  {
    title: "Home",
    url: "",
    icon: LayoutIcon,
  },
  {
    title: "Hero",
    url: "/hero",
    icon: BadgeIcon,
  },
  {
    title: "Analytics",
    url: "/analytics",
    icon: ChartPieIcon,
    badge: "pro",
  },
  {
    title: "About",
    url: "/about",
    icon: UserIcon,
  },
  {
    title: "Projects",
    url: "/portfolio",
    icon: ComponentIcon,
  },
  {
    title: "Blogs",
    url: "/blogs",
    icon: PackageIcon,
    badge: "pro",
  },
  {
    title: "Expertise",
    url: "#",
    icon: IdCardIcon,
    items: [
      {
        title: "Skills/Technologies",
        url: "/expertise/skill",
      },
      {
        title: "Services",
        url: "/expertise/service",
      },
      // {
      //   title: "Languages",
      //   url: "/expertise/language",
      // },
    ],
  },
  {
    title: "Timeline",
    url: "#",
    icon: PackageIcon,
    items: [
      {
        title: "Experience",
        url: "/timeline/experience",
      },
      {
        title: "Education",
        url: "/timeline/education",
      },
      {
        title: "Achievement",
        url: "/timeline/achievement",
      },
      {
        title: "Milestone",
        url: "/timeline/milestone",
      },
    ],
  },
  {
    title: "Reviews",
    url: "/reviews",
    icon: UsersIcon,
  },
  {
    title: "Templates",
    url: "/templates",
    icon: IdCardIcon,
  },
  {
    title: "Gallery",
    url: "/gallery",
    icon: PackageIcon,
  },
  {
    title: "Leads",
    url: "/leads",
    icon: TableIcon,
    badge: "soon",
  },
  {
    title: "Plugins",
    url: "/plugins",
    icon: LucideAppWindow,
    badge: "soon",
  },
]

//dashboard links for the old sidebar own
export const DASHBOARD_LINKS: Link[] = [
  {
    href: "/",
    label: "Home",
    icon: LayoutIcon,
  },
  {
    href: "/hero",
    label: "Hero",
    icon: BadgeIcon,
  },
  {
    href: "/analytics",
    label: "Analytics",
    icon: ChartPieIcon,
    badge: "PRO",
  },
  {
    href: "/about",
    label: "About",
    icon: UserIcon,
  },
  {
    href: "/portfolio",
    label: "Projects",
    icon: ComponentIcon,
  },
  {
    href: "/blogs",
    label: "Blogs",
    icon: PackageIcon,
    badge: "PRO",
  },

  // {
  //   href: "/service-skills",
  //   label: "Service/Skills",
  //   icon: IdCardIcon,
  // },
  {
    href: "/expertise/skill",
    label: "Skills",
    icon: IdCardIcon,
  },
  {
    href: "/expertise/service",
    label: "Services",
    icon: IdCardIcon,
  },
  {
    href: "/expertise/language",
    label: "Languages",
    icon: IdCardIcon,
  },

  // {
  //   href: "/timeline",
  //   label: "Timeline",
  //   icon: PackageIcon,
  // },
  {
    href: "/timeline/experience",
    label: "Experience",
    icon: PackageIcon,
  },
  {
    href: "/timeline/education",
    label: "Education",
    icon: PackageIcon,
  },
  {
    href: "/timeline/achievement",
    label: "Achievement",
    icon: PackageIcon,
  },
  {
    href: "/timeline/milestone",
    label: "MileStone",
    icon: PackageIcon,
  },
  {
    href: "/reviews",
    label: "Reviews",
    icon: UsersIcon,
  },
  {
    href: "/templates",
    label: "Templates",
    icon: IdCardIcon,
  },
  {
    href: "/gallery",
    label: "Gallery",
    icon: PackageIcon,
    // badge: "PRO",
  },
  {
    href: "/table",
    label: "Table",
    icon: TableIcon,
  },
  {
    href: "/users",
    label: "Users",
    icon: UserIcon,
  },
]

export const SIDEBAR_LINKS: Link[] = [
  {
    href: "/all",
    label: "ALL",
    icon: MegaphoneIcon,
  },
  {
    href: "/templates",
    label: "Templates",
    icon: LineChartIcon,
  },
  {
    href: "/starter-kits",
    label: "Starter Kits",
    icon: MessageSquareTextIcon,
  },
  {
    href: "/landing-pages",
    label: "Landing Pages",
    icon: UsersIcon,
  },
  {
    href: "/themes",
    label: "Themes",
    icon: CreditCardIcon,
  },
  {
    href: "/blocks",
    label: "Blocks",
    icon: SettingsIcon,
  },
]

export const FOOTER_LINKS = [
  {
    title: "Product",
    links: [
      { name: "Home", href: "/" },
      { name: "Features", href: "/" },
      { name: "Pricing", href: "/" },
      { name: "Contact", href: "/" },
      { name: "Download", href: "/" },
    ],
  },
  {
    title: "Resources",
    links: [
      { name: "Blog", href: "/blog" },
      { name: "Help Center", href: "/help-center" },
      { name: "Community", href: "/community" },
      { name: "Guides", href: "/guides" },
    ],
  },
  {
    title: "Legal",
    links: [
      { name: "Privacy", href: "/privacy" },
      { name: "Terms", href: "/terms" },
      { name: "Cookies", href: "/cookies" },
    ],
  },
  {
    title: "Developers",
    links: [
      { name: "API Docs", href: "/api-docs" },
      { name: "SDKs", href: "/sdks" },
      { name: "Tools", href: "/tools" },
      { name: "Open Source", href: "/open-source" },
      { name: "Changelog", href: "/changelog" },
    ],
  },
]

import type { BlogFormData } from "@/types/blog"

export const sampleBlogData: BlogFormData = {
  title: "Sample Blog Post",
  slug: "",
  content:
    "<p>This is a sample blog post content. Replace it with your own amazing content!</p>",
  excerpt: "A brief summary of the sample blog post.",
  // categories: ["Sample", "Test"],
  tags: ["sample", "test", "example"],
  thumbnail: "https://source.unsplash.com/random/800x600",
  is_published: false,
  // published_at: new Date(),
  reading_time: "5 min read",
  featured: false,
  meta_title: "Sample Blog Post | Your Website",
  meta_description:
    "This is a sample blog post to demonstrate the blog management system.",
  is_enabled: true,
  website: "",
}

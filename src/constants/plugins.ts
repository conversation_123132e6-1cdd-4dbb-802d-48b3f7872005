export interface Plugin {
  id: string
  name: string
  description: string
  shortDescription: string
  category: string
  author: string
  version: string
  icon: string
  status: "active" | "inactive" | "deprecated"
  is_pro: boolean
  type: "widget" | "integration" | "utility" | "custom"
}

export const plugins: Plugin[] = [
  {
    id: "github-integration",
    name: "GitHub Integration",
    description:
      "Automatically fetch your repositories, contributions, and activity from GitHub. Showcase your projects and coding journey in an organized way.",
    shortDescription: "Connect and display GitHub repositories.",
    category: "Integration",
    author: "theportfolyo",
    version: "1.0.0",
    icon: "",
    status: "active",
    is_pro: false,
    type: "integration",
  },
  {
    id: "linkedin-sync",
    name: "LinkedIn Sync",
    description:
      "Seamlessly import your LinkedIn profile details, including experience, skills, and recommendations, to enrich your portfolio.",
    shortDescription: "Sync LinkedIn profile data.",
    category: "Integration",
    author: "theportfolyo",
    version: "1.0.0",
    icon: "",
    status: "active",
    is_pro: true,
    type: "integration",
  },
  {
    id: "ai-profile-generator",
    name: "AI Profile Generator",
    description:
      "Use AI to generate a professional and impactful portfolio summary based on your imported data. Highlight your unique skills and achievements effortlessly.",
    shortDescription: "AI-generated portfolio summaries.",
    category: "Utility",
    author: "theportfolyo",
    version: "2.0.0",
    icon: "",
    status: "active",
    is_pro: true,
    type: "utility",
  },
  {
    id: "custom-domain",
    name: "Custom Domain Support",
    description:
      "Link your portfolio to a custom domain to establish a personal brand and improve visibility.",
    shortDescription: "Connect a custom domain to your portfolio.",
    category: "Utilities",
    author: "theportfolyo",
    version: "1.2.0",
    icon: "",
    status: "active",
    is_pro: true,
    type: "utility",
  },
  {
    id: "theme-customizer",
    name: "Theme Customizer",
    description:
      "Personalize the look and feel of your portfolio with customizable themes, colors, fonts, and layouts.",
    shortDescription: "Fully customize your portfolio design.",
    category: "Customization",
    author: "theportfolyo",
    version: "3.1.0",
    icon: "",
    status: "active",
    is_pro: false,
    type: "widget",
  },
  {
    id: "analytics-dashboard",
    name: "Portfolio Analytics Dashboard",
    description:
      "Gain insights into your portfolio's performance, including visitor stats, engagement, and popular sections.",
    shortDescription: "Track portfolio performance and engagement.",
    category: "Analytics",
    author: "theportfolyo",
    version: "1.3.0",
    icon: "",
    status: "active",
    is_pro: true,
    type: "utility",
  },
]

export const categories = [
  { name: "All", count: 6 },
  { name: "Analytics", count: 1 },
  { name: "Integration", count: 2 },
  { name: "Utilities", count: 2 },
  { name: "Customization", count: 1 },
]

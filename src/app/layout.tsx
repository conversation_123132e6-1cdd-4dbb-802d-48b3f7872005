import "@/styles/globals.css"

import NextTopLoader from "nextjs-toploader"

import { Providers } from "@/components"
import { aeonik, inter } from "@/constants"
import { cn, generateMetadata } from "@/functions"

export const metadata = generateMetadata()

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang='en' suppressHydrationWarning>
      <body
        className={cn(
          "min-h-screen overflow-x-hidden bg-background font-default text-foreground antialiased !scrollbar-hide",
          inter.variable,
          aeonik.variable,
        )}
      >
        {/* <Script
          strategy="lazyOnload"
          src="https://embed.tawk.to/65cfb99f9131ed19d96dc858/1hmpmq694"
        /> */}
        <NextTopLoader showSpinner={false} height={3} color='#8c49ff' />

        <Providers>{children}</Providers>
      </body>
    </html>
  )
}

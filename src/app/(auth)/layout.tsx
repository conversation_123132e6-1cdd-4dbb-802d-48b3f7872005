"use client"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

import LogoLoading from "@/components/custom/logo-loading"
import { useUserStore } from "@/store/use-user-store"

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const router = useRouter()
  const { userLoading, isLoggedIn, fetchUser } = useUserStore()

  useEffect(() => {
    fetchUser()
  }, [fetchUser])

  useEffect(() => {
    if (!userLoading && isLoggedIn) {
      router.push("/") // Redirect to home if user is already logged in
    }
  }, [userLoading, isLoggedIn, router])

  // Display loading screen until we know whether the user is logged in
  if (userLoading || (isLoggedIn && !userLoading)) {
    return <LogoLoading />
  }

  return <>{children}</>
}

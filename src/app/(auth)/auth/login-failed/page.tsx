"use client"

import { ArrowR<PERSON>Circle, HomeIcon } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"

import { Container, Wrapper } from "@/components"
import { But<PERSON> } from "@/components/ui/button"

const LoginFailed = () => {
  const router = useRouter()

  return (
    <Wrapper>
      <Container className='mx-auto flex min-h-screen max-w-6xl flex-col-reverse items-center justify-center gap-5 px-6 py-12 md:flex-row'>
        {/* Content Section */}
        <div className='w-full lg:w-1/2'>
          <p className='text-sm font-medium text-blue-500 dark:text-blue-400'>
            404 error
          </p>
          <h1 className='mt-3 text-2xl font-semibold text-gray-800 dark:text-white md:text-3xl'>
            Login Failed
          </h1>
          <p className='mt-4 text-gray-500 dark:text-gray-400'>
            Sorry, something went wrong. Please try again.
          </p>
          <div className='mt-6 flex items-center gap-x-3'>
            {/* Go Back Button */}
            <Button onClick={() => router.back()} variant={"outline"}>
              <span>Login Page</span>
              <ArrowRightCircle size={16} className='ml-2' />
            </Button>
            {/* Home Button */}
            <Button asChild>
              <Link href='/'>
                <HomeIcon size={16} className='mr-2' />
                Take Me Home
              </Link>
            </Button>
          </div>
        </div>

        {/* Image Section */}
        <div className='relative mt-8 w-full lg:mt-0 lg:w-1/2'>
          <Image
            width={300}
            height={300}
            className='w-full rounded-lg object-cover'
            src='https://assets.theportfolyo.com/1705496474698-404-computer.svg'
            alt='404 Error Illustration'
          />
        </div>
      </Container>
    </Wrapper>
  )
}

export default LoginFailed

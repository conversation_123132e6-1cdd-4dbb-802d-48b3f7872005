"use client"
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import Link from "next/link"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

import { registerUser } from "@/actions/auth"
import AuthWrapper from "@/components/auth/auth-page-wrapper"
import OAuthButton from "@/components/auth/OAuthButton"
import { TextInput } from "@/components/form"
import Heading from "@/components/headings/heading"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Form } from "@/components/ui/form"

// Define the Zod registerSchema for form validation
const registerSchema = z
  .object({
    full_name: z.string().min(3, { message: "Full name is required" }),
    username: z
      .string()
      .min(3, { message: "Username must be at least 3 characters long" }),
    email: z.string().email({ message: "Invalid email address" }),
    phone_number: z
      .string()
      .optional()
      .refine((val) => !val || /^\+?[1-9]\d{1,14}$/.test(val), {
        message: "Invalid phone number",
      }),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters long" }),
    confirm_password: z.string().min(8, {
      message: "Password confirmation must be at least 8 characters long",
    }),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: "Passwords don't match",
    path: ["confirm_password"],
  })

type FormData = z.infer<typeof registerSchema>

export default function Register() {
  const router = useRouter()

  const initialState: FormData = {
    full_name: "",
    username: "",
    email: "",
    phone_number: "",
    password: "",
    confirm_password: "",
  }

  const form = useForm<FormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: initialState,
  })

  const handleSubmit = async (values: FormData) => {
    try {
      const res = await registerUser(values)

      if (res.success) {
        toast.success("Registration successful. Please login.")
        router.replace("/")
      } else {
        toast.error(res.message || "An error occurred. Please try again later.")
      }
    } catch (error) {
      console.error(error)
      toast.error("An error occurred. Please try again later.")
    }
  }

  return (
    <AuthWrapper>
      <div className='flex w-full overflow-y-auto py-2 scrollbar-hide'>
        <Card className='m-auto w-full p-6 shadow-sm md:p-12'>
          <CardHeader className='space-y-2 p-0 pb-6'>
            <Heading className='text-left text-xl md:text-3xl lg:text-4xl'>
              Getting started
            </Heading>
            <p className='font-medium leading-tight'>
              Create an account to build the portfolio.
            </p>
          </CardHeader>
          <CardContent className='space-y-[30px] p-0'>
            {/* OAuth Login Buttons */}
            <div className='grid grid-cols-2 gap-4'>
              <OAuthButton provider='google' />
              <OAuthButton
                provider='github'
                className='w-full border bg-black text-white hover:bg-black/30'
              />
            </div>

            <div className='flex items-center gap-2.5'>
              <span className='h-px w-full bg-[#E2E4E9]' />
              <p className='shrink-0 font-medium leading-tight'>
                or register with email
              </p>
              <span className='h-px w-full bg-[#E2E4E9]' />
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className='space-y-[30px]'
              >
                <div className='grid gap-4 md:grid-cols-2'>
                  <TextInput
                    control={form.control}
                    name='username'
                    label='Username'
                    placeholder='victoria123'
                    required
                  />
                  <TextInput
                    control={form.control}
                    name='full_name'
                    label='Full Name'
                    placeholder='Victoria Gillham'
                    required
                  />
                </div>

                <div className='grid gap-4 md:grid-cols-2'>
                  <TextInput
                    control={form.control}
                    name='email'
                    label='Email address'
                    type='email'
                    placeholder='<EMAIL>'
                    required
                  />
                  <TextInput
                    control={form.control}
                    name='phone_number'
                    label='Phone Number'
                    placeholder='+1234567890'
                  />
                </div>

                <div className='grid gap-4 md:grid-cols-2'>
                  <TextInput
                    control={form.control}
                    name='password'
                    label='Create password'
                    type='password'
                    placeholder='Abc*********'
                    required
                  />
                  <TextInput
                    control={form.control}
                    name='confirm_password'
                    label='Confirm password'
                    type='password'
                    placeholder='Abc*********'
                    required
                  />
                </div>

                <Link
                  href='/auth/forgot'
                  className='!mt-4 ml-auto block w-max text-right text-xs/4 font-semibold text-muted-foreground underline underline-offset-[3px] hover:text-[#3C3C3D]'
                >
                  Forgot password?
                </Link>

                <Button
                  disabled={form.formState.isSubmitting}
                  type='submit'
                  size='lg'
                  className='w-full'
                >
                  {form.formState.isSubmitting ? "Registering..." : "Register"}
                </Button>

                <div className='text-center text-xs/4 font-semibold text-foreground'>
                  Already have an account?{" "}
                  <Link
                    href='/auth/login'
                    className='pl-1.5 text-sm/tight underline underline-offset-4 hover:text-[#3C3C3D]'
                  >
                    Login
                  </Link>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </AuthWrapper>
  )
}

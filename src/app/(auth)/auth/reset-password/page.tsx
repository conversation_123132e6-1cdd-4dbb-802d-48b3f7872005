"use client"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

import { resetPassword } from "@/actions/auth"
import AuthWrapper from "@/components/auth/auth-page-wrapper"
import { TextInput } from "@/components/form"
import Heading from "@/components/headings/heading"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Form } from "@/components/ui/form"

// Define validation schema with Zod
const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .max(128, "Password cannot exceed 128 characters")
      .regex(
        /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@#$%^&*()_+\-=\[\]{};':"\\|,.<>/?]).{8,}$/,
        "Password must contain at least one letter, one number, and one special character",
      ),
    confirmPassword: z
      .string()
      .min(8, "Password must be at least 8 characters"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  })

type FormData = z.infer<typeof resetPasswordSchema>

export default function ResetPassword() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get("token")

  const initialState: FormData = {
    password: "",
    confirmPassword: "",
  }

  const form = useForm<FormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: initialState,
  })

  const handleSubmit = async (values: FormData) => {
    try {
      if (!token) {
        toast.error("Invalid reset token")
        return
      }

      const res = await resetPassword({ ...values, token })
      if (res.success) {
        toast.success("Password reset successful", {
          description: "You can now login with your new password.",
        })
        router.replace("/auth/login")
      } else {
        toast.error(res.message || "Failed to reset password", {
          description: res.message || "Please try again later.",
        })
      }
    } catch (error) {
      console.error(error)
      toast.error("An error occurred", {
        description: "Please try again later.",
      })
    }
  }

  return (
    <AuthWrapper>
      <div className='flex overflow-y-auto py-2'>
        <Card className='md:max-w[90%] m-auto w-full p-6 shadow-sm md:p-12'>
          <CardHeader className='space-y-2 p-0 pb-6'>
            <Heading className='text-left text-xl md:text-3xl lg:text-4xl'>
              Reset password
            </Heading>
            <p className='font-medium leading-tight'>
              Enter your new password below
            </p>
          </CardHeader>
          <CardContent className='p-0'>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className='space-y-[30px]'
              >
                <TextInput
                  control={form.control}
                  name='password'
                  label='New Password'
                  type='password'
                  placeholder='Enter new password'
                  required
                />

                <TextInput
                  control={form.control}
                  name='confirmPassword'
                  label='Confirm Password'
                  type='password'
                  placeholder='Confirm new password'
                  required
                />

                <div className='space-y-4'>
                  <Button
                    disabled={form.formState.isSubmitting}
                    type='submit'
                    size='lg'
                    className='w-full'
                  >
                    {form.formState.isSubmitting
                      ? "Resetting Password..."
                      : "Reset Password"}
                  </Button>

                  <Link
                    href='/auth/login'
                    className='flex items-center justify-center gap-2 pl-1.5 text-sm/tight font-semibold text-foreground hover:text-[#3C3C3D]'
                  >
                    <ArrowLeft className='size-[18px]' />
                    Back to Login
                  </Link>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </AuthWrapper>
  )
}

"use client"
import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

import { forgotPassword } from "@/actions/auth"
import AuthWrapper from "@/components/auth/auth-page-wrapper"
import { TextInput } from "@/components/form"
import Heading from "@/components/headings/heading"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Form } from "@/components/ui/form"

// Define validation schema with Zod
const forgotSchema = z.object({
  email: z
    .string()
    .email("Please enter a valid email address")
    .min(1, "Email is required")
    .max(255, "Email cannot exceed 255 characters"),
})

type FormData = z.infer<typeof forgotSchema>

export default function Forgot() {
  const router = useRouter()

  const initialState: FormData = {
    email: "",
  }

  const form = useForm<FormData>({
    resolver: zodResolver(forgotSchema),
    defaultValues: initialState,
  })

  const handleSubmit = async (values: FormData) => {
    try {
      const res = await forgotPassword(values)
      if (res.success) {
        toast.success("Password reset instructions sent", {
          description: "Please check your email for further instructions.",
        })
        router.replace("/auth/login")
      } else {
        toast.error("Failed to send reset instructions", {
          description: res.message || "Please try again later.",
        })
      }
    } catch (error) {
      console.error(error)
      toast.error("An error occurred", {
        description: "Please try again later.",
      })
    }
  }

  return (
    <AuthWrapper>
      <div className='flex overflow-y-auto py-2'>
        <Card className='md:max-w[90%] m-auto w-full p-6 shadow-sm md:p-12'>
          <CardHeader className='space-y-2 p-0 pb-6'>
            <Heading className='text-left text-xl md:text-3xl lg:text-4xl'>
              Forgot password
            </Heading>
            <p className='font-medium leading-tight'>
              Enter your email for password reset instructions.
            </p>
          </CardHeader>
          <CardContent className='p-0'>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className='space-y-[30px]'
              >
                <TextInput
                  control={form.control}
                  name='email'
                  label='Email address'
                  type='email'
                  placeholder='<EMAIL>'
                  required
                />

                <div className='space-y-4'>
                  <Button
                    disabled={form.formState.isSubmitting}
                    type='submit'
                    size='lg'
                    className='w-full'
                  >
                    {form.formState.isSubmitting ? "Continuing..." : "Continue"}
                  </Button>

                  <Link
                    href='/auth/login'
                    className='flex items-center justify-center gap-2 pl-1.5 text-sm/tight font-semibold text-foreground hover:text-[#3C3C3D]'
                  >
                    <ArrowLeft className='size-[18px]' />
                    Back to Login
                  </Link>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </AuthWrapper>
  )
}

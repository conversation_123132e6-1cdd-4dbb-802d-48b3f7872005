"use client"
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

import { loginUser } from "@/actions/auth"
import AuthWrapper from "@/components/auth/auth-page-wrapper"
import OAuthButton from "@/components/auth/OAuthButton"
import { TextInput } from "@/components/form"
import Heading from "@/components/headings/heading"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Form } from "@/components/ui/form"

// Define validation schema with Zod
const loginSchema = z.object({
  email: z
    .string()
    .email("Please enter a valid email address")
    .min(1, "Email is required")
    .max(255, "Email cannot exceed 255 characters"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .max(128, "Password cannot exceed 128 characters"),
})

type FormData = z.infer<typeof loginSchema>

export default function Login() {
  const router = useRouter()

  const initialState: FormData = {
    email: "",
    password: "",
  }

  const form = useForm<FormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: initialState,
  })

  const handleSubmit = async (values: FormData) => {
    try {
      const res = await loginUser(values)
      if (res.success) {
        toast.success("Login successful")
        router.replace("/")
      } else {
        toast.error(res.message || "Login failed")
      }
    } catch (error) {
      console.error(error)
      toast.error("An error occurred. Please try again later.")
    }
  }

  return (
    <AuthWrapper>
      <div className='flex h-full w-full overflow-y-auto py-2 scrollbar-hide'>
        <Card className='m-auto w-full p-6 shadow-sm md:p-12'>
          <CardHeader className='space-y-2 p-0 pb-6'>
            <Heading className='text-left text-xl md:text-3xl lg:text-4xl'>
              Sign In to your account
            </Heading>
            <p className='font-medium leading-tight'>
              Enter your details to proceed further
            </p>
          </CardHeader>
          <CardContent className='space-y-[30px] p-0'>
            {/* OAuth Login Buttons */}
            <div className='grid grid-cols-2 gap-4'>
              <OAuthButton provider='google' />
              <OAuthButton
                provider='github'
                className='w-full border bg-black text-white hover:bg-black/30'
              />
            </div>

            <div className='flex items-center gap-2.5'>
              <span className='h-px w-full bg-[#E2E4E9]' />
              <p className='shrink-0 font-medium leading-tight'>
                or login with email
              </p>
              <span className='h-px w-full bg-[#E2E4E9]' />
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className='space-y-[30px]'
              >
                <TextInput
                  control={form.control}
                  name='email'
                  label='Email address'
                  type='email'
                  placeholder='<EMAIL>'
                  required
                />

                <TextInput
                  control={form.control}
                  name='password'
                  label='Password'
                  type='password'
                  placeholder='Abc*********'
                  required
                />

                <Link
                  href='/auth/forgot'
                  className='!mt-4 block text-right text-xs/4 font-semibold text-foreground underline underline-offset-[3px] hover:text-[#3C3C3D]'
                >
                  Forgot password?
                </Link>

                <Button
                  disabled={form.formState.isSubmitting}
                  type='submit'
                  size='lg'
                  className='w-full'
                >
                  {form.formState.isSubmitting ? "Logging in..." : "Login"}
                </Button>

                <div className='text-center text-xs/4 font-semibold text-foreground'>
                  Don&apos;t have an account?{" "}
                  <Link
                    href='/auth/register'
                    className='pl-1.5 text-sm/tight underline underline-offset-4 hover:text-[#3C3C3D]'
                  >
                    Register
                  </Link>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </AuthWrapper>
  )
}

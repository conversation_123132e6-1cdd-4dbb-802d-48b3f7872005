"use client"

import { useQuery } from "@tanstack/react-query"
import { useRouter, useSearchParams } from "next/navigation"
import { Suspense } from "react"
import { toast } from "sonner"

import { verifyEmail } from "@/actions/auth"
import LogoLoading from "@/components/custom/logo-loading" // Updated loader component

const VerifyEmailPage = () => {
  const router = useRouter()
  const searchParam = useSearchParams()

  const token = searchParam.get("token")

  const { isLoading } = useQuery({
    queryKey: ["verifyEmail", token],
    queryFn: async () => {
      if (!token) {
        toast.error("Token not found")
        throw new Error("Token not found")
      }

      const data = await verifyEmail(token)
      if (data) {
        toast.success("Logged in Successfully!")
        const returnUrl = sessionStorage.getItem("returnUrl") ?? "/"
        router.push(returnUrl)
      } else {
        toast.error("Something went wrong during authentication.")
        router.push("/auth/login-failed")
      }
    },
    enabled: !!token, // Only run the query if the token exists
    retry: false, // Disable retries for this query
  })

  if (isLoading) {
    return (
      <div className='flex h-full min-h-screen w-full flex-col items-center justify-center gap-5'>
        <Suspense fallback={<LogoLoading />}>
          <LogoLoading />
          <p className='block text-foreground'>Verifying...</p>
        </Suspense>
      </div>
    )
  }

  return null // No UI needed after the process
}

export default VerifyEmailPage

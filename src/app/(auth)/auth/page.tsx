"use client"

import { useQuery } from "@tanstack/react-query"
import { useRouter, useSearchParams } from "next/navigation"
import { toast } from "sonner"

import { getLoggedInUser } from "@/actions/users"
import LogoLoading from "@/components/custom/logo-loading"
import { useUserStore } from "@/store/use-user-store"

const AuthPage = () => {
  const router = useRouter()
  const searchParam = useSearchParams()
  const token = searchParam.get("token")
  const { setUser, setIsLoggedIn } = useUserStore()

  const { isLoading } = useQuery({
    queryKey: ["authUser", token],
    queryFn: async () => {
      if (token) {
        localStorage.setItem("token", token)
      } else {
        throw new Error("Token not found")
      }

      const data = await getLoggedInUser()
      if (data) {
        toast.success("Logged in Successfully!")
        setIsLoggedIn(true)
        setUser(data)
        const returnUrl = sessionStorage.getItem("returnUrl") ?? "/"
        router.push(returnUrl)
      } else {
        toast.error("Something went wrong during authentication.")
        router.push("/auth/login-failed")
      }
      return data
    },
    enabled: !!token, // Only run the query if the token is available
    retry: false, // Prevent retries if the query fails
    // onError: () => {
    //   toast.error("Something went wrong during authentication.");
    //   router.push("/auth/login-failed");
    // },
  })

  if (isLoading) {
    return (
      <div className='flex h-full min-h-screen w-full flex-col items-center justify-center gap-5'>
        <LogoLoading />
        <p className='block text-white'>Logging...</p>
      </div>
    )
  }

  return null // Render nothing once the process is complete
}

export default AuthPage

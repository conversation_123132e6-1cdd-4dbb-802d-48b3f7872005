"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { motion } from "framer-motion"
import { Briefcase, DollarSign, MapPin } from "lucide-react"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

import { <PERSON>rap<PERSON> } from "@/components"
import CommonHero from "@/components/main/reusable-hero"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

const jobOpenings = [
  {
    id: 1,
    title: "Frontend Developer",
    department: "Engineering",
    location: "Remote",
    type: "Full-time",
    salary: "$80,000 - $120,000",
  },
  {
    id: 2,
    title: "UX Designer",
    department: "Design",
    location: "New York, NY",
    type: "Full-time",
    salary: "$70,000 - $100,000",
  },
  {
    id: 3,
    title: "Product Manager",
    department: "Product",
    location: "San Francisco, CA",
    type: "Full-time",
    salary: "$100,000 - $150,000",
  },
]

const formSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z.string().min(10, { message: "Valid phone number is required" }),
  position: z.string().min(1, { message: "Please select a position" }),
  resume: z.instanceof(File).refine((file) => file.size <= 5000000, {
    message: "Resume must be less than 5MB",
  }),
  coverLetter: z.string().min(1, { message: "Cover letter is required" }),
})

export default function CareersPage() {
  const [selectedJob, setSelectedJob] = useState<number | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      position: "",
      coverLetter: "",
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    // In a real application, you would send this data to your server
    console.log(values)
    toast.success("Application submitted successfully!", {
      description: "We'll review your application and get back to you soon.",
    })
    form.reset()
    setSelectedJob(null)
    setIsDialogOpen(false)
  }

  return (
    <Wrapper className=''>
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className='px=4 isolate mx-auto min-h-screen w-full p-4 md:px-0'
      >
        <CommonHero
          className='mb-8 pt-16 text-center md:pt-24'
          title={"Careers"}
          description='Join our team and make a difference.'
        />

        <div
          aria-hidden='true'
          className='absolute inset-x-0 top-[-10rem] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[-20rem]'
        >
          <div
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
            className='relative right-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]'
          />
        </div>

        <div className='mx-auto max-w-4xl'>
          <motion.div
            className='mb-8 w-full'
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className='bg-'>
              <CardHeader>
                <CardTitle>Open Positions</CardTitle>
                <CardDescription>
                  Explore our current job openings and find your perfect role
                </CardDescription>
              </CardHeader>
              <CardContent className='p-2 md:p-4'>
                <div className='space-y-4'>
                  {jobOpenings.map((job) => (
                    <Card key={job.id} className='bg-'>
                      <CardHeader>
                        <CardTitle>{job.title}</CardTitle>
                        <CardDescription>{job.department}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className='flex flex-col space-y-2'>
                          <div className='flex items-center space-x-2'>
                            <MapPin className='h-4 w-4 text-muted-foreground' />
                            <span>{job.location}</span>
                          </div>
                          <div className='flex items-center space-x-2'>
                            <Briefcase className='h-4 w-4 text-muted-foreground' />
                            <span>{job.type}</span>
                          </div>
                          <div className='flex items-center space-x-2'>
                            <DollarSign className='h-4 w-4 text-muted-foreground' />
                            <span>{job.salary}</span>
                          </div>
                        </div>
                      </CardContent>
                      <CardContent>
                        <Button
                          onClick={() => {
                            setSelectedJob(job.id)
                            setIsDialogOpen(true)
                          }}
                          variant='white'
                        >
                          Apply Now
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className='max-h-[80vh] w-[90%] overflow-auto md:max-w-3xl'>
              <DialogHeader>
                <DialogTitle>
                  Apply for{" "}
                  {jobOpenings.find((job) => job.id === selectedJob)?.title}
                </DialogTitle>
                <DialogDescription>
                  Please fill out the form below to apply for this position
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className='space-y-6'
                >
                  <FormField
                    control={form.control}
                    name='name'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input placeholder='Your full name' {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='email'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            type='email'
                            placeholder='Your email'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='phone'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone</FormLabel>
                        <FormControl>
                          <Input
                            type='tel'
                            placeholder='Your phone number'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='position'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Position</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Select a position' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {jobOpenings.map((job) => (
                              <SelectItem key={job.id} value={job.title}>
                                {job.title}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='resume'
                    // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    render={({ field: { onChange, ...rest } }) => (
                      <FormItem>
                        <FormLabel>Resume</FormLabel>
                        <FormControl>
                          <Input
                            type='file'
                            accept='.pdf,.doc,.docx'
                            onChange={(e) => {
                              const file = e.target.files?.[0]
                              if (file) onChange(file)
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='coverLetter'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cover Letter</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Tell us why you're a great fit for this role"
                            className='h-32 resize-none'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type='submit' className='w-full'>
                    {form.formState.isSubmitting
                      ? "Submitting..."
                      : "Submit Application"}
                  </Button>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </motion.section>
    </Wrapper>
  )
}

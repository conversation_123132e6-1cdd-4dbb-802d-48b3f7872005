import { CheckCircleIcon, MinusCircleIcon } from "lucide-react"

import { cn } from "@/lib/utils"
import { Plan } from "@/types/plans"

interface PlanComparisonProps {
  plans: Plan[]
  currentPlan: string
}

export function PlanComparison({ plans, currentPlan }: PlanComparisonProps) {
  // Filter out free plans
  const filteredPlans: Plan[] = plans.filter((plan) => plan.type !== "free")

  // Collect all unique feature texts across plans
  const allFeatures: string[] = Array.from(
    new Set(
      filteredPlans.flatMap((plan) =>
        plan.metadata?.features.map((f: any) => f.text),
      ),
    ),
  )

  return (
    <div className='mt-16 overflow-x-auto'>
      <table className='w-full border-collapse'>
        <thead>
          <tr>
            <th className='bg-muted p-4 text-left'>Feature</th>
            {filteredPlans.map((plan) => (
              <th
                key={plan._id}
                className={cn(
                  "bg-muted p-4 text-center",
                  plan._id === currentPlan &&
                    "bg-primary text-primary-foreground",
                )}
              >
                {plan.name}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {allFeatures.map((feature, index) => (
            <tr
              key={index}
              className={index % 2 === 0 ? "bg-background" : "bg-muted/50"}
            >
              <td className='p-4'>{feature}</td>
              {filteredPlans.map((plan) => {
                const featureIncluded = plan.metadata?.features.find(
                  (f: any) => f.text === feature,
                )?.included

                return (
                  <td key={plan._id} className='p-4 text-center'>
                    {featureIncluded ? (
                      <CheckCircleIcon className='mx-auto h-5 w-5 text-green-500' />
                    ) : (
                      <MinusCircleIcon className='mx-auto h-5 w-5 text-red-500' />
                    )}
                  </td>
                )
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

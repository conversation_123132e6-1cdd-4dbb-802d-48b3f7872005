"use client"

import { useQuery } from "@tanstack/react-query"
import { useParams, useRouter } from "next/navigation"
import { useEffect } from "react"
import { toast } from "sonner"

import { handleZeroPayment } from "@/actions/payment"
import { getPlans } from "@/actions/plans"
import { getWebsiteById } from "@/actions/website"
import { Container, Wrapper } from "@/components"
import { ErrorDisplay } from "@/components/main/error-display"
import { PricingPlans } from "@/components/main/new-pricing"
import { PricingPlansSkeleton } from "@/components/main/pricing-plan-skeletion"
import { SectionBadge } from "@/components/ui/section-bade"
import { Spotlight } from "@/components/ui/spotlight"
import { useUserStore } from "@/store/use-user-store"
import { Coupon } from "@/types/checkout"
import { Plan } from "@/types/plans"
import { handlePayment } from "@/utils/razorpay"

export default function UpgradePage() {
  const params = useParams<{ website: string }>()
  const router = useRouter()
  const { user, userLoading, isLoggedIn, fetchUser } = useUserStore()

  useEffect(() => {
    fetchUser()
  }, [fetchUser])

  useEffect(() => {
    if (!userLoading && !isLoggedIn) {
      router.push("/auth/login")
    }
  }, [userLoading, isLoggedIn, router])

  const plansQuery = useQuery({
    queryKey: ["plans"],
    queryFn: getPlans,
  })

  const websiteQuery = useQuery({
    queryKey: ["website", params.website],
    queryFn: () => getWebsiteById(params.website),
    enabled: !!params.website && isLoggedIn,
  })

  const handleUpgrade = async (
    selectedPlan: Plan,
    billingCycle: "monthly" | "yearly" | "lifetime",
    coupon: Coupon | null,
    finalPrice: number,
  ) => {
    if (!user || !websiteQuery.data) {
      toast.error("Please ensure you're logged in and the website is loaded.")
      return
    }

    const websiteId = websiteQuery.data._id
    try {
      if (finalPrice <= 0) {
        toast.error("Please contact support or Refresh the page and try again.")
        return
      }

      if (finalPrice > 0) {
        await handlePayment(user, websiteId, selectedPlan, billingCycle, coupon)
      }

      if (finalPrice === 0 && coupon) {
        await handleZeroPayment(websiteId, selectedPlan, coupon)
      }

      toast.success("Upgrade successful!")
    } catch (error) {
      console.error("Upgrade failed:", error)
      toast.error("Failed to process the upgrade. Please try again.")
    }
  }

  const isLoading =
    plansQuery.isLoading || websiteQuery.isLoading || userLoading
  const isError = plansQuery.isError || websiteQuery.isError
  const errorMessage =
    plansQuery.error?.message ||
    websiteQuery.error?.message ||
    "An unexpected error occurred."

  if (userLoading || (!isLoggedIn && !userLoading)) {
    return <PricingPlansSkeleton />
  }

  return (
    <Wrapper className='relative py-20'>
      <div className='absolute left-[calc(55%-379px/2)] top-[-266px] -z-10 hidden h-[620px] w-[379px] rounded-full bg-primary/60 opacity-50 blur-[10rem] lg:flex'></div>
      <div className='absolute left-[calc(50%-433px/2)] top-[-60px] -z-10 hidden h-[525px] w-[433px] rounded-full bg-primaryLight/40 opacity-50 blur-[15rem] lg:flex'></div>

      <Container className='relative w-full py-12 md:py-16 lg:py-20'>
        <Spotlight
          className='-top-40 left-0 md:-top-20 md:left-60'
          fill='rgba(255, 255, 255, 0.5)'
        />

        <div className='mx-auto mb-12 flex max-w-xl flex-col items-center text-center'>
          <SectionBadge title='Upgrade your plan' />
          <h2 className='mt-6 font-heading text-2xl font-medium md:text-4xl lg:text-5xl'>
            Upgrade Your Plan for {/* gradient text */}
            <span className='ml-2 bg-gradient-to-tr from-black to-white bg-clip-text font-heading font-bold tracking-normal text-transparent dark:from-yellow-400 dark:to-orange-400'>
              {websiteQuery.data?.name || "Your Website"}
            </span>
          </h2>
          <p className='my-6 text-base text-accent-foreground/80 md:text-lg'>
            Choose the plan that suits your needs. No hidden fees, no surprises.
          </p>
        </div>

        {isLoading && <PricingPlansSkeleton />}

        {isError && (
          <ErrorDisplay
            message={errorMessage}
            onRetry={() => {
              plansQuery.refetch()
              websiteQuery.refetch()
            }}
          />
        )}

        {!isLoading && !isError && plansQuery.data && websiteQuery.data && (
          <>
            <PricingPlans
              plans={plansQuery.data}
              currentPlan={websiteQuery.data.plan}
              isUpgradePage={true}
              onUpgrade={handleUpgrade}
            />

            {/* <PlanComparison
              plans={plansQuery.data}
              currentPlan={websiteQuery.data.plan}
            /> */}
          </>
        )}
      </Container>
    </Wrapper>
  )
}

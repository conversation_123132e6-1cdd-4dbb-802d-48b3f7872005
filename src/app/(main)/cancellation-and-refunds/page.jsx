import React from "react"

import Heading from "@/components/headings/heading"

export const metadata = {
  title: "Cancellation and Refunds",
}

const CancellationAndRefundsPage = () => {
  return (
    <main className='h-full min-h-screen w-full px-5 pt-20 md:pt-28'>
      <div className='mx-auto w-full max-w-5xl pb-20'>
        {/* Top Heading */}

        <Heading>Cancellation and Refunds</Heading>

        {/* Page Content */}
        <div className='w-full space-y-5 text-pretty text-muted-foreground dark:text-muted-foreground'>
          {/* Section 1: Introduction */}
          <section className='py-3 pt-6'>
            <PrivacySubHeading text={"1. Introduction"} />
            <PrivacyParagraph
              text={
                "We value your satisfaction and strive to provide a transparent and fair cancellation and refund policy for our services. Whether you are on a paid subscription or enjoying our 30 days free trial, we want to ensure you have a positive experience with our platform."
              }
            />
          </section>

          {/* Section 2: Cancellation Process */}
          <section className='py-5'>
            <PrivacySubHeading text={"2. Cancellation Process"} />
            <PrivacyParagraph
              text={
                "If you decide to cancel your subscription, you can do so at any time through your account settings. We do not have any long-term commitments, and you are free to cancel your subscription whenever you choose."
              }
            />
          </section>

          {/* Section 3: Refund Policy */}
          <section className='py-3'>
            <PrivacySubHeading text={"3. Refund Policy"} />
            <PrivacyParagraph
              text={
                "For our monthly subscription plans, we offer a 30 days free trial, allowing you to explore and experience our platform before committing. During this trial period, if you decide to cancel, you will not be charged. Once the trial period ends, and you are billed for the subscription, refunds are not provided for the current billing cycle. You will continue to have access to our services until the end of the billing cycle."
              }
            />
          </section>

          {/* Section 4: Exceptional Cases */}
          <section className='py-3'>
            <PrivacySubHeading text={"4.Refunds in Exceptional Scenarios"} />
            <PrivacyParagraph
              text={
                "We acknowledge that unforeseen circumstances can occur. Should you find yourself in a unique situation that necessitates a refund, please contact our customer support team. We will carefully evaluate each request on an individual basis and endeavor to reach a fair resolution. The following are examples of exceptional cases mentioned for your information."
              }
            />

            <ul className='flex list-disc flex-col gap-2 pl-3 text-sm md:text-base'>
              <li>
                Technical Issues: If you encounter significant technical issues
                that prevent you from accessing or using our services
                effectively, and our support team is unable to resolve the issue
                within a reasonable timeframe, you may be eligible for a refund.
              </li>

              <li>
                Service Disruptions: In the event of prolonged service
                disruptions or outages that significantly impact your ability to
                use our services as intended, we may consider issuing a refund
                for the affected period.
              </li>

              <li>
                Unauthorized Charges: If you believe there has been an
                unauthorized charge or billing error related to your
                subscription, please contact our support team immediately for
                assistance. We will investigate the issue and provide a
                resolution, which may include a refund if the charge is deemed
                unauthorized.
              </li>

              <li>
                Cancellation within Trial Period: For new subscribers, if you
                cancel your subscription within the specified trial period (if
                applicable), you may be eligible for a refund of the
                subscription fee, minus any usage charges or fees incurred
                during the trial period.
              </li>
            </ul>
          </section>
        </div>
      </div>
    </main>
  )
}

const PrivacySubHeading = ({ text }) => {
  return (
    <h2 className='mb-2 text-xl font-semibold text-primary-foreground md:text-2xl'>
      {text}
    </h2>
  )
}

const PrivacyParagraph = ({ text }) => {
  return (
    <p className='py-2 text-sm text-muted-foreground md:text-base'>{text}</p>
  )
}

export default CancellationAndRefundsPage

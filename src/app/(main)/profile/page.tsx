"use client"

import { motion } from "framer-motion"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

import { <PERSON><PERSON><PERSON>, Wrapper } from "@/components"
import BillingSection from "@/components/account/billing-section"
import NotificationPreferences from "@/components/account/notificatoin-preferences"
import ProfileSettings from "@/components/account/profile-setting"
import SecuritySettings from "@/components/account/security-settings"
import LogoLoading from "@/components/custom/logo-loading"
import CommonHero from "@/components/main/reusable-hero"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useUserStore } from "@/store/use-user-store"

export default function ProfilePage() {
  const router = useRouter()
  const { userLoading, isLoggedIn, user, fetchUser } = useUserStore()

  useEffect(() => {
    if (!isLoggedIn) {
      fetchUser()
    }
  }, [isLoggedIn, fetchUser])

  useEffect(() => {
    // Redirect to login if user is not logged in and we're done loading
    if (!userLoading && !isLoggedIn) {
      router.push("/auth/login")
    }
  }, [userLoading, isLoggedIn, router])

  // Show loading screen while checking auth status
  if (userLoading) {
    return <LogoLoading />
  }

  // Don't render anything while redirecting
  if (!isLoggedIn) {
    return null
  }

  return (
    <Wrapper>
      <Container>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className='min-h-screen'
        >
          <CommonHero
            title='My Profile'
            description={`Welcome back, ${user?.full_name || "User"}`}
          />

          <div className='z-10 mx-auto max-w-4xl pt-8 md:pt-10'>
            <Card>
              <CardHeader>
                <CardTitle>Profile Settings</CardTitle>
                <CardDescription>
                  Manage your profile, security, and notification preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue='profile' className='w-full'>
                  <TabsList className='grid w-full grid-cols-4'>
                    <TabsTrigger value='profile'>Profile</TabsTrigger>
                    <TabsTrigger value='security'>Security</TabsTrigger>
                    <TabsTrigger value='notifications'>
                      Notifications
                    </TabsTrigger>
                    <TabsTrigger value='billing'>Billing</TabsTrigger>
                  </TabsList>

                  <TabsContent value='profile' className='mt-6'>
                    <ProfileSettings />
                  </TabsContent>

                  <TabsContent value='security' className='mt-6'>
                    <SecuritySettings />
                  </TabsContent>

                  <TabsContent value='notifications' className='mt-6'>
                    <NotificationPreferences />
                  </TabsContent>

                  <TabsContent value='billing' className='mt-6'>
                    <BillingSection />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </motion.div>
      </Container>
    </Wrapper>
  )
}

import Script from "next/script"
import React from "react"

import { Footer, Navbar } from "@/components"

interface Props {
  children: React.ReactNode
}

const MainLayout = ({ children }: Props) => {
  return (
    <>
      <Script
        strategy='lazyOnload'
        src='https://embed.tawk.to/65cfb99f9131ed19d96dc858/1hmpmq694'
      />

      <Navbar />

      {children}

      <Footer />
    </>
  )
}

export default MainLayout

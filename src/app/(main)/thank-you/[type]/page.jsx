"use client"
// pages/ThankYouPage.js
import { motion } from "framer-motion"
import Link from "next/link"
import { useParams } from "next/navigation"

import { Container, Wrapper } from "@/components"
import { ConfettiEffect } from "@/components/custom/confetti-effect"
import { But<PERSON> } from "@/components/ui/button" // Assuming you're using ShadCN's Button component
import { Spotlight } from "@/components/ui/spotlight"
import { cn } from "@/lib/utils"

const ThankYouPage = () => {
  const { type } = useParams()
  console.log(type)

  return (
    <Wrapper>
      <Container className='relative flex min-h-screen items-center justify-center py-20 md:py-32'>
        {/* Red gradient effect */}

        {/* gradient background effect */}
        <div className='absolute left-[calc(55%-379px/2)] top-[-266px] -z-10 hidden h-[620px] w-[379px] rounded-full bg-primary/60 opacity-50 blur-[10rem] lg:flex'></div>
        <div className='absolute left-[calc(50%-433px/2)] top-[-60px] -z-10 hidden h-[525px] w-[433px] rounded-full bg-primaryLight/40 opacity-50 blur-[15rem] lg:flex'></div>

        {/* spotlight effect */}
        <Spotlight
          className='-top-40 left-0 md:-top-20 md:left-60'
          fill='rgba(255, 255, 255, 0.5)'
        />

        {type !== "failed" && <ConfettiEffect />}
        {/* <div className="absolute bottom-0 left-0 right-0 top-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:14px_24px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)]"></div> */}

        {/* Main Content */}
        {type === "failed" ? (
          <motion.div
            className='relative z-10 mx-auto flex w-full max-w-7xl flex-col items-center justify-center gap-4 p-5 py-44 text-center md:gap-8 md:py-32'
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <h1
              className={cn(
                "mx-auto max-w-5xl bg-gradient-to-r from-primary to-red-500 bg-clip-text text-center text-4xl font-bold text-transparent sm:text-5xl lg:text-6xl xl:text-7xl",
              )}
            >
              Payment Failed
            </h1>
            <h2 className='mx-auto my-3 max-w-5xl text-base font-bold text-slate-900 dark:text-white sm:text-lg lg:text-xl xl:text-3xl'>
              Your payment was not successful. <br /> Please try again.
            </h2>
            <Link href={`/portfolios/general`}>
              <Button
                variant='destructive' // Using ShadCN's outline variant for the button
              >
                Explore More Portfolios
              </Button>
            </Link>
          </motion.div>
        ) : (
          <motion.div
            className='relative z-10 mx-auto flex w-full max-w-7xl flex-col items-center justify-center gap-4 p-5 py-44 text-center md:gap-8 md:py-32'
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <h1
              className={cn(
                "mx-auto max-w-5xl bg-gradient-to-r from-purple-400 to-red-500 bg-clip-text text-center text-4xl font-bold text-transparent sm:text-5xl lg:text-6xl xl:text-7xl",
              )}
            >
              Thank you for <br /> your Order
            </h1>
            <h2 className='mx-auto max-w-5xl text-base font-normal text-primary-foreground sm:text-lg lg:text-xl xl:text-3xl'>
              Now you can go to your dashboard and start building <br /> your
              portfolio. Just click below to Go to Dashboard.
            </h2>
            <Link href={`/dashboard`}>
              <Button>Go to Dasbhoard</Button>
            </Link>
          </motion.div>
        )}
      </Container>
    </Wrapper>
  )
}

export default ThankYouPage

"use client"

import { motion } from "framer-motion"

import { Wrapper } from "@/components"
import BillingSection from "@/components/account/billing-section"
import NotificationPreferences from "@/components/account/notificatoin-preferences"
import ProfileSettings from "@/components/account/profile-setting"
import SecuritySettings from "@/components/account/security-settings"
import Heading from "@/components/headings/heading"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function AccountPage() {
  return (
    <Wrapper className=''>
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className='isolate mx-auto min-h-screen w-full pt-24'
      >
        <div className='mb-5 w-full text-center md:mb-10'>
          <Heading className='pb-0'>Account Settings</Heading>
          <p className='text-sm text-muted-foreground'>
            Manage your account preferences and information from here
          </p>
        </div>

        <div
          aria-hidden='true'
          className='absolute inset-x-0 top-[-10rem] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[-20rem]'
        >
          <div
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
            className='relative right-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]'
          />
        </div>

        <div className='mx-auto max-w-4xl'>
          <Card>
            <CardHeader>
              <CardTitle>Account Settings</CardTitle>
              <CardDescription>
                Manage your profile, billing, security, and notification
                preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue='profile' className='w-full'>
                <TabsList className='grid w-full grid-cols-4'>
                  <TabsTrigger value='profile'>Profile</TabsTrigger>
                  <TabsTrigger value='billing'>Billing</TabsTrigger>
                  <TabsTrigger value='security'>Security</TabsTrigger>
                  <TabsTrigger value='notifications'>Notifications</TabsTrigger>
                </TabsList>
                <TabsContent value='profile'>
                  <ProfileSettings />
                </TabsContent>
                <TabsContent value='billing'>
                  <BillingSection />
                </TabsContent>
                <TabsContent value='security'>
                  <SecuritySettings />
                </TabsContent>
                <TabsContent value='notifications'>
                  <NotificationPreferences />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </motion.section>
    </Wrapper>
  )
}

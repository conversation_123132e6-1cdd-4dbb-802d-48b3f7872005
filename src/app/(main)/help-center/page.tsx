"use client"

import { AnimatePresence, motion } from "framer-motion"
import { ChevronDown, ChevronRight, Mail, Search } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

import { Wrapper } from "@/components"
import CommonHero from "@/components/main/reusable-hero"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { faqCategories } from "@/constants/faq"

export default function HelpCenterPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null)

  const filteredCategories = faqCategories
    .map((category) => ({
      ...category,
      faqs: category.faqs.filter(
        (faq) =>
          faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
          faq.answer.toLowerCase().includes(searchTerm.toLowerCase()),
      ),
    }))
    .filter((category) => category.faqs.length > 0)

  const handleCategoryClick = (categoryId: string) => {
    setExpandedCategory(expandedCategory === categoryId ? null : categoryId)
  }

  return (
    <Wrapper className=''>
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className='isolate mx-auto min-h-screen w-full p-4 md:px-0'
      >
        <CommonHero
          className='mb-8 pt-16 text-center md:pt-24'
          title={"Help Center"}
          description='Find answers to your questions or contact our support team'
        />

        <div
          aria-hidden='true'
          className='absolute inset-x-0 top-[-10rem] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[-20rem]'
        >
          <div
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
            className='relative right-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]'
          />
        </div>

        <div className='mx-auto max-w-3xl'>
          <Card className='mb-8'>
            <CardHeader>
              <CardTitle>Search FAQs</CardTitle>
              <CardDescription>
                Find answers to your questions quickly
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='relative'>
                <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
                <Input
                  placeholder='Search for answers...'
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className='pl-8'
                />
              </div>
            </CardContent>
          </Card>

          <AnimatePresence>
            {filteredCategories.map((category) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className='mb-4'>
                  <CardHeader
                    className='cursor-pointer'
                    onClick={() => handleCategoryClick(category.id)}
                  >
                    <CardTitle className='flex items-center justify-between'>
                      {category.name}
                      {expandedCategory === category.id ? (
                        <ChevronDown className='h-5 w-5' />
                      ) : (
                        <ChevronRight className='h-5 w-5' />
                      )}
                    </CardTitle>
                  </CardHeader>
                  <AnimatePresence>
                    {expandedCategory === category.id && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <CardContent>
                          <Accordion
                            type='single'
                            collapsible
                            className='w-full'
                          >
                            {category.faqs.map((faq) => (
                              <AccordionItem key={faq.id} value={faq.id}>
                                <AccordionTrigger>
                                  {faq.question}
                                </AccordionTrigger>
                                <AccordionContent>
                                  {faq.answer}
                                </AccordionContent>
                              </AccordionItem>
                            ))}
                          </Accordion>
                        </CardContent>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>

          {filteredCategories.length === 0 && (
            <Card>
              <CardHeader>
                <CardTitle>No results found</CardTitle>
                <CardDescription>
                  We couldn&apos;t find any FAQs matching your search. Try a
                  different term or contact our support team.
                </CardDescription>
              </CardHeader>
            </Card>
          )}

          <Card className='mt-8'>
            <CardHeader>
              <CardTitle>Still need help?</CardTitle>
              <CardDescription>
                Our support team is here to assist you
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className='w-full'>
                <Link href='/contact'>
                  <Mail className='mr-2 h-4 w-4' />
                  Contact Support
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </motion.section>
    </Wrapper>
  )
}

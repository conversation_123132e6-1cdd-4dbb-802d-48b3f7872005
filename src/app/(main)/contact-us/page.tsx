"use client"
// import emailjs from 'emailjs-com'
import emailjs, { EmailJSResponseStatus } from "@emailjs/browser"
import { zodResolver } from "@hookform/resolvers/zod"
import { motion } from "framer-motion"
// import { Heading } from '@/components/ui/heading'
import { Mail, MapPin } from "lucide-react"
import { useForm } from "react-hook-form"
// import { toast } from '@/components/ui/use-toast'
import { toast } from "sonner"
import { z } from "zod"

import { Wrapper } from "@/components"
import CommonHero from "@/components/main/reusable-hero"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

const formSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  subject: z.string().min(1, { message: "Subject is required" }),
  message: z.string().min(1, { message: "Message is required" }),
})

function Contact() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      subject: "",
      message: "",
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      await emailjs.send(
        process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID!,
        process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID!,
        values,
        { publicKey: process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY },
      )

      // .send('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', templateParams, {
      //   publicKey: 'YOUR_PUBLIC_KEY',
      // })

      toast.success("Message sent successfully!", {
        description: "We'll get back to you as soon as possible.",
      })
      form.reset()
    } catch (error) {
      if (error instanceof EmailJSResponseStatus) {
        console.log("EMAILJS FAILED...", error)
        return
      }
      console.error("Error sending email:", error)
      toast.error("Failed to send message. Please try again later.")
    }
  }

  return (
    <Wrapper className=''>
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className='isolate mx-auto min-h-screen w-full p-4 md:px-0'
      >
        <CommonHero
          className='mb-8 pt-16 text-center md:pt-24'
          title={"Contact us"}
          description=" We'd love to talk about how we can help you."
        />

        <div
          aria-hidden='true'
          className='absolute inset-x-0 top-[-10rem] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[-20rem]'
        >
          <div
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
            className='relative right-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]'
          />
        </div>

        <div className='grid gap-8 md:grid-cols-2 md:px-5'>
          <motion.div
            className='w-full space-y-6'
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className='border-card bg-card'>
              {/* <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900 dark:to-indigo-950"> */}
              <CardHeader>
                {/* <CardTitle>Contact Information</CardTitle> */}
                <CardTitle className='font-heading text-xl md:text-3xl'>
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex items-center space-x-3'>
                  <Mail className='h-5 w-5 text-blue-600' />
                  <span><EMAIL></span>
                </div>

                <div className='flex items-center space-x-3'>
                  <MapPin className='h-5 w-5 text-blue-600' />
                  <span>123 Main St, Anytown, USA 12345</span>
                </div>
              </CardContent>
            </Card>

            {/* <Card className="bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900 dark:to-pink-950"> */}
            <Card className='border border-card bg-card'>
              <CardHeader>
                <CardTitle className='font-heading text-xl md:text-3xl'>
                  Office Hours
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-2'>
                <p>Monday - Friday: 9AM - 5PM</p>
                <p>Saturday: 10AM - 2PM</p>
                <p>Sunday: Closed</p>
              </CardContent>
            </Card>
          </motion.div>

          {/* form */}
          <motion.div
            className='w-full'
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card className='bg-card'>
              <CardHeader>
                <CardTitle>Send us a message</CardTitle>
                <CardDescription>
                  Fill out the form below and we&apos;ll get back to you as soon
                  as possible.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className='space-y-6'
                  >
                    <FormField
                      control={form.control}
                      name='name'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Name</FormLabel>
                          <FormControl>
                            <Input placeholder='Your name' {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name='email'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input
                              type='email'
                              placeholder='Your email'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name='subject'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Subject</FormLabel>
                          <FormControl>
                            <Input
                              placeholder='Subject of your message'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name='message'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Message</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder='Your message here...'
                              className='h-32 resize-none'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button
                      type='submit'
                      // className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
                      className='w-full'
                    >
                      {/* Send Message */}
                      {form.formState.isSubmitting
                        ? "Sending..."
                        : "Send Message"}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </motion.section>
    </Wrapper>
  )
}

export default Contact

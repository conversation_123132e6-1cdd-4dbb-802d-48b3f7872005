import Heading from "@/components/headings/heading"

export const metadata = {
  title: "Terms and Conditions",
}

const TermsAndConditionsPage = () => {
  return (
    <main className='h-full min-h-screen w-full px-5 pt-20 md:pt-28'>
      <section className='mx-auto w-full max-w-5xl pb-20'>
        {/* top  */}

        <Heading>Terms and Conditions</Heading>

        {/* top end */}

        <div className='w-full space-y-5 text-pretty text-muted-foreground dark:text-muted-foreground'>
          {/* Section 1: Service Overview */}
          <section className='py-3 pt-6'>
            <TermsSubHeading text={"1. Service Overview"} />

            <TermsParagraph
              text={`Welcome to ThePortfolyo, your go-to destination for creating stunning personal portfolio websites! Whether you're a developer, photographer, makeup artist, professor, or anyone looking to showcase their work, we've got you covered. Our platform handles everything from coding and hosting to maintenance and SSL, so you can focus on what you do best while we take care of the technical details.`}
            />
            <ul className='flex list-disc flex-col gap-2 pl-3 text-sm md:text-base'>
              <li>
                {" "}
                Browse through our diverse collection of portfolio website
                templates and find the perfect match for your style and
                profession. Once you&apos;ve found your ideal template,
                purchasing is a breeze, and you&apos;ll receive access to your
                personal dashboard. This dashboard empowers you to update your
                website effortlessly, ensuring that your online presence is
                always up-to-date and impressive.
              </li>
              <li>
                At ThePortfolyo, we&apos;re committed to making your experience
                seamless and enjoyable. Get started today and elevate your
                online portfolio to new heights!
              </li>

              <li>
                We provide portfolio services on a monthly basis. By using our
                services, you agree to comply with and be bound by the following
                terms and conditions.
              </li>
            </ul>
          </section>

          {/* Section 2: Pricing and Payments */}
          <section className='py-5'>
            <TermsSubHeading text={"2. Pricing and Payments"} />
            <TermsParagraph
              text={
                "Our portfolio services are offered at a very affordable price, ensuring that you get excellent value for your investment. Please note that our services are non-refundable once the monthly subscription fee is processed. Payments are processed securely, and you will be billed monthly for the subscription."
              }
            />
          </section>
          <section className='py-5'>
            <TermsSubHeading text={"3. Refunds in Exceptional Scenarios"} />
            <TermsParagraph
              text={`We acknowledge that unforeseen circumstances can occur. Should you find yourself in a unique situation that necessitates a refund, please contact our customer support team. We will carefully evaluate each request on an individual basis and endeavor to reach a fair resolution. The following are examples of exceptional cases mentioned for your information.`}
            />

            <ul className='flex list-disc flex-col gap-2 pl-3 text-sm md:text-base'>
              <li>
                Technical Issues: If you encounter significant technical issues
                that prevent you from accessing or using our services
                effectively, and our support team is unable to resolve the issue
                within a reasonable timeframe, you may be eligible for a refund.
              </li>

              <li>
                Service Disruptions: In the event of prolonged service
                disruptions or outages that significantly impact your ability to
                use our services as intended, we may consider issuing a refund
                for the affected period.
              </li>

              <li>
                Unauthorized Charges: If you believe there has been an
                unauthorized charge or billing error related to your
                subscription, please contact our support team immediately for
                assistance. We will investigate the issue and provide a
                resolution, which may include a refund if the charge is deemed
                unauthorized.
              </li>

              <li>
                Cancellation within Trial Period: For new subscribers, if you
                cancel your subscription within the specified trial period (if
                applicable), you may be eligible for a refund of the
                subscription fee, minus any usage charges or fees incurred
                during the trial period.
              </li>
            </ul>
          </section>

          {/* Section 3: User Responsibilities */}
          <section className='py-3'>
            <TermsSubHeading text={" 4. User Responsibilities"} />
            <TermsParagraph
              text={
                "As a user, you are responsible for maintaining the confidentiality of your account information. You agree to provide accurate and up-to-date information during the account creation process."
              }
            />
          </section>

          {/* Section 4: Non-Refundable Policy */}
          <section className='py-3'>
            <TermsSubHeading text={"5. Non-Refundable Policy"} />
            <TermsParagraph
              text={
                "Our portfolio services are non-refundable. Once the monthly subscription fee is processed, no refunds will be provided. We recommend reviewing our services before subscribing."
              }
            />
          </section>

          {/* Section 5: Modifications to Services */}
          <section className='py-3'>
            <TermsSubHeading text={"6. Modifications to Services"} />
            <TermsParagraph
              text={
                "We reserve the right to modify or discontinue our services at any time without prior notice. ThePortfolyo shall not be liable to you or any third party for any modification, price change, suspension, or discontinuation of the services."
              }
            />
          </section>

          {/* Section 6: Contact Us */}
          <section>
            <TermsSubHeading text={"7. Contact Us"} />
            <TermsParagraph
              text={
                "If you have any questions or concerns about these terms and conditions, please contact <NAME_EMAIL>."
              }
            />
          </section>

          {/* Section 7: Data Usage for Newsletters and Emails */}
          <section className='py-3'>
            <TermsSubHeading
              text={"8. Data Usage for Newsletters and Emails"}
            />
            <TermsParagraph
              text={
                "By subscribing to our newsletters and emails, you consent to the use of your provided data for the purpose of sending you relevant updates, promotions, and information about our services. We respect your privacy and assure you that your information will not be shared with third parties without your explicit consent."
              }
            />
          </section>
        </div>
      </section>
    </main>
  )
}

const TermsSubHeading = ({ text }) => {
  return (
    <h2 className='mb-2 text-xl font-semibold text-primary-foreground md:text-2xl'>
      {text}
    </h2>
  )
}
const TermsParagraph = ({ text }) => {
  return (
    <p className='py-2 text-sm text-muted-foreground md:text-base'>{text}</p>
  )
}

export default TermsAndConditionsPage

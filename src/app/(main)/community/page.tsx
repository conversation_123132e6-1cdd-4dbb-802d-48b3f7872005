"use client"

import { Award, Briefcase, Calendar, MessageSquare, Users } from "lucide-react"
import { useState } from "react"

import { <PERSON><PERSON><PERSON>, Wrapper } from "@/components"
import CommonHero from "@/components/main/reusable-hero"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function CommunityPage() {
  const [activeTab, setActiveTab] = useState("showcase")
  console.log(activeTab)

  return (
    <>
      <CommonHero
        className='mb-8 pt-16 text-center md:pt-24'
        title={"Join Our Thriving \n Portfolio Community"}
        description='  Connect, learn, and grow with fellow creators. Share your work, get
          inspired, and take your portfolio to the next level.'
      />

      <Wrapper className='relative z-[1] py-10'>
        <Container className='relative'>
          <Tabs
            defaultValue='showcase'
            className='w-full'
            onValueChange={setActiveTab}
          >
            <TabsList className='grid w-full grid-cols-3 lg:grid-cols-6'>
              <TabsTrigger value='showcase'>Showcase</TabsTrigger>
              <TabsTrigger value='discussions'>Discussions</TabsTrigger>
              <TabsTrigger value='events'>Events</TabsTrigger>
              <TabsTrigger value='resources'>Resources</TabsTrigger>
              <TabsTrigger value='mentorship'>Mentorship</TabsTrigger>
              <TabsTrigger value='jobs'>Job Board</TabsTrigger>
            </TabsList>
            <TabsContent value='showcase'>
              <ShowcaseTab />
            </TabsContent>
            <TabsContent value='discussions'>
              <DiscussionsTab />
            </TabsContent>
            <TabsContent value='events'>
              <EventsTab />
            </TabsContent>
            <TabsContent value='resources'>
              <ResourcesTab />
            </TabsContent>
            <TabsContent value='mentorship'>
              <MentorshipTab />
            </TabsContent>
            <TabsContent value='jobs'>
              <JobBoardTab />
            </TabsContent>
          </Tabs>
        </Container>
      </Wrapper>
    </>
  )
}

const ShowcaseTab = () => {
  const featuredPortfolios = [
    {
      id: 1,
      name: "Alice Johnson",
      title: "UX Designer",
      image: "/avatars/alice.jpg",
      likes: 245,
      views: 1200,
    },
    {
      id: 2,
      name: "Bob Smith",
      title: "Full-Stack Developer",
      image: "/avatars/bob.jpg",
      likes: 189,
      views: 980,
    },
    {
      id: 3,
      name: "Carol Williams",
      title: "Graphic Designer",
      image: "/avatars/carol.jpg",
      likes: 302,
      views: 1500,
    },
  ]

  return (
    <div className='mt-6 grid gap-6'>
      <h2 className='text-2xl font-semibold'>Featured Portfolios</h2>
      <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
        {featuredPortfolios.map((portfolio) => (
          <Card key={portfolio.id} className='overflow-hidden'>
            <CardHeader className='p-0'>
              <div className='aspect-video bg-gradient-to-r from-purple-400 via-pink-500 to-red-500'></div>
            </CardHeader>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-4'>
                <Avatar className='h-12 w-12'>
                  <AvatarImage src={portfolio.image} alt={portfolio.name} />
                  <AvatarFallback>
                    {portfolio.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle>{portfolio.name}</CardTitle>
                  <CardDescription>{portfolio.title}</CardDescription>
                </div>
              </div>
            </CardContent>
            <CardFooter className='flex justify-between'>
              <div className='flex items-center space-x-2'>
                <Award className='h-4 w-4' />
                <span>{portfolio.likes} likes</span>
              </div>
              <div className='flex items-center space-x-2'>
                <Users className='h-4 w-4' />
                <span>{portfolio.views} views</span>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
      <Button className='mt-4 w-full'>View All Portfolios</Button>
    </div>
  )
}

const DiscussionsTab = () => {
  const topics = [
    {
      id: 1,
      title: "Best practices for responsive design",
      author: "Emma Davis",
      replies: 23,
      views: 156,
    },
    {
      id: 2,
      title: "How to showcase multiple projects effectively",
      author: "Liam Wilson",
      replies: 18,
      views: 132,
    },
    {
      id: 3,
      title: "Tips for writing a compelling bio",
      author: "Sophia Chen",
      replies: 31,
      views: 204,
    },
  ]

  return (
    <div className='mt-6 grid gap-6'>
      <div className='flex items-center justify-between'>
        <h2 className='text-2xl font-semibold'>Recent Discussions</h2>
        <Button variant='outline'>Start a Discussion</Button>
      </div>
      <div className='grid gap-4'>
        {topics.map((topic) => (
          <Card key={topic.id}>
            <CardHeader>
              <CardTitle>{topic.title}</CardTitle>
              <CardDescription>Started by {topic.author}</CardDescription>
            </CardHeader>
            <CardFooter className='flex justify-between'>
              <div className='flex items-center space-x-2'>
                <MessageSquare className='h-4 w-4' />
                <span>{topic.replies} replies</span>
              </div>
              <div className='flex items-center space-x-2'>
                <Users className='h-4 w-4' />
                <span>{topic.views} views</span>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
      <Button variant='outline' className='mt-4 w-full'>
        View All Discussions
      </Button>
    </div>
  )
}

const EventsTab = () => {
  const events = [
    {
      id: 1,
      title: "Portfolio Review Workshop",
      date: "2023-07-15",
      time: "14:00 GMT",
      host: "David Brown",
    },
    {
      id: 2,
      title: "Personal Branding Webinar",
      date: "2023-07-22",
      time: "18:00 GMT",
      host: "Sarah Johnson",
    },
    {
      id: 3,
      title: "Networking for Creatives",
      date: "2023-07-29",
      time: "20:00 GMT",
      host: "Michael Lee",
    },
  ]

  return (
    <div className='mt-6 grid gap-6'>
      <h2 className='text-2xl font-semibold'>Upcoming Events</h2>
      <div className='grid gap-4'>
        {events.map((event) => (
          <Card key={event.id}>
            <CardHeader>
              <CardTitle>{event.title}</CardTitle>
              <CardDescription>Hosted by {event.host}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='flex items-center space-x-2'>
                <Calendar className='h-4 w-4' />
                <span>
                  {event.date} at {event.time}
                </span>
              </div>
            </CardContent>
            <CardFooter>
              <Button className='w-full'>Register</Button>
            </CardFooter>
          </Card>
        ))}
      </div>
      <Button variant='outline' className='mt-4 w-full'>
        View All Events
      </Button>
    </div>
  )
}

const ResourcesTab = () => {
  const resources = [
    {
      id: 1,
      title: "10 Must-Have Elements for Your Portfolio",
      type: "Article",
      author: "Emily White",
    },
    {
      id: 2,
      title: "Portfolio SEO Optimization Guide",
      type: "PDF",
      author: "Alex Turner",
    },
    {
      id: 3,
      title: "Creating a Cohesive Visual Style",
      type: "Video",
      author: "Olivia Green",
    },
  ]

  return (
    <div className='mt-6 grid gap-6'>
      <h2 className='text-2xl font-semibold'>Popular Resources</h2>
      <div className='grid gap-4'>
        {resources.map((resource) => (
          <Card key={resource.id}>
            <CardHeader>
              <CardTitle>{resource.title}</CardTitle>
              <CardDescription>By {resource.author}</CardDescription>
            </CardHeader>
            <CardFooter className='flex items-center justify-between'>
              <Badge>{resource.type}</Badge>
              <Button variant='outline'>Access Resource</Button>
            </CardFooter>
          </Card>
        ))}
      </div>
      <Button variant='outline' className='mt-4 w-full'>
        Browse All Resources
      </Button>
    </div>
  )
}

const MentorshipTab = () => {
  const mentors = [
    {
      id: 1,
      name: "Jessica Lee",
      expertise: "UX/UI Design",
      image: "/avatars/jessica.jpg",
      availability: "Available",
    },
    {
      id: 2,
      name: "Ryan Chen",
      expertise: "Front-end Development",
      image: "/avatars/ryan.jpg",
      availability: "Busy",
    },
    {
      id: 3,
      name: "Amelia Patel",
      expertise: "Branding & Identity",
      image: "/avatars/amelia.jpg",
      availability: "Available",
    },
  ]

  return (
    <div className='mt-6 grid gap-6'>
      <h2 className='text-2xl font-semibold'>Featured Mentors</h2>
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
        {mentors.map((mentor) => (
          <Card key={mentor.id}>
            <CardHeader>
              <div className='flex items-center space-x-4'>
                <Avatar className='h-12 w-12'>
                  <AvatarImage src={mentor.image} alt={mentor.name} />
                  <AvatarFallback>
                    {mentor.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle>{mentor.name}</CardTitle>
                  <CardDescription>{mentor.expertise}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardFooter className='flex items-center justify-between'>
              <Badge
                variant={
                  mentor.availability === "Available" ? "default" : "secondary"
                }
              >
                {mentor.availability}
              </Badge>
              <Button variant='outline'>Request Mentorship</Button>
            </CardFooter>
          </Card>
        ))}
      </div>
      <Button variant='outline' className='mt-4 w-full'>
        View All Mentors
      </Button>
    </div>
  )
}

const JobBoardTab = () => {
  const jobs = [
    {
      id: 1,
      title: "Senior UX Designer",
      company: "TechCorp",
      location: "Remote",
      type: "Full-time",
    },
    {
      id: 2,
      title: "Front-end Developer",
      company: "WebSolutions",
      location: "New York, NY",
      type: "Contract",
    },
    {
      id: 3,
      title: "Graphic Designer",
      company: "CreativeStudio",
      location: "London, UK",
      type: "Part-time",
    },
  ]

  return (
    <div className='mt-6 grid gap-6'>
      <div className='flex items-center justify-between'>
        <h2 className='text-2xl font-semibold'>Latest Job Openings</h2>
        <Button variant='outline'>Post a Job</Button>
      </div>
      <div className='grid gap-4'>
        {jobs.map((job) => (
          <Card key={job.id}>
            <CardHeader>
              <CardTitle>{job.title}</CardTitle>
              <CardDescription>{job.company}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='flex items-center space-x-2'>
                <Briefcase className='h-4 w-4' />
                <span>{job.location}</span>
              </div>
            </CardContent>
            <CardFooter className='flex items-center justify-between'>
              <Badge>{job.type}</Badge>
              <Button>Apply Now</Button>
            </CardFooter>
          </Card>
        ))}
      </div>
      <Button variant='outline' className='mt-4 w-full'>
        View All Jobs
      </Button>
    </div>
  )
}

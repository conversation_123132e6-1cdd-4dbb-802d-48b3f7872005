import { ChevronRight } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Button } from "@/components/ui/button"
import { plugins } from "@/constants/plugins"

export default function PluginPage({ params }: { params: { id: string } }) {
  const plugin = plugins.find((p) => p.id === params.id)

  if (!plugin) {
    notFound()
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-8 flex items-center gap-2 text-sm text-muted-foreground'>
        <Link href='/plugins' className='hover:text-foreground'>
          Plugins
        </Link>
        <ChevronRight className='h-4 w-4' />
        <span>{plugin.category}</span>
      </div>

      <div className='grid gap-6 lg:grid-cols-[300px_1fr]'>
        <div className='space-y-4'>
          <Image
            src={plugin.icon}
            alt={plugin.name}
            width={300}
            height={300}
            className='rounded-lg border bg-background'
          />
          <Button className='w-full' size='lg'>
            Install plugin
          </Button>
        </div>

        <div className='space-y-8'>
          <div>
            <h1 className='text-4xl font-bold'>{plugin.name}</h1>
            <p className='mt-2 text-xl text-muted-foreground'>
              {plugin.description}
            </p>
          </div>

          <div className='space-y-4'>
            <h2 className='text-2xl font-semibold'>Key Features</h2>
            <ul className='list-inside list-disc space-y-2'>
              <li>Feature 1</li>
              <li>Feature 2</li>
              <li>Feature 3</li>
            </ul>
          </div>

          <div className='space-y-4'>
            <h2 className='text-2xl font-semibold'>Installation guide</h2>
            <ol className='list-inside list-decimal space-y-2'>
              <li>Step 1</li>
              <li>Step 2</li>
              <li>Step 3</li>
            </ol>
          </div>

          <Accordion type='single' collapsible className='w-full'>
            <AccordionItem value='free-trial'>
              <AccordionTrigger>Does it have a free trial?</AccordionTrigger>
              <AccordionContent>
                Yes, you can try this plugin for free for 14 days.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value='pricing'>
              <AccordionTrigger>How does the pricing work?</AccordionTrigger>
              <AccordionContent>
                We offer flexible pricing plans based on your needs.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </div>
  )
}

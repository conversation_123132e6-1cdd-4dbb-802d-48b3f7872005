import { Container, Wrapper } from "@/components"
import { PluginCard } from "@/components/cards/plugin-card"
import CommonHero from "@/components/main/reusable-hero"
import { plugins } from "@/constants/plugins"

import { Categories } from "./categories"
import { SearchInput } from "./search-input"

export default function PluginsPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const search =
    typeof searchParams.search === "string" ? searchParams.search : ""
  const category =
    typeof searchParams.category === "string" ? searchParams.category : "All"

  const filteredPlugins = plugins.filter((plugin) => {
    const matchesSearch = search
      ? plugin.name.toLowerCase().includes(search.toLowerCase()) ||
        plugin.description.toLowerCase().includes(search.toLowerCase())
      : true
    const matchesCategory =
      category === "All" ? true : plugin.category === category
    return matchesSearch && matchesCategory
  })

  return (
    <>
      <CommonHero
        className='mb-8 pt-16 text-center md:pt-24'
        title={"Powerful Plugins \n for Your Portfolio"}
        description='Enhance your portfolio with our curated selection of plugins. From
          marketing tools to analytics, these integrations will help you create
          a more powerful and engaging online presence.'
      />

      <Wrapper className='relative py-10'>
        <Container className='relative'>
          {/* <Spotlight
            className="-top-40 left-0 md:left-60 md:-top-20"
            fill="rgba(255, 255, 255, 0.5)"
          /> */}

          <div className='relative mb-8 flex flex-col gap-6 md:flex-row'>
            {/* <div className="flex flex-col md:flex-row gap-6 mb-8 relative"> */}
            <div className='top-20 md:sticky md:w-1/4'>
              <Categories />
            </div>
            <div className='md:w-3/4'>
              <SearchInput />
              <div className='grid grid-cols-1 gap-6 pt-4 md:grid-cols-2 lg:grid-cols-2'>
                {filteredPlugins.map((plugin) => (
                  <PluginCard key={plugin.id} plugin={plugin} />
                ))}
              </div>
            </div>
          </div>
        </Container>
      </Wrapper>
    </>
  )
}

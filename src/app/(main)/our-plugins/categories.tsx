"use client"

import { useRouter, useSearchParams } from "next/navigation"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { categories } from "@/constants/plugins"

export function Categories() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const currentCategory = searchParams.get("category") || "All"

  return (
    <Card>
      <CardContent className='py-3 md:py-5'>
        <CardTitle className='mb-2 font-semibold md:mb-5'>Categories</CardTitle>
        <div className='flex flex-col space-y-2'>
          {categories.map((category) => (
            <Button
              key={category.name}
              variant={currentCategory === category.name ? "default" : "ghost"}
              className='justify-start'
              onClick={() => {
                const params = new URLSearchParams(searchParams)
                params.set("category", category.name)
                router.push(`/our-plugins?${params.toString()}`)
              }}
            >
              {category.name}
              <span className='ml-auto'>{category.count}</span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

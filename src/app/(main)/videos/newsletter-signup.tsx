"use client"

import { motion } from "framer-motion"
import { useState } from "react"
import { toast } from "sonner"

import { Container } from "@/components"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"

export const NewsletterSignup = () => {
  const [email, setEmail] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically send the email to your backend
    console.log("Subscribing email:", email)
    toast.success("Thank you for subscribing to ThePortfolyo Newsletter!")
    setEmail("")
  }

  return (
    <Container className='mx-auto max-w-3xl py-16'>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className='bg-primary text-primary-foreground'>
          <CardHeader>
            <CardTitle className='text-center text-2xl md:text-3xl'>
              Subscribe to ThePortfolyo Newsletter
            </CardTitle>
            <CardDescription className='text-center text-primary-foreground/80'>
              Get the latest portfolio tips, tricks, and trends delivered
              straight to your inbox.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form
              onSubmit={handleSubmit}
              className='flex flex-col gap-4 md:flex-row'
            >
              <Input
                type='email'
                placeholder='Enter your email'
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className='bg-primary-foreground text-primary'
                required
              />
              <Button type='submit' variant='white' className='md:w-auto'>
                Subscribe
              </Button>
            </form>
          </CardContent>
          <CardFooter className='text-center text-sm text-primary-foreground/60'>
            We respect your privacy. Unsubscribe at any time.
          </CardFooter>
        </Card>
      </motion.div>
    </Container>
  )
}

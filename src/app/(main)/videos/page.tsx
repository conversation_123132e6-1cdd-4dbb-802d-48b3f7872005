"use client"

import { motion } from "framer-motion"
import { <PERSON>Play, <PERSON>, Play, Tag } from "lucide-react"
import Image from "next/image"
import { useState } from "react"

import { <PERSON><PERSON><PERSON>, Wrapper } from "@/components"
import CommonHero from "@/components/main/reusable-hero"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"

import { NewsletterSignup } from "./newsletter-signup"
// import { NewsletterSignup } from "./newsletter-signup";

type Video = {
  id: number
  title: string
  category: string
  duration: string
  views: number
  thumbnail: string
  author: {
    name: string
    avatar: string
  }
}

const videoCategories = [
  "All",
  "Portfolio Design",
  "Web Development",
  "UI/UX",
  "Career Tips",
  "Freelancing",
]

const videos: Video[] = [
  {
    id: 1,
    title: "Creating a Stunning Portfolio Homepage",
    category: "Portfolio Design",
    duration: "15:30",
    views: 12500,
    thumbnail: "/video-thumbnails/portfolio-homepage.jpg",
    author: {
      name: "<PERSON> <PERSON>",
      avatar: "/avatars/alex.jpg",
    },
  },
  {
    id: 2,
    title: "Responsive Design Techniques for Portfolios",
    category: "Web Development",
    duration: "22:45",
    views: 9800,
    thumbnail: "/video-thumbnails/responsive-design.jpg",
    author: {
      name: "Emma Davis",
      avatar: "/avatars/emma.jpg",
    },
  },
  {
    id: 3,
    title: "Crafting an Effective About Me Page",
    category: "UI/UX",
    duration: "18:20",
    views: 15200,
    thumbnail: "/video-thumbnails/about-me-page.jpg",
    author: {
      name: "Michael Lee",
      avatar: "/avatars/michael.jpg",
    },
  },
  {
    id: 4,
    title: "Showcasing Your Best Work: Project Pages",
    category: "Portfolio Design",
    duration: "20:15",
    views: 11000,
    thumbnail: "/video-thumbnails/project-pages.jpg",
    author: {
      name: "Sophia Chen",
      avatar: "/avatars/sophia.jpg",
    },
  },
  {
    id: 5,
    title: "Optimizing Your Portfolio for Job Searches",
    category: "Career Tips",
    duration: "25:40",
    views: 18700,
    thumbnail: "/video-thumbnails/portfolio-optimization.jpg",
    author: {
      name: "David Brown",
      avatar: "/avatars/david.jpg",
    },
  },
  {
    id: 6,
    title: "Freelancer's Guide to Portfolio Presentation",
    category: "Freelancing",
    duration: "28:10",
    views: 14300,
    thumbnail: "/video-thumbnails/freelancer-portfolio.jpg",
    author: {
      name: "Olivia Green",
      avatar: "/avatars/olivia.jpg",
    },
  },
]

export default function VideosPage() {
  const [activeCategory, setActiveCategory] = useState("All")

  const filteredVideos =
    activeCategory === "All"
      ? videos
      : videos.filter((video) => video.category === activeCategory)

  return (
    <>
      {/* <Hero /> */}
      <CommonHero
        className='mb-8 pt-16 text-center md:pt-24'
        title={"Learn and Grow with \n Portfolyo Tutorials"}
        description='Discover expert tips, tricks, and strategies to create stunning
          portfolios and advance your career through our curated video
          tutorials.'
      />
      <Wrapper className='relative z-[1] py-10'>
        <Container className='relative'>
          {/* <Spotlight
            className="-top-40 left-0 md:left-60 md:-top-20"
            fill="rgba(255, 255, 255, 0.5)"
          /> */}
          <Tabs
            defaultValue='All'
            className='w-full'
            onValueChange={setActiveCategory}
          >
            <TabsList className='mb-8 grid w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-6'>
              {videoCategories.map((category) => (
                <TabsTrigger key={category} value={category}>
                  {category}
                </TabsTrigger>
              ))}
            </TabsList>
            <motion.div
              className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {filteredVideos.map((video) => (
                <VideoCard key={video.id} video={video} />
              ))}
            </motion.div>
          </Tabs>
        </Container>
      </Wrapper>
      <NewsletterSignup />
    </>
  )
}

interface VideoCardProps {
  video: Video
}

const VideoCard = ({ video }: VideoCardProps) => {
  return (
    <motion.div whileHover={{ scale: 1.03 }} transition={{ duration: 0.2 }}>
      <Card className='overflow-hidden'>
        <CardHeader className='p-0'>
          <div className='relative'>
            <Image
              // src={video.thumbnail ?? "https://vercel.com/api/www/avatar"}
              width={300}
              height={300}
              src={"https://vercel.com/api/www/avatar"}
              alt={video.title}
              className='aspect-video w-full object-cover'
            />
            <div className='absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 opacity-0 transition-opacity duration-300 hover:opacity-100'>
              <CirclePlay className='h-12 w-12 text-white' />
            </div>
          </div>
        </CardHeader>
        <CardContent className='p-4'>
          <CardTitle className='mb-2 text-lg'>{video.title}</CardTitle>
          <div className='mb-2 flex items-center space-x-2'>
            <Avatar className='h-6 w-6'>
              <AvatarImage src={video.author.avatar} alt={video.author.name} />
              <AvatarFallback>
                {video.author.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <CardDescription>{video.author.name}</CardDescription>
          </div>
          <div className='flex items-center justify-between text-sm text-muted-foreground'>
            <div className='flex items-center space-x-2'>
              <Clock className='h-4 w-4' />
              <span>{video.duration}</span>
            </div>
            <div className='flex items-center space-x-2'>
              <Play className='h-4 w-4' />
              <span>{video.views.toLocaleString()} views</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className='p-4 pt-0'>
          <Badge variant='secondary'>
            <Tag className='mr-1 h-3 w-3' />
            {video.category}
          </Badge>
        </CardFooter>
      </Card>
    </motion.div>
  )
}

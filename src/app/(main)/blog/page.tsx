"use client"

import {
  <PERSON><PERSON><PERSON>,
  Calendar,
  MessageSquare,
  ThumbsUp,
  Users,
} from "lucide-react"
import { useState } from "react"

import { Con<PERSON>er, Wrapper } from "@/components"
import CommonHero from "@/components/main/reusable-hero"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function BlogPage() {
  const [activeTab, setActiveTab] = useState("featured")
  console.log(activeTab)

  return (
    <>
      <CommonHero
        className='mb-8 pt-16 text-center md:pt-24'
        title={"Welcome to Our \n Insightful Blog"}
        description='Explore our latest articles, insights, and stories. Stay informed and
          inspired with our diverse range of topics.'
      />
      <Wrapper className='relative z-[1] py-10'>
        <Container className='relative'>
          <Tabs
            defaultValue='featured'
            className='w-full'
            onValueChange={setActiveTab}
          >
            <TabsList className='grid w-full grid-cols-3 lg:grid-cols-5'>
              <TabsTrigger value='featured'>Featured</TabsTrigger>
              <TabsTrigger value='recent'>Recent</TabsTrigger>
              <TabsTrigger value='popular'>Popular</TabsTrigger>
              <TabsTrigger value='categories'>Categories</TabsTrigger>
              <TabsTrigger value='authors'>Authors</TabsTrigger>
            </TabsList>
            <TabsContent value='featured'>
              <FeaturedTab />
            </TabsContent>
            <TabsContent value='recent'>
              <RecentTab />
            </TabsContent>
            <TabsContent value='popular'>
              <PopularTab />
            </TabsContent>
            <TabsContent value='categories'>
              <CategoriesTab />
            </TabsContent>
            <TabsContent value='authors'>
              <AuthorsTab />
            </TabsContent>
          </Tabs>
        </Container>
      </Wrapper>
    </>
  )
}

const FeaturedTab = () => {
  const featuredPosts = [
    {
      id: 1,
      title: "The Future of Web Development: Trends to Watch",
      author: "Alice Johnson",
      image: "/avatars/alice.jpg",
      likes: 245,
      comments: 56,
    },
    {
      id: 2,
      title: "Mastering Responsive Design: Tips and Tricks",
      author: "Bob Smith",
      image: "/avatars/bob.jpg",
      likes: 189,
      comments: 42,
    },
    {
      id: 3,
      title: "The Impact of AI on Modern Design Practices",
      author: "Carol Williams",
      image: "/avatars/carol.jpg",
      likes: 302,
      comments: 78,
    },
  ]

  return (
    <div className='mt-6 grid gap-6'>
      <h2 className='text-2xl font-semibold'>Featured Posts</h2>
      <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
        {featuredPosts.map((post) => (
          <Card key={post.id} className='overflow-hidden'>
            <CardHeader className='p-0'>
              <div className='aspect-video bg-gradient-to-r from-purple-400 via-pink-500 to-red-500'></div>
            </CardHeader>
            <CardContent className='p-6'>
              <div className='mb-4 flex items-center space-x-4'>
                <Avatar className='h-10 w-10'>
                  <AvatarImage src={post.image} alt={post.author} />
                  <AvatarFallback>
                    {post.author
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardDescription>{post.author}</CardDescription>
                </div>
              </div>
              <CardTitle className='mb-2 line-clamp-2'>{post.title}</CardTitle>
            </CardContent>
            <CardFooter className='flex justify-between'>
              <div className='flex items-center space-x-2'>
                <ThumbsUp className='h-4 w-4' />
                <span>{post.likes} likes</span>
              </div>
              <div className='flex items-center space-x-2'>
                <MessageSquare className='h-4 w-4' />
                <span>{post.comments} comments</span>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
      <Button className='mt-4 w-full'>View All Featured Posts</Button>
    </div>
  )
}

const RecentTab = () => {
  const recentPosts = [
    {
      id: 1,
      title: "10 Essential Tools for Modern Web Developers",
      author: "Emma Davis",
      date: "2023-07-10",
      readTime: "5 min read",
    },
    {
      id: 2,
      title: "The Rise of No-Code Platforms: A Game Changer?",
      author: "Liam Wilson",
      date: "2023-07-08",
      readTime: "7 min read",
    },
    {
      id: 3,
      title: "Optimizing Website Performance: A Comprehensive Guide",
      author: "Sophia Chen",
      date: "2023-07-05",
      readTime: "10 min read",
    },
  ]

  return (
    <div className='mt-6 grid gap-6'>
      <h2 className='text-2xl font-semibold'>Recent Posts</h2>
      <div className='grid gap-4'>
        {recentPosts.map((post) => (
          <Card key={post.id}>
            <CardHeader>
              <CardTitle>{post.title}</CardTitle>
              <CardDescription>By {post.author}</CardDescription>
            </CardHeader>
            <CardFooter className='flex justify-between'>
              <div className='flex items-center space-x-2'>
                <Calendar className='h-4 w-4' />
                <span>{post.date}</span>
              </div>
              <div className='flex items-center space-x-2'>
                <BookOpen className='h-4 w-4' />
                <span>{post.readTime}</span>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
      <Button variant='outline' className='mt-4 w-full'>
        View All Recent Posts
      </Button>
    </div>
  )
}

const PopularTab = () => {
  const popularPosts = [
    {
      id: 1,
      title: "The Psychology of Color in Web Design",
      author: "David Brown",
      views: 15600,
      likes: 2300,
    },
    {
      id: 2,
      title: "Demystifying GraphQL: A Beginner's Guide",
      author: "Sarah Johnson",
      views: 12400,
      likes: 1800,
    },
    {
      id: 3,
      title: "The Art of Writing Clean Code: Best Practices",
      author: "Michael Lee",
      views: 10800,
      likes: 1600,
    },
  ]

  return (
    <div className='mt-6 grid gap-6'>
      <h2 className='text-2xl font-semibold'>Popular Posts</h2>
      <div className='grid gap-4'>
        {popularPosts.map((post) => (
          <Card key={post.id}>
            <CardHeader>
              <CardTitle>{post.title}</CardTitle>
              <CardDescription>By {post.author}</CardDescription>
            </CardHeader>
            <CardFooter className='flex justify-between'>
              <div className='flex items-center space-x-2'>
                <Users className='h-4 w-4' />
                <span>{post.views} views</span>
              </div>
              <div className='flex items-center space-x-2'>
                <ThumbsUp className='h-4 w-4' />
                <span>{post.likes} likes</span>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
      <Button variant='outline' className='mt-4 w-full'>
        View All Popular Posts
      </Button>
    </div>
  )
}

const CategoriesTab = () => {
  const categories = [
    { id: 1, name: "Web Development", count: 45 },
    { id: 2, name: "UI/UX Design", count: 32 },
    { id: 3, name: "Mobile Development", count: 28 },
    { id: 4, name: "DevOps", count: 20 },
    { id: 5, name: "Machine Learning", count: 15 },
    { id: 6, name: "Blockchain", count: 10 },
  ]

  return (
    <div className='mt-6 grid gap-6'>
      <h2 className='text-2xl font-semibold'>Categories</h2>
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
        {categories.map((category) => (
          <Card key={category.id}>
            <CardHeader>
              <CardTitle>{category.name}</CardTitle>
              <CardDescription>{category.count} articles</CardDescription>
            </CardHeader>
            <CardFooter>
              <Button variant='outline' className='w-full'>
                View Category
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}

const AuthorsTab = () => {
  const authors = [
    {
      id: 1,
      name: "Jessica Lee",
      expertise: "Front-end Development",
      image: "/avatars/jessica.jpg",
      articles: 23,
    },
    {
      id: 2,
      name: "Ryan Chen",
      expertise: "Mobile App Development",
      image: "/avatars/ryan.jpg",
      articles: 18,
    },
    {
      id: 3,
      name: "Amelia Patel",
      expertise: "UI/UX Design",
      image: "/avatars/amelia.jpg",
      articles: 31,
    },
  ]

  return (
    <div className='mt-6 grid gap-6'>
      <h2 className='text-2xl font-semibold'>Featured Authors</h2>
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
        {authors.map((author) => (
          <Card key={author.id}>
            <CardHeader>
              <div className='flex items-center space-x-4'>
                <Avatar className='h-12 w-12'>
                  <AvatarImage src={author.image} alt={author.name} />
                  <AvatarFallback>
                    {author.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle>{author.name}</CardTitle>
                  <CardDescription>{author.expertise}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardFooter className='flex items-center justify-between'>
              <Badge>{author.articles} articles</Badge>
              <Button variant='outline'>View Profile</Button>
            </CardFooter>
          </Card>
        ))}
      </div>
      <Button variant='outline' className='mt-4 w-full'>
        View All Authors
      </Button>
    </div>
  )
}

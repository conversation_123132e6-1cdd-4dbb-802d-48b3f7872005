"use client"

import { motion } from "framer-motion"

import MagicCard from "@/components/ui/magic-card"

const DecorativeSVG = () => (
  <svg
    className='absolute right-0 top-0 -z-10 h-[300px] w-[300px] text-primary/20'
    viewBox='0 0 200 200'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      fill='currentColor'
      d='M44.9,-76.8C58.1,-69.3,68.9,-56.3,76.3,-41.9C83.7,-27.5,87.7,-11.7,86.5,3.7C85.3,19,78.9,33.9,69.8,46.5C60.7,59.1,48.8,69.4,35.1,76.4C21.4,83.3,5.8,86.9,-9.4,85.5C-24.6,84.1,-39.5,77.7,-52.4,68.5C-65.3,59.3,-76.3,47.3,-83.3,33C-90.3,18.7,-93.3,2.1,-90.3,-13.1C-87.2,-28.3,-78,-42.1,-66.3,-52.6C-54.6,-63.1,-40.4,-70.3,-26.8,-77.1C-13.2,-83.8,-0.2,-90.1,12.8,-89.1C25.8,-88.1,51.6,-79.8,44.9,-76.8Z'
      transform='translate(100 100)'
    />
  </svg>
)

const MissionVision = () => {
  return (
    <div className='relative overflow-hidden'>
      <DecorativeSVG />
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <h2 className='mb-8 text-center text-4xl font-bold'>
          Our Mission & Vision
        </h2>
      </motion.div>
      <div className='mx-auto max-w-4xl'>
        <MagicCard className=''>
          <div className='pt-4 md:p-8'>
            <motion.p
              className='text-pretty leading-relaxed text-muted-foreground md:text-xl'
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.8 }}
            >
              At ThePortfolyo, our mission is to simplify the process of
              building professional portfolios for individuals across diverse
              fields. We aim to empower creators by providing an intuitive,
              user-friendly platform that lets them focus on what matters most —
              showcasing their unique talents and achievements.
            </motion.p>
            <motion.p
              className='mt-6 text-pretty leading-relaxed text-muted-foreground md:text-xl'
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8, duration: 0.8 }}
            >
              Our vision is to become the go-to portfolio-building solution for
              everyone striving to make an impactful digital presence.
            </motion.p>
          </div>
        </MagicCard>
      </div>
    </div>
  )
}

export default MissionVision

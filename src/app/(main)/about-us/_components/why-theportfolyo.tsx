"use client"

import { motion } from "framer-motion"
import { CheckCircle } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { SectionBadge } from "@/components/ui/section-bade"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"

const WhyWeBuilt = () => {
  const fadeIn = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 },
  }

  return (
    <section className='bg-gradient-to-b from-background to-background/75 py-10 md:py-16'>
      <div className='mx-auto'>
        <motion.div
          className='mx-auto flex max-w-2xl flex-col items-center text-center'
          {...fadeIn}
        >
          <SectionBadge title='Why ThePortfolyo' />
          <h2 className='mt-6 font-heading text-2xl font-medium !leading-snug md:text-4xl lg:text-5xl'>
            Why We Built ThePortfolyo
          </h2>
          <p className='my-6 text-center text-base text-accent-foreground/80 md:text-lg'>
            Building a portfolio should be simple, effective, and accessible to
            everyone. ThePortfolyo makes creating and maintaining portfolios
            effortless, even for those with no technical skills.
          </p>
        </motion.div>

        <Tabs defaultValue='users' className='mx-auto w-full max-w-4xl'>
          <TabsList className='mb-3 grid w-full grid-cols-2'>
            <TabsTrigger value='users'>For Individuals</TabsTrigger>
            <TabsTrigger value='businesses'>For Businesses</TabsTrigger>
          </TabsList>
          <TabsContent value='users'>
            <Card>
              <CardHeader>
                <CardTitle>For Individuals</CardTitle>
              </CardHeader>
              <CardContent className='w-full'>
                <p className='text-muted-foreground'>
                  Keeping your portfolio up-to-date shouldn&apos;t feel like a
                  chore. Whether you&apos;re a student, freelancer, or
                  professional, managing your achievements and showcasing your
                  skills should be simple and effective.
                </p>
                <h3 className='my-2 mt-6 text-xl font-semibold'>
                  Our solution offers:
                </h3>
                <ul className='space-y-2'>
                  {[
                    "Pre-designed templates that look stunning and professional",
                    "Quick updates directly from your phone or laptop",
                    "Code-free editing for seamless experience",
                    "Integration with your latest projects and achievements",
                    "SEO-optimized portfolios for better online visibility",
                  ].map((item, index) => (
                    <motion.li
                      key={index}
                      className='flex items-start gap-2'
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <CheckCircle className='mt-1 h-6 w-6 flex-shrink-0 text-primary' />
                      <span>{item}</span>
                    </motion.li>
                  ))}
                </ul>
                <div className='mt-8'>
                  <h4 className='mb-2 text-lg font-semibold'>
                    Problems Solved:
                  </h4>
                  <ul className='list-inside list-disc space-y-2 text-muted-foreground'>
                    <li>Time-consuming and complex portfolio updates</li>
                    <li>
                      Lack of design skills to create impactful portfolios
                    </li>
                    <li>Difficulty in showcasing diverse skills effectively</li>
                    <li>Low visibility due to poor SEO and design</li>
                  </ul>
                </div>
                <div className='mt-6'>
                  <Button>Start Building Your Portfolio</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value='businesses'>
            <Card>
              <CardHeader>
                <CardTitle>For Businesses</CardTitle>
              </CardHeader>
              <CardContent className='w-full'>
                <p className='text-muted-foreground'>
                  Traditional resumes often fail to capture the complete picture
                  of a candidate&apos;s potential. ThePortfolyo helps businesses
                  hire smarter by showcasing actual skills and achievements.
                </p>
                <h3 className='my-2 mt-6 text-xl font-semibold'>
                  Our solution offers:
                </h3>

                <ul className='space-y-2'>
                  {[
                    "Access to standardized, professional portfolios",
                    "Improved hiring decisions with real project showcases",
                    "Streamlined candidate screening with up-to-date profiles",
                    "Detailed insights into candidates' skills and achievements",
                    "A broader talent pool with diverse expertise",
                  ].map((item, index) => (
                    <motion.li
                      key={index}
                      className='flex items-start gap-2'
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <CheckCircle className='mt-1 h-6 w-6 flex-shrink-0 text-primary' />
                      <span>{item}</span>
                    </motion.li>
                  ))}
                </ul>

                <div className='mt-8'>
                  <h4 className='mb-2 text-lg font-semibold'>
                    Problems Solved:
                  </h4>
                  <ul className='list-inside list-disc space-y-2 text-muted-foreground'>
                    <li>Time-consuming and inefficient resume screening</li>
                    <li>Limited insights into candidates real-world skills</li>
                    <li>
                      Challenges in identifying relevant project experience
                    </li>
                    <li>Difficulty finding specialized talent</li>
                  </ul>
                </div>
                <div className='mt-6'>
                  <Button>Find Top Talent Now</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  )
}

export default WhyWeBuilt

import { <PERSON>, Co<PERSON>, <PERSON>, Zap } from "lucide-react"
import Link from "next/link"

import { Container, Perks, Reviews, Wrapper } from "@/components"
import Heading from "@/components/headings/heading"
import HeroGeometric from "@/components/main/main-hero-new"
import { <PERSON><PERSON> } from "@/components/ui/button"

import MissionVision from "./_components/mission-vision"
import WhyWeBuilt from "./_components/why-theportfolyo"

const page = () => {
  return (
    <>
      {/* <AboutMainHero /> */}
      <HeroGeometric
        badge='Build your portfolio today'
        title1={"Build and Showcase "}
        title2={"Professional Portfolio in Minutes"}
        description="ThePortfolyo empowers creators, developers, and professionals to
          effortlessly build stunning portfolio websites. Whether you're
          showcasing projects, skills, or achievements, our platform helps you
          craft a professional digital presence with ease."
      />

      <Wrapper className='min-h-max pt-10'>
        <Container className='py-5 lg:py-10'>
          <WhyWeBuilt />

          {/* Our Mission */}
          <section className='py-10 text-white md:py-16'>
            <MissionVision />

            <div className='mx-auto mt-6 grid max-w-2xl divide-y divide-slate-300 rounded-2xl border border-slate-100 bg-gradient-to-tr from-slate-50 to-slate-200 p-6 text-left dark:divide-card dark:border-card dark:from-card dark:to-card lg:max-w-5xl lg:grid-cols-3 lg:divide-x lg:divide-y-0'>
              <div className='flex items-start gap-6 pb-6 lg:pb-0 lg:pr-6'>
                <div className='w-10'>
                  <span className='flex w-max rounded-xl bg-slate-200 p-3 text-slate-800 dark:bg-gray-900 dark:text-slate-200'>
                    <Copy />
                  </span>
                </div>
                <div className='flex-1 space-y-1'>
                  <h2 className='text-lg font-semibold text-slate-900 dark:text-white'>
                    Easy Portfolio Creation
                  </h2>
                  <p className='text-sm text-slate-700 dark:text-slate-300'>
                    Build a professional portfolio effortlessly with simple,
                    customizable templates and tools.
                  </p>
                </div>
              </div>
              <div className='flex items-start gap-6 py-6 lg:px-6 lg:py-0'>
                <div className='w-10'>
                  <span className='flex w-max rounded-xl bg-slate-200 p-3 text-slate-800 dark:bg-gray-900 dark:text-slate-200'>
                    <Zap />
                  </span>
                </div>
                <div className='flex-1 space-y-1'>
                  <h2 className='text-lg font-semibold text-slate-900 dark:text-white'>
                    Professional Templates
                  </h2>
                  <p className='text-sm text-slate-700 dark:text-slate-300'>
                    Choose from a range of beautifully designed templates
                    tailored to your needs.
                  </p>
                </div>
              </div>
              <div className='flex items-start gap-6 pt-6 lg:pl-6 lg:pt-0'>
                <div className='w-10'>
                  <span className='flex w-max rounded-xl bg-slate-200 p-3 text-slate-800 dark:bg-gray-900 dark:text-slate-200'>
                    <Clock />
                  </span>
                </div>
                <div className='flex-1 space-y-1'>
                  <h2 className='text-lg font-semibold text-slate-900 dark:text-white'>
                    Time-Saving Tools
                  </h2>
                  <p className='text-sm text-slate-700 dark:text-slate-300'>
                    Save time with a platform designed to create your portfolio
                    in just minutes.
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Why We Built ThePortfolyo */}

          {/* Perks */}
          <Perks />

          {/* Reviews */}
          <Reviews />

          {/* Call to Action Section */}
          <section className='relative overflow-hidden py-24'>
            <div className='container relative z-10 mx-auto px-4 text-center'>
              <div>
                <Heading>Ready to Build Your Dream Portfolio?</Heading>
                <p className='mx-auto mb-8 max-w-2xl text-xl text-muted-foreground'>
                  Join thousands of professionals who have created stunning
                  portfolios with ThePortfolyo. Start your journey today and
                  stand out in the digital world.
                </p>
                <Button size='lg' asChild>
                  <Link href='/'>
                    Get Started for Free
                    <Users className='ml-2 h-5 w-5' />
                  </Link>
                </Button>
              </div>
            </div>
          </section>
        </Container>
      </Wrapper>
    </>
  )
}

export default page

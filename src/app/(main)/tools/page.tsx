import { Container, Wrapper } from "@/components"
import { ToolCard } from "@/components/cards/tool-card"
import CommonHero from "@/components/main/reusable-hero"
import { tools } from "@/constants/tools"

const ToolsPage = async () => {
  return (
    <>
      <CommonHero
        className='mb-8 pt-16 text-center md:pt-24'
        title={"Essential Tools \n for Your Portfolio"}
        description=' Discover a curated selection of powerful tools to enhance your
          portfolio. From resume builders to image optimizers, these resources
          will help you create a standout professional presence.'
      />

      <Wrapper className='relative py-10'>
        <Container className='relative'>
          {/* <Spotlight
            className="-top-40 left-0 md:left-60 md:-top-20"
            fill="rgba(255, 255, 255, 0.5)"
          /> */}

          <div className='grid grid-cols-1 gap-6 pt-4 md:grid-cols-2 lg:grid-cols-3'>
            {tools.map((tool) => (
              <ToolCard key={tool.id} tool={tool} />
            ))}
          </div>
        </Container>
      </Wrapper>
    </>
  )
}

export default ToolsPage

import { getTemplates } from "@/actions/template"
import { <PERSON><PERSON><PERSON>, Wrapper } from "@/components"
import { MainThemeCard } from "@/components/cards/theme-card"
import CommonHero from "@/components/main/reusable-hero"
import { Template } from "@/types/template"

const page = async () => {
  const data = await getTemplates()
  // console.log(data);
  return (
    <>
      <CommonHero
        className='mb-8 pt-16 text-center md:pt-24'
        title={"Portfolio Themes \n you can choose from"}
        description=' Our Portfolio themes are crafted to showcase your work effortlessly.
          Perfect for designers, developers, photographers, and creatives,
          they’re easy to use, customize, and designed to help you stand out.'
      />

      <Wrapper className='relative py-10'>
        <Container className='relative'>
          <div className='grid grid-cols-1 gap-6 pt-4 md:grid-cols-2 lg:grid-cols-3'>
            {data?.map((theme: Template) => (
              <MainThemeCard key={theme._id} theme={theme} />
            ))}
          </div>
        </Container>
      </Wrapper>
    </>
  )
}

export default page

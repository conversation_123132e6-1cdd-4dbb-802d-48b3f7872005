import React from "react"

import Heading from "@/components/headings/heading"

export const metadata = {
  title: "privacy-policy",
}

const page = () => {
  return (
    <main className='h-full min-h-screen w-full px-5 pt-20 md:pt-28'>
      <div className='mx-auto w-full max-w-5xl pb-20'>
        {/* top  */}

        <Heading> Privacy Policy</Heading>

        {/* top end */}

        <div className='w-full space-y-5 text-pretty text-muted-foreground dark:text-muted-foreground'>
          {/* Section 1: Information We Collect */}
          <section className='py-3 pt-6'>
            <PrivacySubHeading text={" 1. Information We Collect"} />

            <PrivacyParagraph
              text={
                "   We collect various types of information to operate and provide you with our services. The information we collect includes:"
              }
            />
            <ul className='flex list-disc flex-col gap-2 pl-3 text-sm md:text-base'>
              <li>
                Personal Information: This includes your name, email address,
                billing information, shipping address, and any other information
                you provide during the account creation process.
              </li>
              <li>
                Transaction Information: Details about the products or services
                you purchase, payment methods used, and other
                transaction-related details.
              </li>
              <li>
                User Content: Any content you create, upload, or share on our
                platform, including portfolio items, images, and descriptions.
              </li>
              <li>
                Device and Log Information: Information about your device,
                browser type, IP address, and pages visited on our platform.
              </li>
            </ul>
          </section>

          {/* Section 2: How We Use Your Information */}
          <section className='py-5'>
            <PrivacySubHeading text={"2. How We Use Your Information"} />
            <PrivacyParagraph
              text={
                "We use the collected information for the following purposes: "
              }
            />
            <ul className='flex list-disc flex-col gap-2 pl-3 text-sm md:text-base'>
              <li>
                Service Provision: Fulfilling orders, processing payments, and
                providing customer support.
              </li>
              <li>
                Platform Improvement: Analyzing user behavior to enhance and
                optimize our platform.
              </li>
              <li>
                Communication: Sending important updates, notifications, and
                promotional materials.
              </li>
            </ul>
          </section>

          {/* Section 3: Information Sharing */}
          <section className='py-3'>
            <PrivacySubHeading text={" 3. Information Sharing"} />
            <PrivacyParagraph
              text={
                "We respect your privacy, and we do not sell, trade, or disclose  your personal information to third parties without your consent, except as described below:"
              }
            />

            <ul className='flex list-disc flex-col gap-2 pl-3 text-sm md:text-base'>
              <li>
                Service Providers: We may share information with third-party
                service providers who assist us in operating our platform, such
                as payment processors and shipping partners.
              </li>
              <li>
                Legal Compliance: To comply with applicable laws, regulations,
                or respond to legal requests.
              </li>
            </ul>
          </section>

          {/* Section 4: Your Choices */}
          <section className='py-3'>
            <PrivacySubHeading text={"4. Your Choices"} />

            <PrivacyParagraph
              text={
                "  You have control over the information you provide and can make the following choices:"
              }
            />
            <ul>
              <li>
                Opt-Out: You can opt-out of receiving promotional emails by
                following the instructions in the emails.
              </li>
              <li>
                Account Information: You can review, update, or delete your
                account information by logging into your account.
              </li>
            </ul>
          </section>

          {/* Section 5: Security */}
          <section className='py-3'>
            <PrivacySubHeading text={"5. Security"} />

            <PrivacyParagraph
              text={`We take reasonable measures to protect your information from
unauthorized access, alteration, or disclosure. However, no data
transmission over the internet is entirely secure, and we cannot
guarantee the absolute security of your information.`}
            />
          </section>

          {/* Section 6: Data Retention */}
          <section className='py-3'>
            <PrivacySubHeading text={"6. Data Retention"} />

            <PrivacyParagraph
              text={`We retain your information for as long as necessary to provide our
              services and fulfill the purposes outlined in this Privacy Policy.
              You can request the deletion of your account and associated
              information.`}
            />
          </section>

          {/* Section 7: Updates to This Privacy Policy */}
          <section className='py-3'>
            <PrivacySubHeading text={"7. Updates to This Privacy Policy"} />
            <PrivacyParagraph
              text={`We may update this Privacy Policy to reflect changes in our
              practices. We will notify you of significant changes through the
              platform or other means. Please review this page periodically for
              the latest information.`}
            />
          </section>

          {/* Section 8: Contact Us */}
          <section>
            <PrivacySubHeading text={"8. Contact Us"} />
            <PrivacyParagraph
              text={`If you have any questions or concerns about this Privacy Policy,
              please contact <NAME_EMAIL>`}
            />
          </section>

          {/* Additional Section: Content Management and Dashboard */}
          <section className='py-3'>
            <PrivacySubHeading text={"Content Management and Dashboard"} />
            <PrivacyParagraph
              text={`As a part of our services, we provide you with a user-friendly dashboard. Through the dashboard, you can easily update and manage the content on your portfolio. We do not update the content for you, giving you the autonomy to showcase your work the way you want.`}
            />
            <PrivacyParagraph
              text={`If you prefer assistance, we also offer the option to hire a content manager from us on a monthly basis. Our content managers are skilled professionals who can help curate and enhance your portfolio content, ensuring it aligns with your goals and aspirations.`}
            />
          </section>
        </div>
      </div>
    </main>
  )
}

const PrivacySubHeading = ({ text }) => {
  return (
    <h2 className='mb-2 text-xl font-semibold text-primary-foreground md:text-2xl'>
      {text}
    </h2>
  )
}
const PrivacyParagraph = ({ text }) => {
  return (
    <p className='py-2 text-sm text-muted-foreground md:text-base'>{text}</p>
  )
}

export default page

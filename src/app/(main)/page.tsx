import { getPlans } from "@/actions/plans"
import {
  Companies,
  Container,
  CTA,
  Features,
  Hero,
  Perks,
  Pricing,
  Reviews,
  Wrapper,
} from "@/components"
import FeaturesSection from "@/components/main/feature-section"

const HomePage = async () => {
  const plans = await getPlans()

  return (
    <>
      <Hero showImage={true} />

      <Wrapper className='relative'>
        <Container>
          <Features />
          <Companies />
          <Perks />

          <FeaturesSection />

          <Pricing plans={plans} />
          <Reviews />

          <CTA />
        </Container>
      </Wrapper>

      {/* </Background> */}
    </>
  )
}

export default HomePage

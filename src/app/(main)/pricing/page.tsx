import { getPlans } from "@/actions/plans"
import { Container, Reviews, Wrapper } from "@/components"
import { PricingPlans } from "@/components/main/new-pricing"
import { SectionBadge } from "@/components/ui/section-bade"
import { Spotlight } from "@/components/ui/spotlight"

const PircingPage = async () => {
  const plans = await getPlans()
  if (!plans) {
    return null
  }

  // console.log(plans)
  return (
    <>
      {/* <Background> */}
      <Wrapper className='relative py-20'>
        <div className='absolute left-[calc(55%-379px/2)] top-[-266px] -z-10 hidden h-[620px] w-[379px] rounded-full bg-primary/60 opacity-50 blur-[10rem] lg:flex'></div>
        <div className='absolute left-[calc(50%-433px/2)] top-[-60px] -z-10 hidden h-[525px] w-[433px] rounded-full bg-primaryLight/40 opacity-50 blur-[15rem] lg:flex'></div>

        <Container className='relative w-full py-12 md:py-16 lg:py-20'>
          <div className='mx-auto flex max-w-xl flex-col items-center text-center'>
            <SectionBadge title='Choose your plan' />
            <h2 className='mt-6 font-heading text-2xl font-medium md:text-4xl lg:text-5xl'>
              Simple and transparent pricing
            </h2>
            <p className='my-6 text-base text-accent-foreground/80 md:text-lg'>
              Choose the plan that suits your needs. No hidden fees, no
              surprises.
            </p>
          </div>

          <Spotlight
            className='-top-40 left-0 md:-top-20 md:left-60'
            fill='rgba(255, 255, 255, 0.5)'
          />
          <PricingPlans plans={plans} />
        </Container>

        {/* <Container>
          <Spotlight
            className="-top-40 left-0 md:left-60 md:-top-20"
            fill="rgba(255, 255, 255, 0.5)"
          />
          <PricingPlans plans={plans} />
        </Container> */}

        <Reviews />
      </Wrapper>

      {/* </Background> */}
    </>
  )
}

export default PircingPage

"use client"

import { notFound, useRouter } from "next/navigation"
import React from "react"

import LogoLoading from "@/components/custom/logo-loading"
import { useUserStore } from "@/store/use-user-store"

interface Props {
  children: React.ReactNode
}

const DashboardLayout = ({ children }: Props) => {
  const router = useRouter()
  const { userLoading, userError, isLoggedIn, fetchUser } = useUserStore()

  React.useEffect(() => {
    if (!isLoggedIn) {
      fetchUser()
    }
  }, [isLoggedIn, fetchUser])

  React.useEffect(() => {
    if (!userLoading && !isLoggedIn) {
      router.push("/auth/login")
    }
  }, [userLoading, isLoggedIn, router])

  // Show a loading screen while checking the user status
  if (userLoading) {
    return <LogoLoading />
  }

  // Prevent rendering content if the user is not logged in
  if (!userLoading && !isLoggedIn) {
    return null // Return nothing while waiting for the redirect
  }

  // Handle error states (optional)
  if (userError) {
    return notFound()
  }

  return (
    <>
      <main className='flex min-h-screen w-full flex-col'>{children}</main>
    </>
  )
}

export default DashboardLayout

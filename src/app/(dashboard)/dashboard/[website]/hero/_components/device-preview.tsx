import { Monitor, Smartphone, Tablet } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"

interface DevicePreviewProps {
  value: "desktop" | "tablet" | "mobile"
  onChange: (value: "desktop" | "tablet" | "mobile") => void
}

export function DevicePreview({ value, onChange }: DevicePreviewProps) {
  return (
    <div className='flex space-x-2'>
      <Button
        variant={value === "desktop" ? "default" : "outline"}
        size='sm'
        onClick={() => onChange("desktop")}
      >
        <Monitor className='h-4 w-4' />
      </Button>
      <Button
        variant={value === "tablet" ? "default" : "outline"}
        size='sm'
        onClick={() => onChange("tablet")}
      >
        <Tablet className='h-4 w-4' />
      </Button>
      <Button
        variant={value === "mobile" ? "default" : "outline"}
        size='sm'
        onClick={() => onChange("mobile")}
      >
        <Smartphone className='h-4 w-4' />
      </Button>
    </div>
  )
}

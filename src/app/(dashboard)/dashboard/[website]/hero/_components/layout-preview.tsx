import { cn } from "@/lib/utils"
import type { HeroLayout } from "@/types/hero"

interface LayoutPreviewProps {
  layout: HeroLayout
  isSelected: boolean
  onSelect: (layout: HeroLayout) => void
}

export function LayoutPreview({
  layout,
  isSelected,
  onSelect,
}: LayoutPreviewProps) {
  const layouts: Record<HeroLayout, React.ReactNode> = {
    default: (
      <div className='grid h-24 grid-cols-2 gap-4 rounded-lg bg-muted p-2'>
        <div className='space-y-2'>
          <div className='h-3 w-3/4 rounded bg-foreground/20' />
          <div className='h-2 w-1/2 rounded bg-foreground/20' />
          <div className='flex gap-2'>
            <div className='h-4 w-16 rounded bg-primary' />
            <div className='h-4 w-16 rounded bg-foreground/20' />
          </div>
        </div>
        <div className='rounded bg-foreground/10' />
      </div>
    ),
    centered: (
      <div className='flex h-24 flex-col items-center justify-center space-y-2 rounded-lg bg-muted p-2'>
        <div className='h-3 w-1/2 rounded bg-foreground/20' />
        <div className='h-2 w-1/3 rounded bg-foreground/20' />
        <div className='flex gap-2'>
          <div className='h-4 w-16 rounded bg-primary' />
          <div className='h-4 w-16 rounded bg-foreground/20' />
        </div>
      </div>
    ),
    minimal: (
      <div className='flex h-24 flex-col justify-center space-y-2 rounded-lg bg-muted p-2'>
        <div className='h-3 w-3/4 rounded bg-foreground/20' />
        <div className='h-2 w-1/2 rounded bg-foreground/20' />
        <div className='h-4 w-16 rounded bg-primary' />
      </div>
    ),
    gradient: (
      <div className='flex h-24 flex-col justify-center space-y-2 rounded-lg bg-gradient-to-r from-primary/20 to-primary/5 p-2'>
        <div className='h-3 w-3/4 rounded bg-foreground/20' />
        <div className='h-2 w-1/2 rounded bg-foreground/20' />
        <div className='flex gap-2'>
          <div className='h-4 w-16 rounded bg-primary' />
          <div className='h-4 w-16 rounded bg-foreground/20' />
        </div>
      </div>
    ),
    "image-focus": (
      <div className='grid h-24 grid-cols-3 gap-4 rounded-lg bg-muted p-2'>
        <div className='col-span-2 rounded bg-foreground/10' />
        <div className='space-y-2'>
          <div className='h-3 w-full rounded bg-foreground/20' />
          <div className='h-2 w-2/3 rounded bg-foreground/20' />
          <div className='h-4 w-full rounded bg-primary' />
        </div>
      </div>
    ),
  }

  return (
    <button
      onClick={() => onSelect(layout)}
      className={cn(
        "w-full transition-all hover:scale-105",
        isSelected && "rounded-lg ring-2 ring-primary ring-offset-2",
      )}
    >
      {layouts[layout]}
      <p className='mt-2 text-center text-sm font-medium capitalize'>
        {layout.replace("-", " ")}
      </p>
    </button>
  )
}

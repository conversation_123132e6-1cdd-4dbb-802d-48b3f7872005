import { Columns, LayoutGrid } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"

interface LayoutSelectorProps {
  value: "centered" | "split"
  onChange: (value: "centered" | "split") => void
}

export function LayoutSelector({ value, onChange }: LayoutSelectorProps) {
  return (
    <div className='flex space-x-2'>
      <Button
        variant={value === "centered" ? "default" : "outline"}
        size='sm'
        onClick={() => onChange("centered")}
      >
        <LayoutGrid className='mr-2 h-4 w-4' />
        Centered
      </Button>
      <Button
        variant={value === "split" ? "default" : "outline"}
        size='sm'
        onClick={() => onChange("split")}
      >
        <Columns className='mr-2 h-4 w-4' />
        Split
      </Button>
    </div>
  )
}

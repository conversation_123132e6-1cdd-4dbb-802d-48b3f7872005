/* eslint-disable @next/next/no-img-element */
import Link from "next/link"

import { But<PERSON> } from "@/components/ui/button"
import type { HeroFormValues, HeroLayout } from "@/types/hero"

interface HeroPreviewProps {
  data: HeroFormValues
  selectedLayout: HeroLayout
}

export function HeroPreview({ data, selectedLayout }: HeroPreviewProps) {
  const renderLayout = () => {
    const commonContent = (
      <>
        <h1 className='text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl'>
          {data.title}
        </h1>
        {data.sub_title && (
          <p className='mt-4 text-xl text-muted-foreground'>{data.sub_title}</p>
        )}
        {data.description && (
          <p className='mt-6 text-lg leading-8 text-muted-foreground'>
            {data.description}
          </p>
        )}
      </>
    )

    const primaryButton = (
      <Button asChild size='lg'>
        <Link href={data.cta_button_primary.url}>
          {data.cta_button_primary.text}
        </Link>
      </Button>
    )

    const secondaryButton = data.cta_button_secondary && (
      <Button asChild variant='outline' size='lg'>
        <Link href={data?.cta_button_secondary?.url ?? "#"}>
          {data.cta_button_secondary.text}
        </Link>
      </Button>
    )

    const heroImage = (
      <div className='relative aspect-[16/9] sm:aspect-[2/1] lg:aspect-square lg:w-full lg:max-w-none'>
        <img
          loading='lazy'
          src={
            data.main_image || data.background_images[0] || "/placeholder.svg"
          }
          alt={data.background_images[0] || "Hero image"}
          className='h-full w-full rounded-lg object-cover'
        />
      </div>
    )

    switch (selectedLayout) {
      case "centered":
        return (
          <div className='space-y-8 text-center'>
            {commonContent}
            <div className='mt-8 flex justify-center space-x-4'>
              {primaryButton}
              {secondaryButton}
            </div>
          </div>
        )
      case "minimal":
        return (
          <div className='max-w-2xl space-y-8'>
            {commonContent}
            <div className='mt-8'>{primaryButton}</div>
          </div>
        )
      case "gradient":
        return (
          <div className='from-primary-100 via-primary-50 to-primary-300 relative isolate overflow-hidden rounded-lg bg-gradient-to-b py-16 sm:py-24 lg:py-32'>
            <div className='relative z-10 space-y-8 text-center'>
              {commonContent}
              <div className='mt-8 flex justify-center space-x-4'>
                {primaryButton}
                {secondaryButton}
              </div>
            </div>
            <div className='absolute inset-0 -z-10 opacity-30 mix-blend-multiply'>
              {heroImage}
            </div>
          </div>
        )
      case "image-focus":
        return (
          <div className='grid items-center gap-12 lg:grid-cols-2'>
            {heroImage}
            <div className='space-y-8'>
              {commonContent}
              <div className='mt-8 flex flex-col gap-4 sm:flex-row'>
                {primaryButton}
                {secondaryButton}
              </div>
            </div>
          </div>
        )
      default:
        return (
          <div className='grid items-center gap-12 lg:grid-cols-2'>
            {heroImage}
            <div className='space-y-8'>
              {commonContent}
              <div className='mt-8 flex flex-col gap-4 sm:flex-row'>
                {primaryButton}
                {secondaryButton}
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className='bg-background'>
      <div className='mx-auto max-w-7xl px-4 py-12 sm:px-6 sm:py-16 lg:px-8 lg:py-20'>
        {renderLayout()}
      </div>
    </div>
  )
}

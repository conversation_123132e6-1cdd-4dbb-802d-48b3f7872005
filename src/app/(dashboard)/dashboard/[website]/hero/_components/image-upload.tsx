/* eslint-disable @next/next/no-img-element */
"use client"

import { CameraIcon, X } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface ImageUploadProps {
  value: Array<{
    url: string
    alt_text: string
    is_primary: boolean
  }>
  onChange: (
    value: Array<{
      url: string
      alt_text: string
      is_primary: boolean
    }>,
  ) => void
}

export function ImageUpload({ value, onChange }: ImageUploadProps) {
  const onUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // In a real application, you would upload the file to your storage service
      // For this example, we'll use a placeholder
      const newImage = {
        url: `/placeholder.svg?height=1080&width=1920`,
        alt_text: file.name,
        is_primary: value.length === 0,
      }
      onChange([...value, newImage])
    }
  }

  const onRemove = (index: number) => {
    const newImages = value.filter((_, i) => i !== index)
    onChange(newImages)
  }

  const onSetPrimary = (index: number) => {
    const newImages = value.map((img, i) => ({
      ...img,
      is_primary: i === index,
    }))
    onChange(newImages)
  }

  return (
    <div className='space-y-4'>
      <div className='grid gap-4 sm:grid-cols-2'>
        {value.map((image, index) => (
          <div
            key={index}
            className='group relative aspect-video overflow-hidden rounded-lg border'
          >
            <img
              src={image.url}
              alt={image.alt_text}
              loading='lazy'
              className='h-full w-full object-cover'
            />
            <div className='absolute inset-0 bg-black/40 opacity-0 transition-opacity group-hover:opacity-100'>
              <div className='absolute bottom-2 right-2 flex gap-2'>
                <Button
                  type='button'
                  variant='destructive'
                  size='icon'
                  onClick={() => onRemove(index)}
                >
                  <X className='h-4 w-4' />
                </Button>
                <Button
                  type='button'
                  variant={image.is_primary ? "default" : "secondary"}
                  size='sm'
                  onClick={() => onSetPrimary(index)}
                >
                  {image.is_primary ? "Primary" : "Set as Primary"}
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
      <div>
        <Label htmlFor='image-upload' className='cursor-pointer'>
          <div className='flex items-center gap-2 text-muted-foreground transition-colors hover:text-foreground'>
            <CameraIcon className='h-4 w-4' />
            <span>Upload Image</span>
          </div>
          <Input
            id='image-upload'
            type='file'
            accept='image/*'
            className='hidden'
            onChange={onUpload}
          />
        </Label>
      </div>
    </div>
  )
}

"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { useParams } from "next/navigation"
import { useState } from "react"
import { toast } from "sonner"

import { createOrUpdateHero, getHeroByWebsite } from "@/actions/hero"
import { Container } from "@/components"
import DashboardPageStickyWrapper from "@/components/dashboard/dashboard-page-sticky-wrapper"
import { HeroForm } from "@/components/forms/hero-form"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"
import type { HeroFormValues, HeroLayout } from "@/types/hero"

import { HeroPreview } from "./_components/hero-preview"

const defaultHeroData: HeroFormValues = {
  title: "Welcome to Our Website",
  sub_title: "Discover Amazing Features",
  description: "We offer the best solutions for your needs.",
  main_image: "/placeholder.svg",
  background_images: ["/placeholder.svg"],
  cta_button_primary: { text: "Get Started", url: "#" },
  cta_button_secondary: { text: "Learn More", url: "#" },
}

export default function HeroEditorPage() {
  const { website } = useParams<{ website: string }>()
  const [selectedLayout, setSelectedLayout] = useState<HeroLayout>("default")
  const [isEditing, setIsEditing] = useState(false)

  const {
    data: heroData,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["hero", website],
    queryFn: () => getHeroByWebsite(website),
    enabled: !!website,
  })

  const { mutate } = useMutation({
    mutationFn: (updatedData: HeroFormValues) =>
      createOrUpdateHero(website, updatedData),
    onSuccess: () => {
      toast.success("Hero section updated successfully")
      refetch()
      setIsEditing(false)
    },
    onError: () => {
      toast.error("Failed to update hero section")
    },
  })

  if (isLoading) {
    return (
      <Container>
        <div className='space-y-8'>
          <Skeleton className='h-8 w-[200px]' />
          <Skeleton className='h-[400px] w-full' />
        </div>
      </Container>
    )
  }

  if (isError) {
    return (
      <Container>
        <div className='py-12 text-center'>
          <h2 className='mb-2 text-xl font-semibold'>Something went wrong</h2>
          <p className='text-muted-foreground'>
            Failed to load hero section data
          </p>
        </div>
      </Container>
    )
  }

  const currentData = heroData || defaultHeroData

  return (
    <Container>
      <DashboardPageStickyWrapper>
        <div className='flex items-center space-x-4'>
          <h1 className='text-3xl font-bold'>Hero Editor</h1>
          <div className='flex w-full flex-1 items-center justify-end gap-4'>
            {!isEditing && (
              <>
                <Select
                  value={selectedLayout}
                  onValueChange={(value) =>
                    setSelectedLayout(value as HeroLayout)
                  }
                >
                  <SelectTrigger className='w-[180px]'>
                    <SelectValue placeholder='Select layout' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='default'>Default</SelectItem>
                    <SelectItem value='centered'>Centered</SelectItem>
                    <SelectItem value='minimal'>Minimal</SelectItem>
                    <SelectItem value='gradient'>Gradient</SelectItem>
                    <SelectItem value='image-focus'>Image Focus</SelectItem>
                  </SelectContent>
                </Select>
              </>
            )}
            <Button
              className='min-w-20'
              onClick={() => setIsEditing(!isEditing)}
              variant={isEditing ? "destructive" : "default"}
            >
              {isEditing ? "Cancel" : "Edit"}
            </Button>
          </div>
        </div>
      </DashboardPageStickyWrapper>

      <section className='px-4 py-3 pb-7 md:px-6 md:py-4 md:pb-10 2xl:px-8 2xl:py-6 2xl:pb-16'>
        <Card>
          <CardContent
            className={cn(
              "grid p-6",
              isEditing ? "gap-4 xl:grid-cols-1" : "grid-cols-1",
            )}
          >
            {isEditing ? (
              <HeroForm
                initialData={currentData}
                onOpenChange={setIsEditing}
                onUpdate={(data) => {
                  mutate(data)
                }}
              />
            ) : (
              <HeroPreview data={currentData} selectedLayout={selectedLayout} />
            )}
          </CardContent>
        </Card>
      </section>
    </Container>
  )
}

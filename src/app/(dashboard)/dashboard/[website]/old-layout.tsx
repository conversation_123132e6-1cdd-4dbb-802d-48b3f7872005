// "use client";
// import DashboardNavbar from "@/components/dashboard/dashboard-navbar";
// import DashboardSidebar from "@/components/dashboard/dashboard-sidebar";
// import { useUserStore } from "@/store/use-user-store";

// import Image from "next/image";
// import { notFound, useRouter } from "next/navigation";
// import React, { useEffect } from "react";

// interface Props {
//   children: React.ReactNode;
// }

// const DashboardLayout = ({ children }: Props) => {
//   const router = useRouter();
//   const { user, userLoading, userError, isLoggedIn, fetchUser } =
//     useUserStore();

//   useEffect(() => {
//     fetchUser();
//   }, [fetchUser]);

//   useEffect(() => {
//     if (!userLoading && !isLoggedIn) {
//       router.push("/login");
//     }
//   }, [userLoading, isLoggedIn, router]);

//   if (userLoading) {
//     return (
//       <div className="min-h-screen flex items-center justify-center w-full">
//         <Image
//           src={
//             "https://utfs.io/f/qPlpyBmwd8UNH6DswT44WbcxIHPdTa18r3v7qelB5nkUwJSO"
//           }
//           alt="Loading..."
//           width={300}
//           height={300}
//           className="md:w-24 md:h-24 rounded-sm animate-pulse"
//         />
//       </div>
//     );
//   }

//   if (userError) {
//     return notFound();
//   }

//   if (!isLoggedIn) {
//     return null; // Return nothing while redirecting
//   }

//   return (
//     <div className="flex flex-col min-h-screen w-full">
//       <DashboardNavbar />
//       <main className="flex flex-col lg:flex-row flex-1 size-full">
//         <DashboardSidebar />
//         <div className="w-full pt-14 lg:ml-72 lg:max-w-[calc(100%-18rem)]">
//           {children}
//         </div>
//       </main>
//     </div>
//   );
// };

// export default DashboardLayout;

const oldlayout = () => {
  return <div>oldlayout</div>
}

export default oldlayout

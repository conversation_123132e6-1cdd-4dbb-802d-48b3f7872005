import React from "react"

import Dashboard<PERSON>avbar from "@/components/dashboard/dashboard-navbar"
import DashboardSidebar from "@/components/dashboard/new-dashboard-sidebar/dashboard-sidebar"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"

interface Props {
  children: React.ReactNode
}

export const dynamic = "force-static"

const DashboardLayout = ({ children }: Props) => {
  return (
    <SidebarProvider>
      <DashboardSidebar />
      <SidebarInset>
        <DashboardNavbar />

        <main className='min-h-screen flex-1 p-0'>{children}</main>
      </SidebarInset>
    </SidebarProvider>
  )
}

export default DashboardLayout

"use client"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import { createBlog, getBlogById, updateBlog } from "@/actions/blog"
import { Container } from "@/components"
import { BlogForm } from "@/components/forms/blog-form"
import type { BlogFormData } from "@/types/blog"

export default function BlogFormPage() {
  const params = useParams<{ website: string; id: string }>()
  const { website, id } = params
  const router = useRouter()
  const queryClient = useQueryClient()
  const isNewBlog = id === "new"

  const {
    data: blog,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["blog", params.id],
    queryFn: () => getBlogById(params.id as string),
    enabled: !isNewBlog,
  })

  const createMutation = useMutation({
    mutationFn: (data: BlogFormData) => createBlog(website, data),
    onSuccess: () => {
      toast.success("Blog post created successfully")
      queryClient.invalidateQueries({ queryKey: ["blogs", website] })
      router.push(`/dashboard/${website}/blogs`)
    },
    onError: (error) => {
      toast.error(`Error creating blog post: ${error.message}`)
    },
  })

  const updateMutation = useMutation({
    mutationFn: (data: BlogFormData) => updateBlog(params.id as string, data),
    onSuccess: () => {
      toast.success("Blog post updated successfully")
      queryClient.invalidateQueries({ queryKey: ["blogs", website] })
      queryClient.invalidateQueries({ queryKey: ["blog", params.id] })
      router.push(`/dashboard/${website}/blogs`)
    },
    onError: (error) => {
      toast.error(`Error updating blog post: ${error.message}`)
    },
  })

  const handleSubmit = async (data: BlogFormData) => {
    if (isNewBlog) {
      createMutation.mutate(data)
    } else {
      updateMutation.mutate(data)
    }
  }

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (error) {
    return <div>Error: {error.message}</div>
  }

  return (
    <Container delay={0.3} className='container mx-auto pb-10 pt-2'>
      <h1 className='mb-6 text-3xl font-bold'>
        {isNewBlog ? "Create New Blog Post" : "Edit Blog Post"}
      </h1>
      <BlogForm
        websiteId={website}
        onSubmit={handleSubmit}
        defaultValues={blog ?? undefined}
      />
    </Container>
  )
}

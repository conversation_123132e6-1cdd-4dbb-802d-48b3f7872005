"use client"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import Image from "next/image"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"

import { deletewebsite, getWebsites } from "@/actions/website"
import { Container, Wrapper } from "@/components"
import { AddNewWebsiteCard } from "@/components/cards/add-new-website-card"
import { WebsiteCard } from "@/components/cards/website-card"
import { TopBanner } from "@/components/custom/top-notification-bar"
import { EmptyState } from "@/components/dashboard/empty-state"
import { QuickStats } from "@/components/dashboard/quick-stats"
import { MultiStepCreateWebsiteDialog } from "@/components/forms/multi-step-website-form"
import CommonHero from "@/components/main/reusable-hero"
import { Button } from "@/components/ui/button"
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
} from "@/components/ui/dialog"
import { Skeleton } from "@/components/ui/skeleton"
import { useUserStore } from "@/store/use-user-store"
import { User } from "@/types/user"
import { Website } from "@/types/website"

export default function DashboardHome() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const isDialogOpen = searchParams.get("createWebsite") === "true"
  const { user, userLoading } = useUserStore()
  const queryClient = useQueryClient()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedWebsite, setSelectedWebsite] = useState<Website | null>(null)
  const [hasWebsites, setHasWebsites] = useState(!!user?.websites?.length)

  const {
    data: websites,
    isLoading: websitesLoading,
    isError: websitesError,
  } = useQuery<Website[]>({
    queryKey: ["websites"],
    queryFn: getWebsites,
    enabled: hasWebsites,
  })

  // to handle first time website creation
  useEffect(() => {
    if (websites && websites?.length > 0) {
      setHasWebsites(true)
    }
  }, [websites])

  const mutation = useMutation({
    mutationFn: (websiteId: string) => deletewebsite(websiteId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["websites"] })
      setDeleteDialogOpen(false)
    },
  })

  const confirmDelete = (website: Website) => {
    setSelectedWebsite(website)
    setDeleteDialogOpen(true)
  }

  const openDialog = () => router.push("?createWebsite=true", { scroll: false })
  const closeDialog = () => router.push("?", { scroll: false })

  return (
    <>
      <TopBanner />
      <Wrapper className='min-h-screen p-2 pb-10'>
        <Container reverse className='container relative mx-auto'>
          <Button
            variant='tertiary'
            size='iconlg'
            asChild
            className='fixed right-8 top-4 z-50'
          >
            <Link href='/'>
              <Image
                src='https://utfs.io/f/qPlpyBmwd8UNH6DswT44WbcxIHPdTa18r3v7qelB5nkUwJSO'
                alt='ThePortfolyo Logo'
                width={70}
                height={70}
                className='h-10 w-auto rounded-sm'
              />
            </Link>
          </Button>

          {/* Background elements */}
          <BackgroundElements />

          {/* Welcome Header */}
          <WelcomeHeader user={user} userLoading={userLoading} />

          {/* Quick Stats */}
          {websitesLoading ? (
            <QuickStatsLoading />
          ) : (
            websites && hasWebsites && <QuickStats websites={websites} />
          )}

          {/* Website Section */}
          {websitesLoading ? (
            <WebsitesLoading />
          ) : hasWebsites ? (
            <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
              <AddNewWebsiteCard onClick={openDialog} />
              {websites?.map((website: Website) => (
                <WebsiteCard
                  key={website._id}
                  website={website}
                  onDelete={() => confirmDelete(website)}
                />
              ))}
            </div>
          ) : (
            !websitesError && <EmptyState onClick={openDialog} />
          )}

          {/* Dialogs */}
          <MultiStepCreateWebsiteDialog
            open={isDialogOpen}
            onOpenChange={closeDialog}
          />
          <DeleteWebsiteDialog
            open={deleteDialogOpen}
            onOpenChange={setDeleteDialogOpen}
            website={selectedWebsite}
            onConfirm={() => mutation.mutate(selectedWebsite?._id || "")}
            isDeleting={mutation.isPending}
          />
        </Container>
      </Wrapper>
    </>
  )
}

// Extracted components
function BackgroundElements() {
  return (
    <>
      <div
        aria-hidden='true'
        className='absolute inset-x-0 top-[-10rem] -z-[1] transform-gpu overflow-hidden blur-3xl sm:top-[-20rem]'
      >
        <div
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
          className='relative right-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]'
        />
      </div>
      <div className='fixed inset-0 -left-20 h-screen'>
        <Image
          src='/images/background-effect1.svg'
          alt='background'
          layout='fill'
          objectFit='cover'
          objectPosition='center'
          quality={100}
          className='z-[-1] opacity-30'
        />
      </div>
    </>
  )
}

interface WelcomeHeaderProps {
  user: User | null
  userLoading: boolean
}
function WelcomeHeader({ user, userLoading }: WelcomeHeaderProps) {
  if (userLoading) {
    return (
      <div className='mt-8 w-full text-center md:mt-16'>
        <Skeleton className='mx-auto mb-4 h-12 w-56' />
        <Skeleton className='mx-auto mb-4 h-12 w-80' />
        <Skeleton className='mx-auto h-4 w-96' />
      </div>
    )
  }

  return (
    <CommonHero
      className='mb-8 pt-8 text-center md:pt-10'
      title={`Welcome \n ${user?.full_name || "User"}!`}
      description='Manage your websites or create new ones below.'
    />
  )
}

function QuickStatsLoading() {
  return (
    <div className='mb-8 grid grid-cols-1 gap-6 md:grid-cols-3'>
      <Skeleton className='h-24 w-full' />
      <Skeleton className='h-24 w-full' />
      <Skeleton className='h-24 w-full' />
    </div>
  )
}

function WebsitesLoading() {
  return (
    <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
      {[...Array(6)].map((_, i) => (
        <Skeleton key={i} className='h-36 w-full' />
      ))}
    </div>
  )
}

interface DeleteWebsiteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  website: Website | null
  onConfirm: () => void
  isDeleting: boolean
}

function DeleteWebsiteDialog({
  open,
  onOpenChange,
  website,
  onConfirm,
  isDeleting,
}: DeleteWebsiteDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <h2 className='text-lg font-semibold'>Confirm Deletion</h2>
        </DialogHeader>
        <p>
          Are you sure you want to delete your website: &apos;{website?.name}
          &apos;?
        </p>
        <DialogFooter>
          <Button variant='secondary' onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            variant='destructive'
            onClick={onConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

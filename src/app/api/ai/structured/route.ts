import { google } from "@ai-sdk/google"
import { generateObject } from "ai"
import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

// Define the input schema
const inputSchema = z.object({
  text: z.string().min(1, "Text is required"),
  websiteId: z.string().min(1, "Website ID is required"),
})

// Define shared schemas
const socialLinkSchema = z.object({
  platform: z.string(),
  url: z.string(), // removed .url()
  icon: z.string().optional(), // removed .url()
})

const addressSchema = z.object({
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  zip_code: z.string().optional(),
})

// Define the complete output schema
const outputSchema = z.object({
  hero: z.object({
    website: z.string(),
    title: z.string(),
    sub_title: z.string(),
    description: z.string(),
    main_image: z.string(), // removed .url()
    background_images: z.array(z.string()), // removed .url()
    cta_button_primary: z.object({
      text: z.string(),
      url: z.string(), // removed .url()
    }),
    cta_button_secondary: z
      .object({
        text: z.string(),
        url: z.string(), // removed .url()
      })
      .optional(),
  }),
  about: z.object({
    website: z.string(),
    first_name: z.string(),
    middle_name: z.string().optional(),
    last_name: z.string(),
    title: z.string(),
    bio: z.string(),
    quote: z.string().optional(),
    years_of_experience: z.number().optional(),
    address: addressSchema.optional(),
    total_projects: z.number().optional(),
    total_clients: z.number().optional(),
    phone_number: z.string().optional(),
    contact_email: z.string(), // removed .email()
    social_links: z.array(socialLinkSchema),
    avatar: z.string().optional(), // removed .url()
  }),
  portfolio: z.array(
    z.object({
      website: z.string(),
      title: z.string(),
      description: z.string(),
      images: z.array(z.string()), // removed .url()
      external_links: z.object({
        live_url: z.string(), // removed .url()
        repository_url: z.string(), // removed .url()
      }),
      tags: z.array(z.string()),
      categories: z.array(z.string()),
    }),
  ),
  timeline: z.array(
    z.object({
      website: z.string(),
      title: z.string(),
      description: z.string(),
      bullet_points: z.array(z.string()),
      type: z.enum(["experience", "education", "achievement"]),
      institution: z.string(),
      location: z.string(),
      start_date: z.string(),
      end_date: z.string().optional(),
      is_ongoing: z.boolean(),
      icon: z.string().optional(), // removed .url()
      tags: z.array(z.string()),
    }),
  ),
  reviews: z.array(
    z.object({
      website: z.string(),
      client_name: z.string(),
      client_image: z.string(), // removed .url()
      client_designation: z.string(),
      feedback: z.string(),
      rating: z.number().min(1).max(5).optional(),
      company: z.string(),
    }),
  ),
  service_skills: z.array(
    z.object({
      website: z.string(),
      title: z.string(),
      description: z.string().optional(),
      type: z.enum(["service", "skill"]),
      icon: z.string().optional(), // removed .url()
      percentage: z.number().min(0).max(100).optional(),
      price: z.number().min(0).optional(),
    }),
  ),
})

export async function POST(req: NextRequest) {
  try {
    const json = await req.json()
    const { text, websiteId } = inputSchema.parse(json)

    const result = await generateObject({
      model: google("models/gemini-2.0-flash-exp"),
      schema: outputSchema,
      prompt: `Extract or generate comprehensive portfolio website data from the provided text.
      Use the websiteId "${websiteId}" for all website fields.

      Guidelines:
      - If specific information is not available, generate realistic placeholder data
      - For images, use placeholder URLs from Unsplash or similar services
      - Keep descriptions professional and concise
      - Generate at least:
        * 3-5 portfolio projects
        * 4-6 timeline entries (mix of experience, education, and achievements)
        * 2-3 client reviews
        * 5-8 skills/services
      - Ensure all URLs are valid HTTPS URLs
      - Social links should include common platforms (LinkedIn, GitHub, Twitter)
      - Timeline entries should have realistic dates and progression

      Input text: ${text}`,
      schemaDescription: "Comprehensive structure for portfolio website data",
      mode: "json",
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error generating structured data:", error)
    return NextResponse.json(
      {
        error: "Failed to generate structured data",
        details: error instanceof Error ? error.message : undefined,
      },
      { status: 500 },
    )
  }
}

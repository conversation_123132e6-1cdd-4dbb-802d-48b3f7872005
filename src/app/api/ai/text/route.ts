import { google } from "@ai-sdk/google"
import { mistral } from "@ai-sdk/mistral"
import { openai } from "@ai-sdk/openai"
import { generateText } from "ai"
import { z } from "zod"

// Input validation schema
const inputSchema = z.object({
  text: z.string().min(1, "Text is required"),
  instruction: z.string().min(1, "Instruction is required"),
  provider: z.enum(["openai", "google", "mistral"]).default("mistral"),
})

export async function POST(req: Request) {
  try {
    const json = await req.json()
    const { text, instruction, provider } = inputSchema.parse(json)

    const modelMap: Record<string, any> = {
      openai: openai("o3-mini"),
      google: google("models/gemini-2.0-flash-exp"),
      mistral: mistral("mistral-large-latest"),
    }

    const model = modelMap[provider]
    const prompt = `You are a helpful assistant. ${instruction}\n\n${text}`

    const response = await generateText({ model, prompt })

    return new Response(response.text, {
      headers: { "Content-Type": "text/plain" },
    })
  } catch (error) {
    console.error("Error generating text:", error)
    return new Response(JSON.stringify({ error: "Failed to generate text" }), {
      status: 500,
    })
  }
}

// app/api/github/route.ts
import { NextResponse } from "next/server"

import { GitHubData, GitHubRepository, GitHubUser } from "@/types/github"

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const username = searchParams.get("username")

  if (!username) {
    return NextResponse.json(
      { message: "Username is required" },
      { status: 400 },
    )
  }

  const GITHUB_PAT = process.env.GITHUB_PAT
  const headers = {
    Authorization: `Bearer ${GITHUB_PAT}`,
    Accept: "application/vnd.github.v3+json",
  }

  try {
    // Fetch user profile data
    const userResponse = await fetch(
      `https://api.github.com/users/${username}`,
      { headers },
    )
    const userData: GitHubUser = await userResponse.json()

    // Fetch all public repositories (handling pagination)
    let reposData: GitHubRepository[] = []
    let page = 1
    let hasMoreRepos = true

    while (hasMoreRepos) {
      const reposResponse = await fetch(
        `https://api.github.com/users/${username}/repos?per_page=100&page=${page}`,
        { headers },
      )

      if (!reposResponse.ok) {
        throw new Error("Failed to fetch repositories")
      }

      const reposPage: GitHubRepository[] = await reposResponse.json()
      reposData = [...reposData, ...reposPage]

      // Check if there are more repositories to fetch
      if (reposPage.length < 100) {
        hasMoreRepos = false // No more repositories to fetch
      } else {
        page++ // Move to the next page
      }
    }

    // Combine all data into a single response
    const responseData: GitHubData = {
      user: userData,
      repositories: reposData,
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error("Error fetching GitHub data:", error)
    return NextResponse.json(
      {
        message: "Failed to fetch GitHub data",
        error: (error as Error).message,
      },
      { status: 500 },
    )
  }
}

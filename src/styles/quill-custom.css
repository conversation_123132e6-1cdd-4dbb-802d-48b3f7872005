/*
.quill {
  border-radius: 0.375rem;
}

.ql-toolbar.ql-snow {
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
  border-color: hsl(var(--input));
  background-color: hsl(var(--muted));
  padding: 0.75rem;
}

.ql-container.ql-snow {
  border-bottom-left-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  border-color: hsl(var(--input));
  background-color: hsl(var(--background));
  min-height: 250px;
}

.ql-editor {
  min-height: 250px;
  font-size: 1rem;
  line-height: 1.5;
}

.ql-editor p {
  margin-bottom: 1rem;
}

.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.ql-editor h1 {
  font-size: 1.875rem;
}

.ql-editor h2 {
  font-size: 1.5rem;
}

.ql-editor h3 {
  font-size: 1.25rem;
}

.ql-editor blockquote {
  border-left: 4px solid hsl(var(--muted));
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
}

.ql-editor pre.ql-syntax {
  background-color: hsl(var(--muted));
  border-radius: 0.375rem;
  padding: 1rem;
  overflow-x: auto;
}

.ql-snow .ql-tooltip {
  border-radius: 0.375rem;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: hsl(var(--border));
}


.dark .ql-toolbar.ql-snow {
  background-color: hsl(var(--muted));
}

.dark .ql-container.ql-snow {
  background-color: hsl(var(--background));
}

.dark .ql-snow .ql-stroke {
  stroke: hsl(var(--foreground));
}

.dark .ql-snow .ql-fill {
  fill: hsl(var(--foreground));
}

.dark .ql-snow .ql-picker {
  color: hsl(var(--foreground));
}

.dark .ql-snow .ql-picker-options {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
} */

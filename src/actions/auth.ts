import { toast } from "sonner"

import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

//register using fetch with no cache
export const registerUser = async (data: any): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })

    return await response.json()
  } catch (error) {
    console.error(error)
    return error
  }
}

export const loginUser = async (data: any): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })
    const reponseData = await response.json()
    localStorage.setItem("token", reponseData.data)
    return reponseData
    // return await response.json();
  } catch (error) {
    console.error(error)
    return error
  }
}

export const logoutUser = async (): Promise<any> => {
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/auth/logout`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    })
    if (data.success) {
      localStorage.removeItem("token")
      toast.success("Logged out successfully")
      location.reload()
    }
    return data
  } catch (error) {
    console.error(error)
    return error
  }
}

export const verifyEmail = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/verify-email/${token}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })

    return await response.json()
  } catch (error) {
    console.error(error)
    return error
  }
}

export const forgotPassword = async (data: any): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/forgot-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })

    return await response.json()
  } catch (error) {
    console.error(error)
    return error
  }
}

export const resetPassword = async (data: any): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/reset-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })

    return await response.json()
  } catch (error) {
    console.error(error)
    return error
  }
}

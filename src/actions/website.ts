import { toast } from "sonner"

import { CreateWebsiteFormData } from "@/types/website-creation"
import { generatePortfolioData } from "@/utils/complete-ai-data"
import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

// This file contains actions related to websites
export async function checkWebsiteName(name: string) {
  const data = await fetchWithAuth(`${API_BASE_URL}/websites/check-name`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ name }),
  })

  if (!data.success) {
    throw new Error("Failed to create website")
  }

  return data
}

export async function fetchGitHubData(username: string) {
  try {
    // Call the GitHub API route
    const response = await fetch(`/api/github?username=${username}`)
    const data = await response.json()

    if (response.ok) {
      // Log the fetched data for debugging
      console.log("GitHub Data Fetched:", data)

      // Return the fetched data
      console.log("data", data)
      return data
    } else {
      // Handle API errors
      console.error(
        "Failed to fetch GitHub data:",
        data.message || "Unknown error",
      )
      throw new Error(data.message || "Failed to fetch GitHub data")
    }
  } catch (error) {
    // Handle network or other errors
    console.error("Error fetching GitHub data:", error)
    throw new Error("An error occurred while fetching GitHub data")
  }
}

// This file contains actions related to websites
export async function createWebsite(data: CreateWebsiteFormData) {
  try {
    const token = localStorage.getItem("token")

    const response = await fetch(`${API_BASE_URL}/websites`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        ...data,
        portfolio_data: data.pdfData,
      }),
    })

    const responseData = await response.json()

    console.log(response)
    if (!response.ok) {
      throw new Error(responseData.message || "Failed to create website ")
    }

    console.log("responseData", responseData)

    // if (data.pdfData && data.pdfData !== "") {
    //   await buildWebsiteWithResumeData(data.pdfData, responseData.data._id);
    //   //call the build website function
    // }

    return responseData
  } catch (error) {
    console.error("Failed to create website", error)
    throw new Error(
      error instanceof Error
        ? error.message
        : "An error occurred while creating the website",
    )
  }
}

export const buildWebsiteWithResumeData = async (
  websiteId: string,
  resumeData: string | null,
) => {
  if (!websiteId) {
    throw new Error("Invalid website id")
  }
  try {
    // const ai_generated_portfolio_data = await generateDataFromResumeorCVText(
    //   resumeData,
    //   websiteId,
    // )

    let ai_generated_portfolio_data

    if (resumeData) {
      // Call the new structured data API endpoint
      const response = await fetch("/api/ai/structured", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          text: resumeData,
          websiteId,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to generate structured data")
      }

      ai_generated_portfolio_data = await response.json()
      console.log("ai_generated_portfolio_data", ai_generated_portfolio_data)
    }
    console.log("ai_generated_portfolio_data", ai_generated_portfolio_data)

    // Generate portfolio data with either AI-generated data or sample data
    const enhanced_data = generatePortfolioData(
      websiteId,
      ai_generated_portfolio_data?.object || {},
    )

    // const enhanced_data = enhanceStructuredData(
    //   ai_generated_portfolio_data.data,
    //   websiteId,
    // )

    console.log("enhanced_data", enhanced_data)

    const data = await fetchWithAuth(`${API_BASE_URL}/websites/build`, {
      method: "POST",
      body: JSON.stringify({
        websiteId,
        portfolio_data: enhanced_data,
      }),
    })
    return data
  } catch (error) {
    console.error("Failed to build website", error)
    throw new Error(
      error instanceof Error
        ? error.message
        : "An error occurred while building the website",
    )
  }
}

export async function getWebsites() {
  try {
    const token = localStorage.getItem("token")
    const response = await fetch(`${API_BASE_URL}/websites/user`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    const data = await response.json()

    if (data.success) {
      return data.data
    } else {
      return []
    }
  } catch (error) {
    console.error("Failed to fetch websites", error)
    return []
  }
}

export async function getWebsiteById(id: string) {
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/websites/${id}`)
    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error("Failed to fetch website", error)
    return null
  }
}

export async function deletewebsite(id: string) {
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/websites/${id}`, {
      method: "DELETE",
    })

    if (data.success) {
      toast.success("Website deleted successfully")
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error("Failed to delete website", error)
    return null
  }
}

export async function publishWebsite(id: string) {
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/websites/publish`, {
      body: JSON.stringify({ websiteId: id }),
      method: "POST",
    })

    if (data.success) {
      toast.success("Website published successfully")
      return data.data
    } else {
      toast.error("Failed to publish website")
      return null
    }
  } catch (error) {
    toast.error("Failed to publish website")
    console.error("Failed to publish website", error)
    return null
  }
}

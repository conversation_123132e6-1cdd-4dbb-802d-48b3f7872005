import { toast } from "sonner"

import { ServiceSkillFormData } from "@/types/service-skill"
import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

// Get all timelines for a specific website
export const getSericeSkillsByWebsiteAndType = async (
  websiteId: string,
  type: string,
) => {
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/service-skills/type/${websiteId}/${type}`,
    )

    if (data.success) {
      return data.data
    } else {
      return []
    }
  } catch (error) {
    console.error(error)
    return []
  }
}

//get service skills by website
export const getServiceSkills = async (websiteId: string) => {
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/service-skills/website/${websiteId}`,
    )

    if (data.success) {
      return data.data
    } else {
      return []
    }
  } catch (error) {
    console.error(error)
    return []
  }
}

//create service skill
export const createServiceSkill = async (
  websiteId: string,
  skillData: ServiceSkillFormData,
) => {
  if (!websiteId) {
    toast.error("Website ID is missing")
  }
  try {
    const response = await fetchWithAuth(
      `${API_BASE_URL}/service-skills/${websiteId}`,
      {
        method: "POST",
        body: JSON.stringify(skillData),
      },
    )

    const data = await response.json()
    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

//update service skill
export const updateServiceSkill = async (skillData: ServiceSkillFormData) => {
  if (!skillData._id) {
    toast.error("Service Skill ID is missing")
  }
  try {
    const response = await fetchWithAuth(
      `${API_BASE_URL}/service-skills/${skillData._id}`,
      {
        method: "PUT",
        body: JSON.stringify(skillData),
      },
    )

    const data = await response.json()
    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

//delete service skill
export const deleteServiceSkill = async (skillId: string) => {
  try {
    const response = await fetchWithAuth(
      `${API_BASE_URL}/service-skills/${skillId}`,
      {
        method: "DELETE",
      },
    )

    const data = await response.json()
    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

//enable disable service skill
export const toggleServiceSkillStatus = async (skillId?: string) => {
  if (!skillId) {
    toast.error("Service Skill ID is missing")
  }
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/service-skills/${skillId}/status`,
      {
        method: "PATCH",
      },
    )

    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

"use server"

import { GoogleGenerativeA<PERSON> } from "@google/generative-ai"
import { z } from "zod"

// Initialize Google Generative AI client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!)

// Define input validation schema
const schema = z.object({
  text: z.string().min(1, "Text is required"),
  instruction: z.string().min(1, "Instruction is required"),
})

// Regex to detect code-like patterns
const CODE_REGEX =
  /\b(const|code|function|class|def|let|var|return|if|else|for|while|do|switch|case|break|import|export|require)\b/

export async function generateText(input: {
  text: string
  instruction: string
}) {
  try {
    // Validate input
    const { text, instruction } = schema.parse(input)

    // Check for code-like patterns
    if (CODE_REGEX.test(text)) {
      return { error: "Code or invalid content detected in the input text." }
    }

    // Initialize Gemini AI model
    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash",
    })

    // Call Gemini API
    const result = await model.generateContent([
      `You are a helpful assistant. ${instruction}`,
      text,
    ])

    // Extract response
    const response = result.response.text()

    return { response }
  } catch (error) {
    console.error("Error calling Gemini API:", error)
    return { error: "Failed to generate text" }
  }
}

export async function generatePdfText(input: {
  text: string
  instruction: string
}) {
  try {
    // Validate input
    const { text, instruction } = schema.parse(input)

    // Initialize Gemini AI model
    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash",
    })

    // Call Gemini API
    const result = await model.generateContent([
      `You are a helpful assistant. ${instruction}`,
      text,
    ])

    // Extract response
    const response = result.response.text()

    return { response }
  } catch (error) {
    console.error("Error calling Gemini API:", error)
    return { error: "Failed to generate refined text" }
  }
}

// export async function generateDataFromResumeorCVText(
//   text: string,
//   websiteId: string
// ) {
//   try {
//     // Define the expected JSON schema

//     const schema = {
//       description: "Structured portfolio data extracted from resumes or CVs",
//       type: SchemaType.OBJECT,
//       properties: {
//         hero: {
//           type: SchemaType.OBJECT,
//           properties: {
//             website: { type: SchemaType.STRING },
//             title: { type: SchemaType.STRING },
//             sub_title: { type: SchemaType.STRING },
//             description: { type: SchemaType.STRING },
//             main_image: { type: SchemaType.STRING },
//             background_images: {
//               type: SchemaType.ARRAY,
//               items: { type: SchemaType.STRING },
//             },
//             cta_button_primary: {
//               type: SchemaType.OBJECT,
//               properties: {
//                 text: { type: SchemaType.STRING },
//                 url: { type: SchemaType.STRING },
//               },
//             },
//             cta_button_secondary: {
//               type: SchemaType.OBJECT,
//               properties: {
//                 text: { type: SchemaType.STRING },
//                 url: { type: SchemaType.STRING },
//               },
//             },
//           },
//           required: ["website", "title", "description"],
//         },
//         about: {
//           type: SchemaType.OBJECT,
//           properties: {
//             website: { type: SchemaType.STRING },
//             first_name: { type: SchemaType.STRING },
//             middle_name: { type: SchemaType.STRING, nullable: true },
//             last_name: { type: SchemaType.STRING },
//             title: { type: SchemaType.STRING },
//             bio: { type: SchemaType.STRING },
//             quote: { type: SchemaType.STRING, nullable: true },
//             years_of_experience: { type: SchemaType.NUMBER },
//             address: {
//               type: SchemaType.OBJECT,
//               properties: {
//                 street: { type: SchemaType.STRING },
//                 city: { type: SchemaType.STRING },
//                 state: { type: SchemaType.STRING },
//                 country: { type: SchemaType.STRING },
//                 zip_code: { type: SchemaType.STRING },
//               },
//             },
//             total_projects: { type: SchemaType.NUMBER },
//             total_clients: { type: SchemaType.NUMBER },
//             phone_number: { type: SchemaType.STRING },
//             contact_email: { type: SchemaType.STRING },
//             social_links: {
//               type: SchemaType.ARRAY,
//               items: {
//                 type: SchemaType.OBJECT,
//                 properties: {
//                   platform: { type: SchemaType.STRING },
//                   url: { type: SchemaType.STRING },
//                 },
//               },
//             },
//           },
//           required: ["website", "first_name", "last_name", "title", "bio"],
//         },
//         portfolio: {
//           type: SchemaType.ARRAY,
//           items: {
//             type: SchemaType.OBJECT,
//             properties: {
//               website: { type: SchemaType.STRING },
//               title: { type: SchemaType.STRING },
//               description: { type: SchemaType.STRING },
//               images: {
//                 type: SchemaType.ARRAY,
//                 items: { type: SchemaType.STRING },
//               },
//               external_links: {
//                 type: SchemaType.OBJECT,
//                 properties: {
//                   live_url: { type: SchemaType.STRING },
//                   repository_url: { type: SchemaType.STRING },
//                 },
//               },
//               tags: {
//                 type: SchemaType.ARRAY,
//                 items: { type: SchemaType.STRING },
//               },
//               categories: {
//                 type: SchemaType.ARRAY,
//                 items: { type: SchemaType.STRING },
//               },
//             },
//           },
//         },
//         timeline: {
//           type: SchemaType.ARRAY,
//           items: {
//             type: SchemaType.OBJECT,
//             properties: {
//               website: { type: SchemaType.STRING },
//               title: { type: SchemaType.STRING },
//               description: { type: SchemaType.STRING },
//               bullet_points: {
//                 type: SchemaType.ARRAY,
//                 items: { type: SchemaType.STRING },
//               },
//               type: { type: SchemaType.STRING },
//               institution: { type: SchemaType.STRING },
//               location: { type: SchemaType.STRING },
//               start_date: { type: SchemaType.STRING },
//               end_date: { type: SchemaType.STRING, nullable: true },
//               is_ongoing: { type: SchemaType.BOOLEAN },
//               icon: { type: SchemaType.STRING },
//               tags: {
//                 type: SchemaType.ARRAY,
//                 items: { type: SchemaType.STRING },
//               },
//             },
//           },
//         },
//         reviews: {
//           type: SchemaType.ARRAY,
//           items: {
//             type: SchemaType.OBJECT,
//             properties: {
//               website: { type: SchemaType.STRING },
//               client_name: { type: SchemaType.STRING },
//               client_image: { type: SchemaType.STRING },
//               client_designation: { type: SchemaType.STRING },
//               feedback: { type: SchemaType.STRING },
//               rating: { type: SchemaType.NUMBER },
//               company: { type: SchemaType.STRING },
//             },
//           },
//         },
//         service_skills: {
//           type: SchemaType.ARRAY,
//           items: {
//             type: SchemaType.OBJECT,
//             properties: {
//               website: { type: SchemaType.STRING },
//               title: { type: SchemaType.STRING },
//               description: { type: SchemaType.STRING },
//               type: { type: SchemaType.STRING },
//               icon: { type: SchemaType.STRING },
//               percentage: { type: SchemaType.NUMBER },
//               price: { type: SchemaType.NUMBER },
//             },
//           },
//         },
//       },
//     };

//     // Initialize Gemini AI model
//     const model = genAI.getGenerativeModel({
//       model: "gemini-1.5-pro",
//       generationConfig: {
//         responseMimeType: "application/json",
//         responseSchema: schema,
//       },
//     });

//     // Prompt for extracting structured portfolio data
//     const prompt = `Extract structured portfolio information from the following resume text:
//     \n\n${text}\n\n
//     Ensure the JSON format follows the predefined schema. The "website" field should always be set to "${websiteId}".
//     - Use real-world professional details.
//     - Use HTTPS image URLs from Unsplash, Pexels, or RandomUser.me.
//     - Do NOT return Markdown or code blocks, only valid JSON.
//     - All fields should be realistic and relevant.`;

//     // Generate structured content
//     const result = await model.generateContent(prompt);
//     // const extractedData = result.response.json();
//     const extractedData = JSON.parse(result.response.text());
//     console.log(result.response.text());

//     console.log("Extracted Data:", extractedData);

//     return { data: extractedData };
//   } catch (error) {
//     console.error("Error generating data from resume or CV:", error);
//     return { error: "Failed to generate data from resume or CV" };
//   }
// }

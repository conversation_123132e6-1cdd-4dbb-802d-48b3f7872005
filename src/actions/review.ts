import { toast } from "sonner"

import { ReviewFormData } from "@/types/review"
import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

// Get all reviews for a specific website
export const getReviewsByWebsite = async (websiteId: string) => {
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/reviews/${websiteId}`)

    if (data.success) {
      return data.data
    } else {
      return []
    }
  } catch (error) {
    console.error(error)
    return []
  }
}

// Create a new review entry
export const createReview = async (reviewData: ReviewFormData) => {
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/reviews/`, {
      method: "POST",
      body: JSON.stringify(reviewData),
    })

    return data
  } catch (error) {
    console.error(error)
    return null
  }
}

// Update an existing review entry
export const updateReview = async (reviewData: ReviewFormData) => {
  if (!reviewData._id) {
    toast.error("Review ID is missing")
    return null
  }
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/reviews/${reviewData._id}`,
      {
        method: "PUT",
        body: JSON.stringify(reviewData),
      },
    )

    return data
  } catch (error) {
    console.error(error)
    return null
  }
}

// Soft delete a review entry
export const deleteReview = async (reviewId: string) => {
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/reviews/${reviewId}`, {
      method: "DELETE",
    })

    return data
  } catch (error) {
    console.error(error)
    return error
  }
}

// Enable or disable a review entry
export const toggleReviewVisibility = async (reviewId?: string) => {
  if (!reviewId) {
    toast.error("Review ID is missing")
    return null
  }
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/reviews/${reviewId}/visibility`,
      {
        method: "PATCH",
      },
    )

    return data
  } catch (error) {
    console.error(error)
    return error
  }
}

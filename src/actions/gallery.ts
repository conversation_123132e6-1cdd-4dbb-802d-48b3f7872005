import { ImageType } from "@/types/image"
import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

export const createGalleryItem = async (data: Partial<ImageType>) => {
  const response = await fetch(`${API_BASE_URL}/gallery`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })

  if (!response.ok) {
    throw new Error("Failed to create gallery item")
  }

  return response.json()
}

export const fetchGalleryItemsWithPagination = async (
  page: number,
  limit: number,
  search: string,
) => {
  const response = await fetch(
    `${API_BASE_URL}/gallery?page=${page}&limit=${limit}&search=${search}`,
  )

  if (!response.ok) {
    throw new Error("Failed to fetch gallery items")
  }

  return response.json()
}

//delete image from gallery
export const deleteImageFromGallery = async (
  imageId: string,
  websiteId: string,
) => {
  const response = await fetchWithAuth(`${API_BASE_URL}/media/${imageId}`, {
    method: "DELETE",
    body: JSON.stringify({ websiteId }),
  })

  if (!response.ok) {
    throw new Error("Failed to delete image")
  }

  return response.json()
}

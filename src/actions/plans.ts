import { API_BASE_URL } from "."

export const getPlans = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/plans`, {
      next: { revalidate: 3600 }, // Revalidate every hour
      // cache: "no-store", // Avoid caching for fresh data
    })
    const data = await response.json()

    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

"use server"

import OpenAI from "openai"
import { z } from "zod"

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// Define input validation schema
const schema = z.object({
  text: z.string().min(1, "Text is required"),
  instruction: z.string().min(1, "Instruction is required"),
  checkRegex: z.boolean().default(true), // Default value for checkRegex
})

// Regex to detect code-like patterns
const CODE_REGEX =
  /\b(const|code|function|class|def|let|var|return|if|else|for|while|do|switch|case|break|import|export|require)\b/

export async function generateText(input: {
  text: string
  instruction: string
  checkRegex?: boolean
}) {
  try {
    // Validate the input
    const { text, instruction, checkRegex } = schema.parse(input)

    // Check if the text contains code-like patterns, if checkRegex is true
    if (checkRegex && CODE_REGEX.test(text)) {
      return { error: "Code or invalid content detected in the input text." }
    }

    // Call the OpenAI API
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo", // Use GPT-3.5 Turbo
      messages: [
        {
          role: "system",
          content: `You are a helpful assistant. ${instruction}`,
        },
        { role: "user", content: text },
      ],
    })

    // Extract the response
    const response = completion.choices[0].message.content

    // Return the response
    return { response }
  } catch (error) {
    console.error("Error calling OpenAI API:", error)
    return { error: "Failed to generate text" }
  }
}

export async function generateDataFromResumeorCVText(
  text: string,
  websiteId: string,
) {
  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `I have provided you the extracted data from the cv or resume and using that data you have to generate the data for the website. Output must follow this JSON format:
          {
            "hero": { 
              "website": "${websiteId}", 
              "title": "e.g., 'I'm John Doe, Full-Stack Developer'", 
              "sub_title": "e.g., 'Building innovative solutions'", 
              "description": "Short bio about the person", 
              "main_image": "Valid HTTPS image URL", 
              "background_images": ["Valid HTTPS image URLs"], 
              "cta_button_primary": { "text": "e.g., 'Hire Me'", "url": "Valid URL" }, 
              "cta_button_secondary": { "text": "e.g., 'Contact Me'", "url": "Valid URL" } 
            },
            "about": { 
              "website": "${websiteId}", 
              "first_name": "John", 
              "middle_name": "Optional", 
              "last_name": "Doe", 
              "title": "e.g., 'Software Engineer'", 
              "bio": "Brief professional summary minimum 200 words", 
              "quote": "e.g., 'Code is poetry.'", 
              "years_of_experience": 5, 
              "address": { "street": "123 Main St", "city": "New York", "state": "NY", "country": "USA", "zip_code": "10001" }, 
              "total_projects": 50, 
              "total_clients": 20, 
              "phone_number": "****** 567 8901", 
              "contact_email": "<EMAIL>", 
              "social_links": [{ "platform": "LinkedIn", "url": "Valid URL", "icon": "Valid HTTPS image URL" }] 
            },
            "portfolio": [{ 
              "website": "${websiteId}", 
              "title": "e.g., 'Personal Portfolio'", 
              "description": "Short project description", 
              "images": ["Valid HTTPS image URLs"], 
              "external_links": { "live_url": "Valid URL", "repository_url": "Valid URL" }, 
              "tags": ["e.g., 'web development', 'React'"], 
              "categories": ["e.g., 'Web', 'Mobile'"] 
            }],
            "timeline": [{ 
              "website": "${websiteId}", 
              "title": "e.g., 'Software Engineer at Google'", 
              "description": "Work/education summary", 
              "bullet_points": ["Key achievement 1", "Key achievement 2"], 
              "type": "experience | education | achievement", 
              "institution": "e.g., 'Google' or 'Harvard'", 
              "location": "e.g., 'California, USA'", 
              "start_date": "YYYY-MM-DD", 
              "end_date": "YYYY-MM-DD (or null if ongoing)", 
              "is_ongoing": true, 
              "icon": "Valid HTTPS image URL", 
              "tags": ["e.g., 'technology', 'leadership'"] 
            }],
            "reviews": [{ 
              "website": "${websiteId}", 
              "client_name": "e.g., 'Jane Smith'", 
              "client_image": "Valid HTTPS image URL", 
              "client_designation": "e.g., 'CEO at ABC Corp.'", 
              "feedback": "Client review text", 
              "rating": 5, 
              "company": "ABC Corp." 
            }],
            "service_skills": [{ 
              "website": "${websiteId}", 
              "title": "e.g., 'JavaScript'", 
              "description": "Brief service description, not needed for skill", 
              "type": "service | skill", 
              "icon": "Valid HTTPS image URL", 
              "percentage": 90, 
              "price": 100 
            }]
          }
          Rules:
          - If data is missing, provide sample but realistic data.
          - Use HTTPS image URLs from Unsplash, Pexels, or RandomUser.me.
          - Ensure URLs are valid and publicly accessible.
          - Keep descriptions concise.
          `,
        },
        { role: "user", content: text },
      ],
      response_format: { type: "json_object" },
    })

    const response = completion.choices[0]?.message?.content
    return response
      ? { data: JSON.parse(response) }
      : { error: "Failed to extract data." }
  } catch (error) {
    console.error("Error generating data:", error)
    return { error: "Failed to generate data from resume or CV" }
  }
}

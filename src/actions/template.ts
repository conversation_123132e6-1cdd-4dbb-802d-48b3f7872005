import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

export const getTemplates = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/templates`)
    const data = await response.json()

    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

export const applyTemplate = async (websiteId: string, templateId: string) => {
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/websites/change-template`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ templateId, websiteId }),
      },
    )
    return data
  } catch (error) {
    console.error(error)
    return null
  }
}

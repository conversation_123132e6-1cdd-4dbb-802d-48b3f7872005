import { toast } from "sonner"

import type { Blog, BlogFormData } from "@/types/blog"
import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

/**
 * Get all blog posts for a specific website
 * @param websiteId - The ID of the website
 * @returns A list of blog posts
 */
export const getAllBlogsByWebsiteId = async (
  websiteId: string,
): Promise<Blog[]> => {
  try {
    const response = await fetchWithAuth(
      `${API_BASE_URL}/blogs/website/${websiteId}`,
    )
    if (response.success) {
      return response.data
    } else {
      throw new Error(response.message || "Failed to fetch blogs")
    }
  } catch (error) {
    console.error(error)
    toast.error("An error occurred while fetching blogs")
    throw error // Re-throw the error to be handled by React Query
  }
}

/**
 * Create a new blog post for a specific website
 * @param websiteId - The ID of the website
 * @param blogData - The blog post data
 * @returns The created blog post
 */
export const createBlog = async (
  websiteId: string,
  blogData: BlogFormData,
): Promise<Blog> => {
  try {
    const response = await fetchWithAuth(`${API_BASE_URL}/blogs/${websiteId}`, {
      method: "POST",
      body: JSON.stringify(blogData),
    })

    console.log(response)

    if (response.success) {
      return response.data
    } else {
      throw new Error(response.message || "Failed to create blog post")
    }
  } catch (error) {
    console.error(error)
    toast.error("An error occurred while creating the blog post")
    throw error // Re-throw the error to be handled by React Query
  }
}

/**
 * Update a blog post by ID
 * @param blogId - The ID of the blog post
 * @param blogData - The updated blog post data
 * @returns The updated blog post
 */
export const updateBlog = async (
  blogId: string,
  blogData: BlogFormData,
): Promise<Blog> => {
  try {
    const response = await fetchWithAuth(`${API_BASE_URL}/blogs/${blogId}`, {
      method: "PUT",
      body: JSON.stringify(blogData),
    })

    if (response.success) {
      return response.data
    } else {
      throw new Error(response.message || "Failed to update blog post")
    }
  } catch (error) {
    console.error(error)
    // toast.error("An error occurred while updating the blog post");
    throw error // Re-throw the error to be handled by React Query
  }
}

/**
 * Soft delete a blog post by ID
 * @param blogId - The ID of the blog post
 * @returns void
 */
export const softDeleteBlog = async (blogId: string): Promise<void> => {
  try {
    const response = await fetchWithAuth(`${API_BASE_URL}/blogs/${blogId}`, {
      method: "DELETE",
    })

    if (!response.success) {
      throw new Error(response.message || "Failed to soft delete blog post")
    }
  } catch (error) {
    console.error(error)
    toast.error("An error occurred while soft deleting the blog post")
    throw error // Re-throw the error to be handled by React Query
  }
}

/**
 * Permanently delete a blog post by ID (admin only)
 * @param blogId - The ID of the blog post
 * @returns void
 */
export const deleteBlog = async (blogId: string): Promise<void> => {
  try {
    const response = await fetchWithAuth(
      `${API_BASE_URL}/blogs/admin/${blogId}`,
      {
        method: "DELETE",
      },
    )

    if (!response.success) {
      throw new Error(response.message || "Failed to delete blog post")
    }
  } catch (error) {
    console.error(error)
    toast.error("An error occurred while deleting the blog post")
    throw error // Re-throw the error to be handled by React Query
  }
}

/**
 * Toggle the publish status of a blog post
 * @param blogId - The ID of the blog post
 * @returns The updated blog post
 */
export const toggleBlogStatus = async (blogId: string): Promise<Blog> => {
  try {
    const response = await fetchWithAuth(
      `${API_BASE_URL}/blogs/${blogId}/status`,
      {
        method: "PATCH",
      },
    )

    if (response.success) {
      return response.data
    } else {
      throw new Error(response.message || "Failed to toggle blog status")
    }
  } catch (error) {
    console.error(error)
    toast.error("An error occurred while toggling blog status")
    throw error // Re-throw the error to be handled by React Query
  }
}

/**
 * Get a blog post by ID
 * @param blogId - The ID of the blog post
 * @returns The blog post
 */
export const getBlogById = async (blogId: string): Promise<Blog> => {
  try {
    const response = await fetchWithAuth(`${API_BASE_URL}/blogs/${blogId}`)
    if (response.success) {
      return response.data
    } else {
      throw new Error(response.message || "Failed to fetch blog post")
    }
  } catch (error) {
    console.error(error)
    toast.error("An error occurred while fetching the blog post")
    throw error // Re-throw the error to be handled by React Query
  }
}

/**
 * Get all blog posts with optional filters, pagination, and sorting
 * @param queryParams - Query parameters for filtering, pagination, and sorting
 * @returns A paginated list of blog posts
 */
export const getAllBlogs = async (queryParams: {
  page?: number
  limit?: number
  search?: string
  category?: string
  tag?: string
  sortBy?: string
}): Promise<{ total: number; page: number; limit: number; blogs: Blog[] }> => {
  try {
    const queryString = new URLSearchParams(
      queryParams as Record<string, string>,
    ).toString()
    const response = await fetchWithAuth(
      `${API_BASE_URL}/blogs/all?${queryString}`,
    )

    if (response.success) {
      return response.data
    } else {
      throw new Error(response.message || "Failed to fetch blogs")
    }
  } catch (error) {
    console.error(error)
    toast.error("An error occurred while fetching blogs")
    throw error // Re-throw the error to be handled by React Query
  }
}

import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

export const getHeroByWebsite = async (websiteId: string) => {
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/hero/website/${websiteId}`,
    )

    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

export const createOrUpdateHero = async (websiteId: string, aboutData: any) => {
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/hero`, {
      method: "POST",
      body: JSON.stringify({ ...aboutData, website: websiteId }),
    })

    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

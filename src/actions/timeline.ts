import { toast } from "sonner"

import { TimelineFormData } from "@/types/timeline"
import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

// Get all timelines for a specific website
export const getTimelinesByWebsiteAndType = async (
  websiteId: string,
  type: string,
) => {
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/timelines/type/${websiteId}/${type}`,
    )

    if (data.success) {
      return data.data
    } else {
      return []
    }
  } catch (error) {
    console.error(error)
    return []
  }
}

// Get all timelines for a specific website
export const getTimelinesByWebsite = async (websiteId: string) => {
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/timelines/${websiteId}`)

    if (data.success) {
      return data.data
    } else {
      return []
    }
  } catch (error) {
    console.error(error)
    return []
  }
}

// Get a timeline by slug
export const getTimelineById = async (id: string) => {
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/timelines/${id}`)

    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

// Create a new timeline entry
export const createTimeline = async (timelineData: TimelineFormData) => {
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/timelines/`, {
      method: "POST",
      body: JSON.stringify(timelineData),
    })

    return data

    // const data = await response.json();
    // if (data.success) {
    //   return data.data;
    // } else {
    //   return null;
    // }
  } catch (error) {
    console.error(error)
    return null
  }
}

// Update an existing timeline entry
export const updateTimeline = async (timelineData: TimelineFormData) => {
  if (!timelineData?._id) {
    toast.error("Timeline ID is missing")
    return null
  }
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/timelines/${timelineData?._id}`,
      {
        method: "PUT",
        body: JSON.stringify(timelineData),
      },
    )

    return data
    // if (data.success) {

    //   return data.data;
    // } else {
    //   return null;
    // }
  } catch (error) {
    console.error(error)
    return null
  }
}

// Soft delete a timeline entry
export const deleteTimeline = async (timelineId: string) => {
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/timelines/${timelineId}`,
      {
        method: "DELETE",
      },
    )

    return data
  } catch (error) {
    console.error(error)
    return error
  }
}

// Enable or disable a timeline entry
export const toggleTimelineStatus = async (timelineId?: string) => {
  if (!timelineId) {
    toast.error("Timeline ID is missing")
    return null
  }
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/timelines/${timelineId}/status`,
      {
        method: "PATCH",
      },
    )

    return data
  } catch (error) {
    console.error(error)
    return error
  }
}

import { toast } from "sonner"

import { PortfolioFormData } from "@/types/portfolio"
import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

//get portfolios(projects) by website
export const getPortfolio = async (websiteId: string) => {
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/portfolio/website/${websiteId}`,
    )

    if (data.success) {
      return data.data
    } else {
      return []
    }
  } catch (error) {
    console.error(error)
    return []
  }
}

//create portfolio(project)
export const createPortfolio = async (
  websiteId: string,
  portfolioData: PortfolioFormData,
) => {
  if (!websiteId) {
    toast.error("Website ID is missing")
  }
  try {
    const response = await fetchWithAuth(
      `${API_BASE_URL}/portfolio/${websiteId}`,
      {
        method: "POST",
        body: JSON.stringify(portfolioData),
      },
    )

    const data = await response.json()
    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}
//create portfolio(project)
export const updatedPortfolio = async (portfolioData: PortfolioFormData) => {
  if (!portfolioData._id) {
    toast.error("Website ID or Project ID is missing")
  }
  try {
    const response = await fetchWithAuth(
      `${API_BASE_URL}/portfolio/${portfolioData._id}`,
      {
        method: "PUT",
        body: JSON.stringify(portfolioData),
      },
    )

    const data = await response.json()
    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

//delete portfolio(project)
export const deletePortfolio = async (projectId: string) => {
  try {
    const response = await fetchWithAuth(
      `${API_BASE_URL}/portfolio/${projectId}`,
      {
        method: "DELETE",
      },
    )

    const data = await response.json()
    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

//enable disable portfolio(project) item
export const togglePortfolioStatus = async (projectId?: string) => {
  if (!projectId) {
    toast.error("Project ID is missing")
  }
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/portfolio/${projectId}/status`,
      {
        method: "PATCH",
      },
    )

    // const data = await response.json();
    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

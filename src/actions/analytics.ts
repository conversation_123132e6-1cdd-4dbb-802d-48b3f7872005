import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

export const getAnalyticsByWebsite = async (
  websiteId: string,
  model: "Session" | "Pageview" | "Event" | "WebsiteAnalytics",
  page: number = 1,
  limit: number = 10,
) => {
  try {
    let url = `${API_BASE_URL}/analytics/${model}/${websiteId}`

    if (model !== "WebsiteAnalytics") {
      url += `?page=${page}&limit=${limit}`
    }

    const data = await fetchWithAuth(url)

    if (data.success) {
      return data.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

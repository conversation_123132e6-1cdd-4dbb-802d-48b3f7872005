import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

export const getLoggedInUser = async (): Promise<any> => {
  console.log("getLoggedInUser")
  if (typeof window === "undefined" || !localStorage.getItem("token")) {
    return null
  }
  try {
    const data = await fetchWithAuth(`${API_BASE_URL}/user/me`)
    // const data = await response.json();
    if (data.success) {
      const user = data?.data
      return user
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

// upload user media images(files)
export const uploadUserMedia = async (
  files: any,
  websiteId: string,
): Promise<any> => {
  try {
    // const formData = new FormData();
    // formData.append("files", files[0]);
    const data = await fetchWithAuth(
      `${API_BASE_URL}/media/upload/${websiteId}`,
      {
        method: "POST",
        body: JSON.stringify({ files }),
      },
    )

    if (data.success) {
      return data?.data
    } else {
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

// fetch users media image
export const fetchUserMedia = async ({
  websiteId,
  search = "",
  // filter = "all",
  page = 1,
}: {
  websiteId: string
  search?: string
  filter?: string
  page?: number
}): Promise<any> => {
  try {
    // Construct query parameters
    const query = new URLSearchParams({
      search,
      // filter,
      page: page.toString(),
    }).toString()

    // Make the API call with authentication
    const data = await fetchWithAuth(
      `${API_BASE_URL}/media/website/${websiteId}?${query}`,
    )

    if (data.success) {
      return data.data
    } else {
      return null
    }
    // Parse the response
  } catch (error) {
    console.error("Error in fetchUserMedia:", error)
    return null
  }
}

import { toast } from "sonner"

import { Coupon } from "@/types/checkout"
import { Plan } from "@/types/plans"
import { fetchWithAuth } from "@/utils/fetchWithAuth"

import { API_BASE_URL } from "."

export const handleZeroPayment = async (
  websiteId: string,
  selectedPlan: Plan,
  coupon: Coupon,
) => {
  try {
    const data = await fetchWithAuth(
      `${API_BASE_URL}/payments/complete-checkout-via-coupon`,
      {
        method: "POST",
        body: JSON.stringify({
          planId: selectedPlan._id,
          websiteId,
          couponCode: coupon.code,
        }),
      },
    )

    // return data;
    if (data.success) {
      toast.success("Upgrade successful!")
      window.location.href = `/dashboard/${websiteId}`
    } else {
      toast.error("Failed to process the upgrade. Please try again.")
      toast.error(data.message)
    }
  } catch (error) {
    console.error("Upgrade failed:", error)
  }
}
